using UnityEngine;
using System.Collections.Generic;

public class VfxShaderGraphController : MonoBehaviour
{
    [Head<PERSON>("Shader Graph Settings")]
    public Material targetMaterial;
    public Renderer targetRenderer;
    
    [Header("Animation Settings")]
    public bool animateProperties = true;
    public float animationSpeed = 1f;
    
    private Dictionary<string, float> floatProperties = new Dictionary<string, float>();
    private Dictionary<string, Vector4> vectorProperties = new Dictionary<string, Vector4>();
    private Dictionary<string, Color> colorProperties = new Dictionary<string, Color>();
    private Dictionary<string, Texture> textureProperties = new Dictionary<string, Texture>();
    
    void Start()
    {
        InitializeShaderController();
    }
    
    void InitializeShaderController()
    {
        if (targetRenderer == null)
            targetRenderer = GetComponent<Renderer>();
            
        if (targetMaterial == null && targetRenderer != null)
            targetMaterial = targetRenderer.material;
            
        CacheShaderProperties();
        Debug.Log("VFX Shader Graph Controller initialized!");
    }
    
    void CacheShaderProperties()
    {
        if (targetMaterial == null) return;
        
        // Cache common shader properties
        CacheFloatProperty("_Metallic", 0f);
        CacheFloatProperty("_Smoothness", 0.5f);
        CacheFloatProperty("_Alpha", 1f);
        CacheFloatProperty("_Emission", 1f);
        
        CacheColorProperty("_BaseColor", Color.white);
        CacheColorProperty("_EmissionColor", Color.black);
        
        CacheVectorProperty("_Tiling", Vector4.one);
        CacheVectorProperty("_Offset", Vector4.zero);
    }
    
    void Update()
    {
        if (animateProperties)
        {
            AnimateShaderProperties();
        }
    }
    
    void AnimateShaderProperties()
    {
        float time = Time.time * animationSpeed;
        
        // Example animations
        SetFloatProperty("_Emission", Mathf.Sin(time) * 0.5f + 0.5f);
        
        Color emissionColor = Color.HSVToRGB((time * 0.1f) % 1f, 1f, 1f);
        SetColorProperty("_EmissionColor", emissionColor);
        
        Vector4 offset = new Vector4(Mathf.Sin(time * 0.5f) * 0.1f, Mathf.Cos(time * 0.3f) * 0.1f, 0, 0);
        SetVectorProperty("_Offset", offset);
    }
    
    void CacheFloatProperty(string propertyName, float defaultValue)
    {
        if (targetMaterial != null && targetMaterial.HasProperty(propertyName))
        {
            floatProperties[propertyName] = targetMaterial.GetFloat(propertyName);
        }
        else
        {
            floatProperties[propertyName] = defaultValue;
        }
    }
    
    void CacheColorProperty(string propertyName, Color defaultValue)
    {
        if (targetMaterial != null && targetMaterial.HasProperty(propertyName))
        {
            colorProperties[propertyName] = targetMaterial.GetColor(propertyName);
        }
        else
        {
            colorProperties[propertyName] = defaultValue;
        }
    }
    
    void CacheVectorProperty(string propertyName, Vector4 defaultValue)
    {
        if (targetMaterial != null && targetMaterial.HasProperty(propertyName))
        {
            vectorProperties[propertyName] = targetMaterial.GetVector(propertyName);
        }
        else
        {
            vectorProperties[propertyName] = defaultValue;
        }
    }
    
    public void SetFloatProperty(string propertyName, float value)
    {
        floatProperties[propertyName] = value;
        
        if (targetMaterial != null && targetMaterial.HasProperty(propertyName))
        {
            targetMaterial.SetFloat(propertyName, value);
        }
    }
    
    public void SetColorProperty(string propertyName, Color value)
    {
        colorProperties[propertyName] = value;
        
        if (targetMaterial != null && targetMaterial.HasProperty(propertyName))
        {
            targetMaterial.SetColor(propertyName, value);
        }
    }
    
    public void SetVectorProperty(string propertyName, Vector4 value)
    {
        vectorProperties[propertyName] = value;
        
        if (targetMaterial != null && targetMaterial.HasProperty(propertyName))
        {
            targetMaterial.SetVector(propertyName, value);
        }
    }
    
    public void SetTextureProperty(string propertyName, Texture value)
    {
        textureProperties[propertyName] = value;
        
        if (targetMaterial != null && targetMaterial.HasProperty(propertyName))
        {
            targetMaterial.SetTexture(propertyName, value);
        }
    }
    
    public float GetFloatProperty(string propertyName)
    {
        return floatProperties.ContainsKey(propertyName) ? floatProperties[propertyName] : 0f;
    }
    
    public Color GetColorProperty(string propertyName)
    {
        return colorProperties.ContainsKey(propertyName) ? colorProperties[propertyName] : Color.white;
    }
    
    public Vector4 GetVectorProperty(string propertyName)
    {
        return vectorProperties.ContainsKey(propertyName) ? vectorProperties[propertyName] : Vector4.zero;
    }
    
    public void ResetToDefaults()
    {
        CacheShaderProperties();
        Debug.Log("Shader properties reset to defaults!");
    }
}