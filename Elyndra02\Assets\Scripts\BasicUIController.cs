using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using System.Collections.Generic;

public class BasicUIController : MonoBehaviour
{
    public Canvas mainCanvas;
    public EventSystem eventSystem;
    
    private Dictionary<string, GameObject> elements = new Dictionary<string, GameObject>();
    
    void Start()
    {
        Init();
    }
    
    void Init()
    {
        Debug.Log("Basic UI Controller initialized!");
        
        if (mainCanvas == null)
        {
            CreateCanvas();
        }
        
        if (eventSystem == null)
        {
            CreateEventSystem();
        }
        
        CreateTestButton();
    }
    
    void CreateCanvas()
    {
        GameObject canvasObj = new GameObject("Canvas");
        mainCanvas = canvasObj.AddComponent<Canvas>();
        mainCanvas.renderMode = RenderMode.ScreenSpaceOverlay;
        canvasObj.AddComponent<GraphicRaycaster>();
        
        Debug.Log("Canvas created");
    }
    
    void CreateEventSystem()
    {
        GameObject eventObj = new GameObject("EventSystem");
        eventSystem = eventObj.AddComponent<EventSystem>();
        eventObj.AddComponent<StandaloneInputModule>();
        
        Debug.Log("EventSystem created");
    }
    
    void CreateTestButton()
    {
        GameObject buttonObj = new GameObject("TestButton");
        buttonObj.transform.SetParent(mainCanvas.transform, false);
        
        RectTransform rect = buttonObj.AddComponent<RectTransform>();
        rect.sizeDelta = new Vector2(100, 30);
        
        Image img = buttonObj.AddComponent<Image>();
        img.color = Color.white;
        
        Button btn = buttonObj.AddComponent<Button>();
        btn.onClick.AddListener(() => {
            Debug.Log("Test button clicked!");
        });
        
        elements["TestButton"] = buttonObj;
        Debug.Log("Test button created");
    }
    
    public int GetElementCount()
    {
        return elements.Count;
    }
}