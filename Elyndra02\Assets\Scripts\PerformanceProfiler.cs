using UnityEngine;
using UnityEngine.Profiling;
using System.Collections.Generic;
using System.Text;

public class PerformanceProfiler : MonoBehaviour
{
    [Header("Profiling Settings")]
    public bool enableProfiling = true;
    public float updateInterval = 1f;
    public bool showOnScreenStats = true;
    
    [Header("Memory Tracking")]
    public bool trackMemory = true;
    public bool trackGC = true;
    
    private float deltaTime = 0f;
    private float fps = 0f;
    private long totalMemory = 0;
    private long usedMemory = 0;
    // Removed unused fields: drawCalls, triangles, vertices
    
    private List<float> fpsHistory = new List<float>();
    private const int maxHistorySize = 60;
    
    private GUIStyle guiStyle;
    private StringBuilder statsBuilder = new StringBuilder();
    
    void Start()
    {
        InitializeProfiler();
        InvokeRepeating(nameof(UpdateStats), 0f, updateInterval);
    }
    
    void InitializeProfiler()
    {
        guiStyle = new GUIStyle();
        guiStyle.fontSize = 16;
        guiStyle.normal.textColor = Color.white;
        
        Debug.Log("Performance Profiler initialized!");
    }
    
    void Update()
    {
        if (!enableProfiling) return;
        
        deltaTime += (Time.unscaledDeltaTime - deltaTime) * 0.1f;
        fps = 1.0f / deltaTime;
        
        // Track FPS history
        fpsHistory.Add(fps);
        if (fpsHistory.Count > maxHistorySize)
        {
            fpsHistory.RemoveAt(0);
        }
    }
    
    void UpdateStats()
    {
        if (!enableProfiling) return;
        
        // Memory stats
        if (trackMemory)
        {
            totalMemory = Profiler.GetTotalAllocatedMemoryLong();
            usedMemory = Profiler.GetTotalReservedMemoryLong();
        }
        
        // Rendering stats removed (unused fields)
        
        BuildStatsString();
    }
    
    void BuildStatsString()
    {
        statsBuilder.Clear();
        
        statsBuilder.AppendLine($"FPS: {fps:F1}");
        statsBuilder.AppendLine($"Frame Time: {deltaTime * 1000:F1}ms");
        
        if (trackMemory)
        {
            statsBuilder.AppendLine($"Total Memory: {totalMemory / 1024 / 1024}MB");
            statsBuilder.AppendLine($"Used Memory: {usedMemory / 1024 / 1024}MB");
        }
        
        if (fpsHistory.Count > 0)
        {
            float avgFps = 0f;
            float minFps = float.MaxValue;
            float maxFps = float.MinValue;
            
            foreach (float f in fpsHistory)
            {
                avgFps += f;
                if (f < minFps) minFps = f;
                if (f > maxFps) maxFps = f;
            }
            
            avgFps /= fpsHistory.Count;
            
            statsBuilder.AppendLine($"Avg FPS: {avgFps:F1}");
            statsBuilder.AppendLine($"Min FPS: {minFps:F1}");
            statsBuilder.AppendLine($"Max FPS: {maxFps:F1}");
        }
        
        statsBuilder.AppendLine($"Time Scale: {Time.timeScale:F2}");
        statsBuilder.AppendLine($"Quality Level: {QualitySettings.GetQualityLevel()}");
    }
    
    void OnGUI()
    {
        if (!showOnScreenStats || !enableProfiling) return;
        
        GUI.Label(new Rect(10, 10, 300, 200), statsBuilder.ToString(), guiStyle);
    }
    
    public void StartProfiling()
    {
        enableProfiling = true;
        Profiler.enabled = true;
        Debug.Log("Profiling started!");
    }
    
    public void StopProfiling()
    {
        enableProfiling = false;
        Profiler.enabled = false;
        Debug.Log("Profiling stopped!");
    }
    
    public void ClearHistory()
    {
        fpsHistory.Clear();
        Debug.Log("Performance history cleared!");
    }
    
    public float GetAverageFPS()
    {
        if (fpsHistory.Count == 0) return 0f;
        
        float total = 0f;
        foreach (float f in fpsHistory)
        {
            total += f;
        }
        
        return total / fpsHistory.Count;
    }
    
    public void LogPerformanceReport()
    {
        Debug.Log("=== PERFORMANCE REPORT ===");
        Debug.Log(statsBuilder.ToString());
        Debug.Log("=========================");
    }
}