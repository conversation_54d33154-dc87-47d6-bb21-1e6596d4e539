using UnityEngine;
using UnityEngine.Profiling;
using System.Collections.Generic;

public class SimpleProfiler : MonoBehaviour
{
    [Header("Settings")]
    public bool enableProfiling = true;
    public float updateInterval = 1f;
    
    private List<float> fpsSamples = new List<float>();
    private List<long> memorySamples = new List<long>();
    private float timer = 0f;
    
    void Start()
    {
        Debug.Log("Simple Profiler initialized!");
        
        if (enableProfiling)
        {
            Profiler.enabled = true;
        }
    }
    
    void Update()
    {
        if (!enableProfiling) return;
        
        timer += Time.deltaTime;
        
        if (timer >= updateInterval)
        {
            CollectSamples();
            timer = 0f;
        }
    }
    
    void CollectSamples()
    {
        // FPS
        float fps = 1f / Time.deltaTime;
        fpsSamples.Add(fps);
        
        // Memory
        long memory = Profiler.GetTotalAllocatedMemoryLong();
        memorySamples.Add(memory);
        
        // Keep only last 100 samples
        if (fpsSamples.Count > 100)
        {
            fpsSamples.RemoveAt(0);
        }
        
        if (memorySamples.Count > 100)
        {
            memorySamples.RemoveAt(0);
        }
    }
    
    [ContextMenu("Generate Report")]
    public void GenerateReport()
    {
        if (fpsSamples.Count == 0)
        {
            Debug.Log("No samples collected yet");
            return;
        }
        
        float avgFPS = CalculateAverage(fpsSamples);
        long avgMemory = (long)CalculateAverage(memorySamples);
        
        Debug.Log($"=== PROFILER REPORT ===");
        Debug.Log($"Average FPS: {avgFPS:F1}");
        Debug.Log($"Average Memory: {FormatBytes(avgMemory)}");
        Debug.Log($"Current Memory: {FormatBytes(Profiler.GetTotalAllocatedMemoryLong())}");
        Debug.Log($"Samples: {fpsSamples.Count}");
    }
    
    float CalculateAverage(List<float> samples)
    {
        float sum = 0f;
        foreach (float sample in samples)
        {
            sum += sample;
        }
        return sum / samples.Count;
    }
    
    float CalculateAverage(List<long> samples)
    {
        long sum = 0;
        foreach (long sample in samples)
        {
            sum += sample;
        }
        return (float)sum / samples.Count;
    }
    
    string FormatBytes(long bytes)
    {
        if (bytes >= 1024 * 1024)
        {
            return $"{bytes / (1024f * 1024f):F2} MB";
        }
        else if (bytes >= 1024)
        {
            return $"{bytes / 1024f:F2} KB";
        }
        else
        {
            return $"{bytes} B";
        }
    }
    
    public void StartProfiling()
    {
        enableProfiling = true;
        Profiler.enabled = true;
        Debug.Log("Profiling started");
    }
    
    public void StopProfiling()
    {
        enableProfiling = false;
        Debug.Log("Profiling stopped");
    }
    
    public float GetCurrentFPS()
    {
        return fpsSamples.Count > 0 ? fpsSamples[fpsSamples.Count - 1] : 0f;
    }
    
    public int GetSampleCount()
    {
        return fpsSamples.Count;
    }
}