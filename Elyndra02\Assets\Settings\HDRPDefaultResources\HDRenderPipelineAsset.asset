%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cf1dab834d4ec34195b920ea7bbf9ec, type: 3}
  m_Name: HDRenderPipelineAsset
  m_EditorClassIdentifier: 
  m_RenderPipelineSettings:
    supportShadowMask: 1
    supportSSR: 0
    supportSSRTransparent: 0
    supportSSAO: 1
    supportSSGI: 0
    supportSubsurfaceScattering: 1
    subsurfaceScatteringAttenuation: 1
    sssSampleBudget:
      m_Values: 140000002800000050000000
      m_SchemaId:
        m_Id: With3Levels
    sssDownsampleSteps:
      m_Values: 000000000000000000000000
      m_SchemaId:
        m_Id: With3Levels
    supportVolumetrics: 1
    supportVolumetricClouds: 0
    supportLightLayers: 0
    renderingLayerMaskBuffer: 0
    supportWater: 0
    waterSimulationResolution: 128
    supportWaterExclusion: 0
    supportWaterHorizontalDeformation: 0
    supportWaterDecals: 1
    waterDecalAtlasSize: 1024
    maximumWaterDecalCount: 48
    waterScriptInteractionsMode: 0
    waterFullCPUSimulation: 0
    supportComputeThickness: 0
    computeThicknessResolution: 1
    computeThicknessLayerMask:
      serializedVersion: 2
      m_Bits: 0
    supportDistortion: 1
    supportTransparentBackface: 1
    supportTransparentDepthPrepass: 1
    supportTransparentDepthPostpass: 1
    colorBufferFormat: 74
    supportCustomPass: 1
    supportVariableRateShading: 1
    customBufferFormat: 12
    supportedLitShaderMode: 2
    planarReflectionResolution:
      m_Values: 000100000004000000080000
      m_SchemaId:
        m_Id: With3Levels
    cubeReflectionResolution:
      m_Values: 000100000002000000040000
      m_SchemaId:
        m_Id: With3Levels
    supportDecals: 1
    supportDecalLayers: 0
    supportSurfaceGradient: 0
    decalNormalBufferHP: 0
    supportHighQualityLineRendering: 0
    highQualityLineRenderingMemoryBudget: 128
    msaaSampleCount: 1
    supportMotionVectors: 1
    supportScreenSpaceLensFlare: 1
    supportDataDrivenLensFlare: 1
    supportDitheringCrossFade: 1
    supportRuntimeAOVAPI: 0
    supportTerrainHole: 0
    lightProbeSystem: 0
    oldLightProbeSystem: 0
    probeVolumeMemoryBudget: 1024
    supportProbeVolumeGPUStreaming: 0
    supportProbeVolumeDiskStreaming: 0
    probeVolumeSHBands: 1
    supportProbeVolumeScenarios: 0
    supportProbeVolumeScenarioBlending: 1
    probeVolumeBlendingMemoryBudget: 128
    supportRayTracing: 0
    supportVFXRayTracing: 0
    supportedRayTracingMode: 3
    lightLoopSettings:
      cookieAtlasSize: 2048
      cookieFormat: 74
      cookieAtlasLastValidMip: 0
      cookieTexArraySize: 1
      planarReflectionAtlasSize: 1024
      reflectionProbeCacheSize: 64
      reflectionCubemapSize: 256
      maxEnvLightsOnScreen: 64
      reflectionCacheCompressed: 0
      reflectionProbeFormat: 74
      reflectionProbeTexCacheSize: 1073750016
      reflectionProbeTexLastValidCubeMip: 3
      reflectionProbeTexLastValidPlanarMip: 0
      reflectionProbeDecreaseResToFit: 1
      skyReflectionSize: 256
      skyLightingOverrideLayerMask:
        serializedVersion: 2
        m_Bits: 0
      supportFabricConvolution: 0
      maxDirectionalLightsOnScreen: 16
      maxPunctualLightsOnScreen: 512
      maxAreaLightsOnScreen: 64
      maxCubeReflectionOnScreen: 48
      maxPlanarReflectionOnScreen: 16
      maxDecalsOnScreen: 512
      maxLightsPerClusterCell: 8
      maxLocalVolumetricFogSize: 32
      maxLocalVolumetricFogOnScreen: 64
    hdShadowInitParams:
      maxShadowRequests: 128
      directionalShadowsDepthBits: 32
      punctualShadowFilteringQuality: 1
      directionalShadowFilteringQuality: 1
      areaShadowFilteringQuality: 0
      punctualLightShadowAtlas:
        shadowAtlasResolution: 4096
        shadowAtlasDepthBits: 32
        useDynamicViewportRescale: 1
      areaLightShadowAtlas:
        shadowAtlasResolution: 4096
        shadowAtlasDepthBits: 32
        useDynamicViewportRescale: 1
      cachedPunctualLightShadowAtlas: 2048
      cachedAreaLightShadowAtlas: 1024
      allowDirectionalMixedCachedShadows: 0
      shadowResolutionDirectional:
        m_Values: 00010000000200000004000000080000
        m_SchemaId:
          m_Id: With4Levels
      shadowResolutionPunctual:
        m_Values: 00010000000200000004000000080000
        m_SchemaId:
          m_Id: With4Levels
      shadowResolutionArea:
        m_Values: 00010000000200000004000000080000
        m_SchemaId:
          m_Id: With4Levels
      maxDirectionalShadowMapResolution: 2048
      maxPunctualShadowMapResolution: 2048
      maxAreaShadowMapResolution: 2048
      supportScreenSpaceShadows: 0
      maxScreenSpaceShadowSlots: 4
      screenSpaceShadowBufferFormat: 48
    decalSettings:
      drawDistance: 1000
      atlasWidth: 4096
      atlasHeight: 4096
      transparentTextureResolution:
        m_Values: 000100000002000000040000
        m_SchemaId:
          m_Id: With3Levels
      perChannelMask: 0
    postProcessSettings:
      m_LutSize: 32
      lutFormat: 48
      bufferFormat: 74
    dynamicResolutionSettings:
      enabled: 0
      useMipBias: 0
      advancedUpscalersByPriority: 
      DLSSPerfQualitySetting: 0
      DLSSInjectionPoint: 0
      TAAUInjectionPoint: 0
      STPInjectionPoint: 0
      defaultInjectionPoint: 2
      DLSSUseOptimalSettings: 0
      DLSSSharpness: 0
      FSR2EnableSharpness: 0
      FSR2Sharpness: 0
      FSR2UseOptimalSettings: 0
      FSR2QualitySetting: 0
      FSR2InjectionPoint: 0
      fsrOverrideSharpness: 0
      fsrSharpness: 0.92
      maxPercentage: 100
      minPercentage: 100
      dynResType: 1
      upsampleFilter: 1
      forceResolution: 0
      forcedPercentage: 100
      lowResTransparencyMinimumThreshold: 0
      rayTracingHalfResThreshold: 50
      lowResSSGIMinimumThreshold: 0
      lowResVolumetricCloudsMinimumThreshold: 50
      enableDLSS: 0
    lowresTransparentSettings:
      enabled: 1
      checkerboardDepthBuffer: 1
      upsampleType: 1
    xrSettings:
      singlePass: 1
      occlusionMesh: 1
      cameraJitter: 0
      allowMotionBlur: 0
    postProcessQualitySettings:
      NearBlurSampleCount: 030000000500000008000000
      NearBlurMaxRadius:
      - 2
      - 4
      - 7
      FarBlurSampleCount: 04000000070000000e000000
      FarBlurMaxRadius:
      - 5
      - 8
      - 13
      DoFResolution: 040000000200000001000000
      DoFHighQualityFiltering: 000101
      DoFPhysicallyBased: 000000
      AdaptiveSamplingWeight:
      - 0.5
      - 0.75
      - 2
      LimitManualRangeNearBlur: 000000
      MotionBlurSampleCount: 04000000080000000c000000
      BloomRes: 040000000200000002000000
      BloomHighQualityFiltering: 000101
      BloomHighQualityPrefiltering: 000001
      ChromaticAberrationMaxSamples: 03000000060000000c000000
    lightSettings:
      useContactShadow:
        m_Values: 000001
        m_SchemaId:
          m_Id: With3Levels
    maximumLODLevel:
      m_Values: 000000000000000000000000
      m_SchemaId:
        m_Id: With3Levels
    lodBias:
      m_Values:
      - 1
      - 1
      - 1
      m_SchemaId:
        m_Id: With3Levels
    lightingQualitySettings:
      AOStepCount: 040000000600000010000000
      AOFullRes: 000001
      AOMaximumRadiusPixels: 200000002800000050000000
      AOBilateralUpsample: 000101
      AODirectionCount: 010000000200000004000000
      ContactShadowSampleCount: 060000000a00000010000000
      SSRMaxRaySteps: 100000002000000040000000
      SSGIRaySteps: 200000004000000080000000
      SSGIDenoise: 010101
      SSGIHalfResDenoise: 010000
      SSGIDenoiserRadius:
      - 0.75
      - 0.5
      - 0.5
      SSGISecondDenoise: 010101
      RTAORayLength:
      - 0.5
      - 3
      - 20
      RTAOSampleCount: 010000000200000008000000
      RTAODenoise: 010101
      RTAODenoiserRadius:
      - 0.25
      - 0.5
      - 0.65
      RTGIRayLength:
      - 50
      - 50
      - 50
      RTGIFullResolution: 000001
      RTGIRaySteps: 200000003000000040000000
      RTGIDenoise: 010101
      RTGIHalfResDenoise: 010000
      RTGIDenoiserRadius:
      - 0.75
      - 0.5
      - 0.25
      RTGISecondDenoise: 010101
      RTRMinSmoothness:
      - 0.6
      - 0.4
      - 0
      RTRSmoothnessFadeStart:
      - 0.7
      - 0.5
      - 0
      RTRRayLength:
      - 50
      - 50
      - 50
      RTRFullResolution: 000001
      RTRRayMaxIterations: 200000003000000040000000
      RTRDenoise: 010101
      RTRDenoiserRadiusDimmer:
      - 0.75
      - 0.75
      - 1
      RTRDenoiserAntiFlicker:
      - 1
      - 1
      - 1
      Fog_ControlMode: 000000000000000000000000
      Fog_Budget:
      - 0.166
      - 0.33
      - 0.666
      Fog_DepthRatio:
      - 0.666
      - 0.666
      - 0.5
    gpuResidentDrawerSettings:
      mode: 0
      smallMeshScreenPercentage: 0
      enableOcclusionCullingInCameras: 0
      useDepthPrepassForOccluders: 1
    m_ObsoleteLightLayerName0: 
    m_ObsoleteLightLayerName1: 
    m_ObsoleteLightLayerName2: 
    m_ObsoleteLightLayerName3: 
    m_ObsoleteLightLayerName4: 
    m_ObsoleteLightLayerName5: 
    m_ObsoleteLightLayerName6: 
    m_ObsoleteLightLayerName7: 
    m_ObsoleteDecalLayerName0: 
    m_ObsoleteDecalLayerName1: 
    m_ObsoleteDecalLayerName2: 
    m_ObsoleteDecalLayerName3: 
    m_ObsoleteDecalLayerName4: 
    m_ObsoleteDecalLayerName5: 
    m_ObsoleteDecalLayerName6: 
    m_ObsoleteDecalLayerName7: 
    m_ObsoleteSupportRuntimeDebugDisplay: 0
  allowShaderVariantStripping: 1
  enableSRPBatcher: 1
  availableMaterialQualityLevels: -1
  m_DefaultMaterialQualityLevel: 4
  diffusionProfileSettings: {fileID: 0}
  m_VolumeProfile: {fileID: 0}
  virtualTexturingSettings:
    streamingCpuCacheSizeInMegaBytes: 256
    streamingMipPreloadTexturesPerFrame: 0
    streamingPreloadMipCount: 1
    streamingGpuCacheSettings:
    - format: 0
      sizeInMegaBytes: 128
  m_UseRenderGraph: 1
  m_CompositorCustomVolumeComponentsList:
    m_InjectionPoint: 1
    m_CustomPostProcessTypesAsString: []
  m_Version: 25
  m_ObsoleteFrameSettings:
    overrides: 0
    enableShadow: 0
    enableContactShadows: 0
    enableShadowMask: 0
    enableSSR: 0
    enableSSAO: 0
    enableSubsurfaceScattering: 0
    enableTransmission: 0
    enableAtmosphericScattering: 0
    enableVolumetrics: 0
    enableReprojectionForVolumetrics: 0
    enableLightLayers: 0
    enableExposureControl: 1
    diffuseGlobalDimmer: 0
    specularGlobalDimmer: 0
    shaderLitMode: 0
    enableDepthPrepassWithDeferredRendering: 0
    enableTransparentPrepass: 0
    enableMotionVectors: 0
    enableObjectMotionVectors: 0
    enableDecals: 0
    enableRoughRefraction: 0
    enableTransparentPostpass: 0
    enableDistortion: 0
    enablePostprocess: 0
    enableOpaqueObjects: 0
    enableTransparentObjects: 0
    enableRealtimePlanarReflection: 0
    enableMSAA: 0
    enableAsyncCompute: 0
    runLightListAsync: 0
    runSSRAsync: 0
    runSSAOAsync: 0
    runContactShadowsAsync: 0
    runVolumeVoxelizationAsync: 0
    lightLoopSettings:
      overrides: 0
      enableDeferredTileAndCluster: 0
      enableComputeLightEvaluation: 0
      enableComputeLightVariants: 0
      enableComputeMaterialVariants: 0
      enableFptlForForwardOpaque: 0
      enableBigTilePrepass: 0
      isFptlEnabled: 0
  m_ObsoleteBakedOrCustomReflectionFrameSettings:
    overrides: 0
    enableShadow: 0
    enableContactShadows: 0
    enableShadowMask: 0
    enableSSR: 0
    enableSSAO: 0
    enableSubsurfaceScattering: 0
    enableTransmission: 0
    enableAtmosphericScattering: 0
    enableVolumetrics: 0
    enableReprojectionForVolumetrics: 0
    enableLightLayers: 0
    enableExposureControl: 1
    diffuseGlobalDimmer: 0
    specularGlobalDimmer: 0
    shaderLitMode: 0
    enableDepthPrepassWithDeferredRendering: 0
    enableTransparentPrepass: 0
    enableMotionVectors: 0
    enableObjectMotionVectors: 0
    enableDecals: 0
    enableRoughRefraction: 0
    enableTransparentPostpass: 0
    enableDistortion: 0
    enablePostprocess: 0
    enableOpaqueObjects: 0
    enableTransparentObjects: 0
    enableRealtimePlanarReflection: 0
    enableMSAA: 0
    enableAsyncCompute: 0
    runLightListAsync: 0
    runSSRAsync: 0
    runSSAOAsync: 0
    runContactShadowsAsync: 0
    runVolumeVoxelizationAsync: 0
    lightLoopSettings:
      overrides: 0
      enableDeferredTileAndCluster: 0
      enableComputeLightEvaluation: 0
      enableComputeLightVariants: 0
      enableComputeMaterialVariants: 0
      enableFptlForForwardOpaque: 0
      enableBigTilePrepass: 0
      isFptlEnabled: 0
  m_ObsoleteRealtimeReflectionFrameSettings:
    overrides: 0
    enableShadow: 0
    enableContactShadows: 0
    enableShadowMask: 0
    enableSSR: 0
    enableSSAO: 0
    enableSubsurfaceScattering: 0
    enableTransmission: 0
    enableAtmosphericScattering: 0
    enableVolumetrics: 0
    enableReprojectionForVolumetrics: 0
    enableLightLayers: 0
    enableExposureControl: 1
    diffuseGlobalDimmer: 0
    specularGlobalDimmer: 0
    shaderLitMode: 0
    enableDepthPrepassWithDeferredRendering: 0
    enableTransparentPrepass: 0
    enableMotionVectors: 0
    enableObjectMotionVectors: 0
    enableDecals: 0
    enableRoughRefraction: 0
    enableTransparentPostpass: 0
    enableDistortion: 0
    enablePostprocess: 0
    enableOpaqueObjects: 0
    enableTransparentObjects: 0
    enableRealtimePlanarReflection: 0
    enableMSAA: 0
    enableAsyncCompute: 0
    runLightListAsync: 0
    runSSRAsync: 0
    runSSAOAsync: 0
    runContactShadowsAsync: 0
    runVolumeVoxelizationAsync: 0
    lightLoopSettings:
      overrides: 0
      enableDeferredTileAndCluster: 0
      enableComputeLightEvaluation: 0
      enableComputeLightVariants: 0
      enableComputeMaterialVariants: 0
      enableFptlForForwardOpaque: 0
      enableBigTilePrepass: 0
      isFptlEnabled: 0
  m_ObsoleteDefaultVolumeProfile: {fileID: 0}
  m_ObsoleteDefaultLookDevProfile: {fileID: 0}
  m_ObsoleteFrameSettingsMovedToDefaultSettings:
    bitDatas:
      data1: 0
      data2: 0
    lodBias: 0
    lodBiasMode: 0
    lodBiasQualityLevel: 0
    maximumLODLevel: 0
    maximumLODLevelMode: 0
    maximumLODLevelQualityLevel: 0
    sssQualityMode: 0
    sssQualityLevel: 0
    sssCustomSampleBudget: 0
    sssCustomDownsampleSteps: 0
    msaaMode: 0
    materialQuality: 0
  m_ObsoleteBakedOrCustomReflectionFrameSettingsMovedToDefaultSettings:
    bitDatas:
      data1: 0
      data2: 0
    lodBias: 0
    lodBiasMode: 0
    lodBiasQualityLevel: 0
    maximumLODLevel: 0
    maximumLODLevelMode: 0
    maximumLODLevelQualityLevel: 0
    sssQualityMode: 0
    sssQualityLevel: 0
    sssCustomSampleBudget: 0
    sssCustomDownsampleSteps: 0
    msaaMode: 0
    materialQuality: 0
  m_ObsoleteRealtimeReflectionFrameSettingsMovedToDefaultSettings:
    bitDatas:
      data1: 0
      data2: 0
    lodBias: 0
    lodBiasMode: 0
    lodBiasQualityLevel: 0
    maximumLODLevel: 0
    maximumLODLevelMode: 0
    maximumLODLevelQualityLevel: 0
    sssQualityMode: 0
    sssQualityLevel: 0
    sssCustomSampleBudget: 0
    sssCustomDownsampleSteps: 0
    msaaMode: 0
    materialQuality: 0
  m_ObsoleteBeforeTransparentCustomPostProcesses: []
  m_ObsoleteBeforePostProcessCustomPostProcesses: []
  m_ObsoleteAfterPostProcessCustomPostProcesses: []
  m_ObsoleteBeforeTAACustomPostProcesses: []
  m_ObsoleteShaderVariantLogLevel: 0
  m_ObsoleteLensAttenuation: 0
  m_ObsoleteDiffusionProfileSettingsList: []
  m_PrefilterUseLegacyLightmaps: 0
  m_PrefilterUseLightmapBicubicSampling: 0
