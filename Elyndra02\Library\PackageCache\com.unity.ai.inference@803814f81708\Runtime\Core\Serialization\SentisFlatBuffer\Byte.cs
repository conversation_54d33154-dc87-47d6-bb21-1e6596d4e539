// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>
#define ENABLE_SPAN_T
#define UNSAFE_BYTEBUFFER
#define BYTEBUFFER_NO_BOUNDS_CHECK

namespace SentisFlatBuffer
{

using global::System;
using global::System.Collections.Generic;
using global::Unity.InferenceEngine.Google.FlatBuffers;

struct Byte : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_5_26(); }
  public static Byte GetRootAsByte(ByteBuffer _bb) { return GetRootAsByte(_bb, new Byte()); }
  public static Byte GetRootAsByte(ByteBuffer _bb, Byte obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, Byte<PERSON>uff<PERSON> _bb) { __p = new Table(_i, _bb); }
  public Byte __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public byte ByteVal { get { int o = __p.__offset(4); return o != 0 ? __p.bb.Get(o + __p.bb_pos) : (byte)0; } }

  public static Offset<SentisFlatBuffer.Byte> CreateByte(FlatBufferBuilder builder,
      byte byte_val = 0) {
    builder.StartTable(1);
    Byte.AddByteVal(builder, byte_val);
    return Byte.EndByte(builder);
  }

  public static void StartByte(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddByteVal(FlatBufferBuilder builder, byte byteVal) { builder.AddByte(0, byteVal, 0); }
  public static Offset<SentisFlatBuffer.Byte> EndByte(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<SentisFlatBuffer.Byte>(o);
  }
}


static class ByteVerify
{
  static public bool Verify(Unity.InferenceEngine.Google.FlatBuffers.Verifier verifier, uint tablePos)
  {
    return verifier.VerifyTableStart(tablePos)
      && verifier.VerifyField(tablePos, 4 /*ByteVal*/, 1 /*byte*/, 1, false)
      && verifier.VerifyTableEnd(tablePos);
  }
}

}
