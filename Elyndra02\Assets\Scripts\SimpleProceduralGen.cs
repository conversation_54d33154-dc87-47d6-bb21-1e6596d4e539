using UnityEngine;

public class SimpleProceduralGen : MonoBehaviour
{
    [Header("Generation Settings")]
    public int seed = 12345;
    public Vector2Int mapSize = new Vector2Int(50, 50);
    public float noiseScale = 0.1f;
    public float terrainHeight = 10f;
    
    [Header("Objects")]
    public GameObject[] prefabs;
    public float density = 0.1f;
    
    private System.Random random;
    
    void Start()
    {
        random = new System.Random(seed);
        GenerateObjects();
    }
    
    void GenerateObjects()
    {
        if (prefabs.Length == 0) return;
        
        int objectCount = Mathf.RoundToInt(mapSize.x * mapSize.y * density);
        
        for (int i = 0; i < objectCount; i++)
        {
            float x = random.Next(0, mapSize.x);
            float z = random.Next(0, mapSize.y);
            float y = Mathf.PerlinNoise(x * noiseScale, z * noiseScale) * terrainHeight;
            
            Vector3 position = new Vector3(x, y, z);
            GameObject prefab = prefabs[random.Next(prefabs.Length)];
            
            Instantiate(prefab, position, Quaternion.identity, transform);
        }
        
        Debug.Log($"Generated {objectCount} procedural objects");
    }
    
    public void Regenerate()
    {
        // Clear existing
        for (int i = transform.childCount - 1; i >= 0; i--)
        {
            DestroyImmediate(transform.GetChild(i).gameObject);
        }
        
        // Generate new
        GenerateObjects();
    }
}