using UnityEngine;
using System.Collections;

public class ProceduralAudioController : MonoBehaviour
{
    [Header("Procedural Audio Settings")]
    public AudioSource audioSource;
    
    [Header("Wave Generation")]
    public WaveType waveType = WaveType.Sine;
    public float frequency = 440.0f;
    public float amplitude = 0.5f;
    public int sampleRate = 44100;
    
    [Header("Real-time Controls")]
    public bool realTimeGeneration = false;
    public float frequencyModulation = 0.0f;
    public AnimationCurve frequencyCurve = AnimationCurve.Linear(0, 1, 1, 1);
    
    public enum WaveType
    {
        Sine,
        Square,
        Triangle,
        Sawtooth,
        Noise
    }
    
    private AudioClip proceduralClip;
    private float[] clipData;
    private int samples;
    private float time;
    
    void Start()
    {
        if (audioSource == null)
            audioSource = GetComponent<AudioSource>();
            
        if (audioSource == null)
        {
            Debug.LogError("ProceduralAudioController: No AudioSource found!");
            return;
        }
        
        InitializeProceduralAudio();
    }
    
    void Update()
    {
        if (realTimeGeneration && audioSource.isPlaying)
        {
            UpdateProceduralAudio();
        }
    }
    
    public void InitializeProceduralAudio()
    {
        samples = sampleRate;
        proceduralClip = AudioClip.Create("ProceduralClip", samples, 1, sampleRate, true, OnAudioRead);
        audioSource.clip = proceduralClip;
        
        clipData = new float[samples];
    }
    
    public void PlayProceduralAudio()
    {
        if (audioSource != null && proceduralClip != null)
        {
            audioSource.Play();
        }
    }
    
    public void StopProceduralAudio()
    {
        if (audioSource != null)
        {
            audioSource.Stop();
        }
    }
    
    public void SetFrequency(float newFrequency)
    {
        frequency = newFrequency;
        if (realTimeGeneration)
        {
            GenerateWaveData();
            proceduralClip.SetData(clipData, 0);
        }
    }
    
    public void SetWaveType(WaveType newWaveType)
    {
        waveType = newWaveType;
        if (realTimeGeneration)
        {
            GenerateWaveData();
            proceduralClip.SetData(clipData, 0);
        }
    }
    
    private void UpdateProceduralAudio()
    {
        float currentFrequency = frequency + (frequencyModulation * frequencyCurve.Evaluate(time));
        
        // Apply frequency modulation
        if (Mathf.Abs(currentFrequency - frequency) > 0.1f)
        {
            frequency = currentFrequency;
            GenerateWaveData();
        }
        
        time += Time.deltaTime;
        if (time > 1.0f) time = 0.0f;
    }
    
    private void OnAudioRead(float[] data)
    {
        if (clipData != null && clipData.Length == data.Length)
        {
            System.Array.Copy(clipData, data, data.Length);
        }
    }
    
    private void GenerateWaveData()
    {
        for (int i = 0; i < samples; i++)
        {
            float t = (float)i / sampleRate;
            float waveValue = 0.0f;
            
            switch (waveType)
            {
                case WaveType.Sine:
                    waveValue = Mathf.Sin(2.0f * Mathf.PI * frequency * t);
                    break;
                case WaveType.Square:
                    waveValue = Mathf.Sign(Mathf.Sin(2.0f * Mathf.PI * frequency * t));
                    break;
                case WaveType.Triangle:
                    waveValue = 2.0f * Mathf.Abs(2.0f * ((frequency * t) % 1.0f) - 1.0f) - 1.0f;
                    break;
                case WaveType.Sawtooth:
                    waveValue = 2.0f * ((frequency * t) % 1.0f) - 1.0f;
                    break;
                case WaveType.Noise:
                    waveValue = Random.Range(-1.0f, 1.0f);
                    break;
            }
            
            clipData[i] = waveValue * amplitude;
        }
    }
    
    void OnValidate()
    {
        frequency = Mathf.Clamp(frequency, 20.0f, 20000.0f);
        amplitude = Mathf.Clamp01(amplitude);
        
        if (Application.isPlaying && realTimeGeneration)
        {
            GenerateWaveData();
            if (proceduralClip != null)
                proceduralClip.SetData(clipData, 0);
        }
    }
}