# Changelog
All notable changes to this package will be documented in this file.

The format is based on [Keep a Changelog](http://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](http://semver.org/spec/v2.0.0.html).

## [1.0.0-pre.15] - 2025-06-27

### Changed

- Changed to a soft-dependency pattern to import Plugin attributes from AI Assistant for Generators.

### Fixed

- Fixed points refresh on access token refresh on domain reload, open, or play and stop.

## [1.0.0-pre.14] - 2025-06-26

### Added

- Added 'View Documentation' link.

### Fixed

- Fixed 'Manage Account' link.

## [1.0.0-pre.13] - 2025-06-25

### Added

- Added remaining points tooltip.
- Added 'hide AI menu' preference.
- Added manage account menu item.

### Changed

- Update SDK to version 0.21.0.

## [1.0.0-pre.12] - 2025-06-02

### Fixed

- Fixed internal account environment.

## [1.0.0-pre.11] - 2025-05-29

### Changed

- Run Unity Hub if it isn't running when refreshing the cloud access token.
- Show invalid cloud project banner if cloud settings not valid for 10 seconds or more.

## [1.0.0-pre.10] - 2025-05-27

### Changed

- Implemented an attempt to refresh the cloud access token when querying the Points Manager, if Unity Hub is running.

## [1.0.0-pre.9] - 2025-05-22

### Added

- Added support for Editor paused play mode.

### Changed

- Updated the 'Unityai' asset label to 'UnityAI'.

## [1.0.0-pre.8] - 2025-05-14

### Fixed

- Fixed some connection loading indicators.

## [1.0.0-pre.7] - 2025-05-12

### Fixed

- Fixed labels and copy.
- Fixed ai enablement flags sync with cloud project.

### Changed

- Update SDK to version 0.18.0.

## [1.0.0-pre.6] - 2025-04-30

### Fixed

- Fixed labels and copy.

## [1.0.0-pre.5] - 2025-04-23

### Fixed

- Fixed "Open Assistant" menu.

## [1.0.0-pre.4] - 2025-04-16

### Changed

- Update SDK to version 0.16.2.
- Update assistant plugins.
- Update ai-enabled for assistant and generators.

## [1.0.0-pre.3] - 2025-04-09

### Changed

- Update SDK to version 0.15.0.

## [1.0.0-pre.2] - 2025-03-27

### Changed

- Moved shared modules to generators namespace.

## [1.0.0-pre.1] - 2025-03-21

### Added

- Initial release of the AI Toolkit package.
