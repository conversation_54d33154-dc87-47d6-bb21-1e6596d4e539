{"name": "com.unity.ai.toolkit", "displayName": "AI Toolkit", "version": "1.0.0-pre.15", "unity": "6000.2", "unityRelease": "0b4", "description": "AI Toolkit is a package that provides tools for adding generative AI to Unity.", "dependencies": {"com.unity.nuget.newtonsoft-json": "3.2.1"}, "relatedPackages": {"com.unity.ai.toolkit.tests": "1.0.0-pre.15"}, "_upm": {"changelog": "### Changed\n\n- Changed to a soft-dependency pattern to import Plugin attributes from AI Assistant for Generators.\n\n### Fixed\n\n- Fixed points refresh on access token refresh on domain reload, open, or play and stop."}, "upmCi": {"footprint": "32037a81ce5589eaa4a26662e21608170723dfc9"}, "documentationUrl": "https://docs.unity3d.com/Packages/com.unity.ai.toolkit@1.0/manual/index.html", "repository": {"url": "https://github.cds.internal.unity3d.com/unity/UnityAIWorkflows.git", "type": "git", "revision": "2329c1b51594ac5671e1b7062174a1412d65de14"}, "_fingerprint": "b709126c2e56f082e4d6886ff1d085840ca366e1"}