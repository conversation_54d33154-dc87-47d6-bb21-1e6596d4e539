using UnityEngine;
using UnityEngine.AI;

public class SimpleAI : MonoBehaviour
{
    [<PERSON><PERSON>("AI Settings")]
    public float detectionRange = 10f;
    public float moveSpeed = 3.5f;
    public Transform target;
    
    [Header("Patrol")]
    public Transform[] patrolPoints;
    public float waitTime = 2f;
    
    private NavMeshAgent navAgent;
    private int currentPatrolIndex = 0;
    private bool isPatrolling = true;
    
    private void Start()
    {
        navAgent = GetComponent<NavMeshAgent>();
        if (navAgent == null)
        {
            navAgent = gameObject.AddComponent<NavMeshAgent>();
        }
        
        navAgent.speed = moveSpeed;
        
        if (patrolPoints.Length > 0)
        {
            navAgent.SetDestination(patrolPoints[0].position);
        }
        
        Debug.Log("Simple AI initialized");
    }
    
    private void Update()
    {
        if (navAgent == null || !navAgent.isOnNavMesh) return;
        
        DetectTarget();
        
        if (target != null)
        {
            ChaseTarget();
        }
        else if (isPatrolling)
        {
            Patrol();
        }
    }
    
    private void DetectTarget()
    {
        Collider[] colliders = Physics.OverlapSphere(transform.position, detectionRange);
        
        foreach (Collider col in colliders)
        {
            if (col.CompareTag("Player"))
            {
                target = col.transform;
                isPatrolling = false;
                break;
            }
        }
        
        if (target != null && Vector3.Distance(transform.position, target.position) > detectionRange * 1.5f)
        {
            target = null;
            isPatrolling = true;
        }
    }
    
    private void ChaseTarget()
    {
        if (target != null)
        {
            navAgent.SetDestination(target.position);
        }
    }
    
    private void Patrol()
    {
        if (patrolPoints.Length == 0) return;
        
        if (!navAgent.pathPending && navAgent.remainingDistance < 0.5f)
        {
            currentPatrolIndex = (currentPatrolIndex + 1) % patrolPoints.Length;
            navAgent.SetDestination(patrolPoints[currentPatrolIndex].position);
        }
    }
    
    private void OnDrawGizmosSelected()
    {
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, detectionRange);
        
        if (patrolPoints != null)
        {
            Gizmos.color = Color.blue;
            for (int i = 0; i < patrolPoints.Length; i++)
            {
                if (patrolPoints[i] != null)
                {
                    Gizmos.DrawWireSphere(patrolPoints[i].position, 1f);
                }
            }
        }
    }
}