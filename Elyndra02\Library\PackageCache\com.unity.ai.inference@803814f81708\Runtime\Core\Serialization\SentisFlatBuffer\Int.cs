// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>
#define ENABLE_SPAN_T
#define UNSAFE_BYTEBUFFER
#define BYTEBUFFER_NO_BOUNDS_CHECK

namespace SentisFlatBuffer
{

using global::System;
using global::System.Collections.Generic;
using global::Unity.InferenceEngine.Google.FlatBuffers;

struct Int : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_5_26(); }
  public static Int GetRootAsInt(ByteBuffer _bb) { return GetRootAsInt(_bb, new Int()); }
  public static Int GetRootAsInt(ByteBuffer _bb, Int obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, By<PERSON><PERSON><PERSON><PERSON> _bb) { __p = new Table(_i, _bb); }
  public Int __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public int IntVal { get { int o = __p.__offset(4); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }

  public static Offset<SentisFlatBuffer.Int> CreateInt(FlatBufferBuilder builder,
      int int_val = 0) {
    builder.StartTable(1);
    Int.AddIntVal(builder, int_val);
    return Int.EndInt(builder);
  }

  public static void StartInt(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddIntVal(FlatBufferBuilder builder, int intVal) { builder.AddInt(0, intVal, 0); }
  public static Offset<SentisFlatBuffer.Int> EndInt(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<SentisFlatBuffer.Int>(o);
  }
}


static class IntVerify
{
  static public bool Verify(Unity.InferenceEngine.Google.FlatBuffers.Verifier verifier, uint tablePos)
  {
    return verifier.VerifyTableStart(tablePos)
      && verifier.VerifyField(tablePos, 4 /*IntVal*/, 4 /*int*/, 4, false)
      && verifier.VerifyTableEnd(tablePos);
  }
}

}
