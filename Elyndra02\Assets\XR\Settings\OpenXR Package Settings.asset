%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-9071722438011490116
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 486b5e28864f9a94b979b9620ce5006d, type: 3}
  m_Name: ConformanceAutomationFeature Metro
  m_EditorClassIdentifier: Unity.XR.OpenXR.Features.ConformanceAutomation::UnityEngine.XR.OpenXR.Features.ConformanceAutomation.ConformanceAutomationFeature
  m_enabled: 0
  nameUi: Conformance Automation
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.conformance
  openxrExtensionStrings: XR_EXT_conformance_automation
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-9063101308175745777
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c5b5af5107e35a43818d5411328bfc3, type: 3}
  m_Name: DPadInteraction Android
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.DPadInteraction
  m_enabled: 0
  nameUi: D-Pad Binding
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.dpadinteraction
  openxrExtensionStrings: XR_KHR_binding_modification XR_EXT_dpad_binding
  company: Unity
  priority: 0
  required: 0
  forceThresholdLeft: 0.5
  forceThresholdReleaseLeft: 0.4
  centerRegionLeft: 0.5
  wedgeAngleLeft: 1.5707964
  isStickyLeft: 0
  forceThresholdRight: 0.5
  forceThresholdReleaseRight: 0.4
  centerRegionRight: 0.5
  wedgeAngleRight: 1.5707964
  isStickyRight: 0
  extensionStrings:
  - XR_KHR_binding_modification
  - XR_EXT_dpad_binding
--- !u!114 &-8853561329671338751
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b3cf79659a011bd419c7a2a30eb74e9a, type: 3}
  m_Name: EyeGazeInteraction Android
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.EyeGazeInteraction
  m_enabled: 0
  nameUi: Eye Gaze Interaction Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.eyetracking
  openxrExtensionStrings: XR_EXT_eye_gaze_interaction
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-8722676223645522548
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b5a1f07dc5afe854f9f12a4194aca3fb, type: 3}
  m_Name: WebGL
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.OpenXRSettings
  features:
  - {fileID: -2773865544480897148}
  m_renderMode: 1
  m_autoColorSubmissionMode: 1
  m_colorSubmissionModes:
    m_List: 00000000
  m_depthSubmissionMode: 0
  m_optimizeBufferDiscards: 0
  m_symmetricProjection: 0
  m_foveatedRenderingApi: 0
--- !u!114 &-8604564284569591517
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0f6bfdbcb316ed242b30a8798c9eb853, type: 3}
  m_Name: KHRSimpleControllerProfile Standalone
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.KHRSimpleControllerProfile
  m_enabled: 0
  nameUi: Khronos Simple Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.khrsimpleprofile
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-8597547088247178926
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f028123e2efe1d443875bc7609b4a98b, type: 3}
  m_Name: PalmPoseInteraction Standalone
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.PalmPoseInteraction
  m_enabled: 0
  nameUi: Palm Pose
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.palmpose
  openxrExtensionStrings: XR_EXT_palm_pose
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-8307080816009343457
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0f6bfdbcb316ed242b30a8798c9eb853, type: 3}
  m_Name: KHRSimpleControllerProfile Metro
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.KHRSimpleControllerProfile
  m_enabled: 0
  nameUi: Khronos Simple Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.khrsimpleprofile
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-8080313665118285920
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 056125dd64c0ed540b40a4af74f7b495, type: 3}
  m_Name: RuntimeDebuggerOpenXRFeature Standalone
  m_EditorClassIdentifier: Unity.XR.OpenXR.Features.RuntimeDebugger::UnityEngine.XR.OpenXR.Features.RuntimeDebugger.RuntimeDebuggerOpenXRFeature
  m_enabled: 0
  nameUi: Runtime Debugger
  version: 1
  featureIdInternal: com.unity.openxr.features.runtimedebugger
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
  cacheSize: 1048576
  perThreadCacheSize: 51200
--- !u!114 &-7639654918134472783
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f6a75d1f5ff90154ea2a8e58222a1f59, type: 3}
  m_Name: FoveatedRenderingFeature Standalone
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.FoveatedRenderingFeature
  m_enabled: 0
  nameUi: Foveated Rendering
  version: 1
  featureIdInternal: com.unity.openxr.feature.foveatedrendering
  openxrExtensionStrings: XR_UNITY_foveation XR_FB_foveation XR_FB_foveation_configuration
    XR_FB_swapchain_update_state XR_FB_foveation_vulkan
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-7298370368015244802
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7de993716e042c6499d0c18eed3a773c, type: 3}
  m_Name: MockRuntime Android
  m_EditorClassIdentifier: Unity.XR.OpenXR.Features.MockRuntime::UnityEngine.XR.OpenXR.Features.Mock.MockRuntime
  m_enabled: 0
  nameUi: Mock Runtime
  version: 0.0.2
  featureIdInternal: com.unity.openxr.feature.mockruntime
  openxrExtensionStrings: XR_UNITY_null_gfx XR_UNITY_android_present
  company: Unity
  priority: 0
  required: 0
  ignoreValidationErrors: 0
--- !u!114 &-7007062009026885335
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5019471fb2174e5c852ecd4047163007, type: 3}
  m_Name: HandInteractionProfile Standalone
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.HandInteractionProfile
  m_enabled: 0
  nameUi: Hand Interaction Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.handinteraction
  openxrExtensionStrings: XR_EXT_hand_interaction
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-7001963764414316062
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b213d3e3c7f3109449eb46a4c8ee42f0, type: 3}
  m_Name: XrPerformanceSettingsFeature Metro
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Extensions.PerformanceSettings.XrPerformanceSettingsFeature
  m_enabled: 0
  nameUi: XR Performance Settings
  version: 1.0.0
  featureIdInternal: com.unity.openxr.feature.extension.performance_settings
  openxrExtensionStrings: XR_EXT_performance_settings
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-5865469257042372692
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 274c02963f889a64e90bc2e596e21d13, type: 3}
  m_Name: HTCViveControllerProfile Standalone
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.HTCViveControllerProfile
  m_enabled: 0
  nameUi: HTC Vive Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.htcvive
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-5664140824911975718
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 761fdd4502cb7a84e9ec7a2b24f33f37, type: 3}
  m_Name: MicrosoftMotionControllerProfile Standalone
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.MicrosoftMotionControllerProfile
  m_enabled: 0
  nameUi: Microsoft Motion Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.microsoftmotioncontroller
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-5289996257055175421
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e5315f812f023cf4ebf26f7e5d2d70f2, type: 3}
  m_Name: HPReverbG2ControllerProfile Metro
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.HPReverbG2ControllerProfile
  m_enabled: 0
  nameUi: HP Reverb G2 Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.hpreverb
  openxrExtensionStrings: XR_EXT_hp_mixed_reality_controller
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-5272620634397097386
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9ef793c31862a37448e907829482ef80, type: 3}
  m_Name: OculusQuestFeature Android
  m_EditorClassIdentifier: Unity.XR.OpenXR.Features.OculusQuestSupport::UnityEngine.XR.OpenXR.Features.OculusQuestSupport.OculusQuestFeature
  m_enabled: 0
  nameUi: Oculus Quest Support
  version: 1.0.0
  featureIdInternal: com.unity.openxr.feature.oculusquest
  openxrExtensionStrings: XR_OCULUS_android_initialize_loader
  company: Unity
  priority: 0
  required: 0
  targetQuest: 1
  targetQuest2: 1
--- !u!114 &-5165472710495129567
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b3cf79659a011bd419c7a2a30eb74e9a, type: 3}
  m_Name: EyeGazeInteraction Standalone
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.EyeGazeInteraction
  m_enabled: 0
  nameUi: Eye Gaze Interaction Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.eyetracking
  openxrExtensionStrings: XR_EXT_eye_gaze_interaction
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-3922380763662512267
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c5b5af5107e35a43818d5411328bfc3, type: 3}
  m_Name: DPadInteraction Standalone
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.DPadInteraction
  m_enabled: 0
  nameUi: D-Pad Binding
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.dpadinteraction
  openxrExtensionStrings: XR_KHR_binding_modification XR_EXT_dpad_binding
  company: Unity
  priority: 0
  required: 0
  forceThresholdLeft: 0.5
  forceThresholdReleaseLeft: 0.4
  centerRegionLeft: 0.5
  wedgeAngleLeft: 1.5707964
  isStickyLeft: 0
  forceThresholdRight: 0.5
  forceThresholdReleaseRight: 0.4
  centerRegionRight: 0.5
  wedgeAngleRight: 1.5707964
  isStickyRight: 0
  extensionStrings:
  - XR_KHR_binding_modification
  - XR_EXT_dpad_binding
--- !u!114 &-3815209420876156056
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: feeef8d85de8db242bdda70cc7ff5acd, type: 3}
  m_Name: OculusTouchControllerProfile Standalone
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.OculusTouchControllerProfile
  m_enabled: 0
  nameUi: Oculus Touch Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.oculustouch
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-3687921554898823784
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f028123e2efe1d443875bc7609b4a98b, type: 3}
  m_Name: PalmPoseInteraction Android
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.PalmPoseInteraction
  m_enabled: 0
  nameUi: Palm Pose
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.palmpose
  openxrExtensionStrings: XR_EXT_palm_pose
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-2954405014581215592
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 056125dd64c0ed540b40a4af74f7b495, type: 3}
  m_Name: RuntimeDebuggerOpenXRFeature Metro
  m_EditorClassIdentifier: Unity.XR.OpenXR.Features.RuntimeDebugger::UnityEngine.XR.OpenXR.Features.RuntimeDebugger.RuntimeDebuggerOpenXRFeature
  m_enabled: 0
  nameUi: Runtime Debugger
  version: 1
  featureIdInternal: com.unity.openxr.features.runtimedebugger
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
  cacheSize: 1048576
  perThreadCacheSize: 51200
--- !u!114 &-2932461902726770245
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c4b862ee14fb479fbfe5fffe655d3ed3, type: 3}
  m_Name: MetaQuestTouchProControllerProfile Metro
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.MetaQuestTouchProControllerProfile
  m_enabled: 0
  nameUi: Meta Quest Touch Pro Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.metaquestpro
  openxrExtensionStrings: XR_FB_touch_controller_pro
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-2773865544480897148
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b213d3e3c7f3109449eb46a4c8ee42f0, type: 3}
  m_Name: XrPerformanceSettingsFeature WebGL
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Extensions.PerformanceSettings.XrPerformanceSettingsFeature
  m_enabled: 0
  nameUi: XR Performance Settings
  version: 1.0.0
  featureIdInternal: com.unity.openxr.feature.extension.performance_settings
  openxrExtensionStrings: XR_EXT_performance_settings
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-2663063290349964690
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f6a75d1f5ff90154ea2a8e58222a1f59, type: 3}
  m_Name: FoveatedRenderingFeature Metro
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.FoveatedRenderingFeature
  m_enabled: 0
  nameUi: Foveated Rendering
  version: 1
  featureIdInternal: com.unity.openxr.feature.foveatedrendering
  openxrExtensionStrings: XR_UNITY_foveation XR_FB_foveation XR_FB_foveation_configuration
    XR_FB_swapchain_update_state XR_FB_foveation_vulkan
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-1697010944141778570
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0d6ccd3d0ef0f1d458e69421dccbdae1, type: 3}
  m_Name: ValveIndexControllerProfile Standalone
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.ValveIndexControllerProfile
  m_enabled: 0
  nameUi: Valve Index Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.valveindex
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-1442913280668194613
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c4b862ee14fb479fbfe5fffe655d3ed3, type: 3}
  m_Name: MetaQuestTouchProControllerProfile Standalone
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.MetaQuestTouchProControllerProfile
  m_enabled: 0
  nameUi: Meta Quest Touch Pro Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.metaquestpro
  openxrExtensionStrings: XR_FB_touch_controller_pro
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-1414158743305144816
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b3cf79659a011bd419c7a2a30eb74e9a, type: 3}
  m_Name: EyeGazeInteraction Metro
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.EyeGazeInteraction
  m_enabled: 0
  nameUi: Eye Gaze Interaction Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.eyetracking
  openxrExtensionStrings: XR_EXT_eye_gaze_interaction
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-1192592798070028359
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b5a1f07dc5afe854f9f12a4194aca3fb, type: 3}
  m_Name: Standalone
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.OpenXRSettings
  features:
  - {fileID: 4068059245991711878}
  - {fileID: -3922380763662512267}
  - {fileID: -5165472710495129567}
  - {fileID: -7639654918134472783}
  - {fileID: 5965618994070184593}
  - {fileID: -7007062009026885335}
  - {fileID: 6409565724951125710}
  - {fileID: -5865469257042372692}
  - {fileID: -8604564284569591517}
  - {fileID: 1345416912966043866}
  - {fileID: -1442913280668194613}
  - {fileID: 6700583418693182871}
  - {fileID: -5664140824911975718}
  - {fileID: 8918419020461172867}
  - {fileID: -3815209420876156056}
  - {fileID: -8597547088247178926}
  - {fileID: -8080313665118285920}
  - {fileID: -1697010944141778570}
  - {fileID: -908099341926555646}
  m_renderMode: 1
  m_autoColorSubmissionMode: 1
  m_colorSubmissionModes:
    m_List: 00000000
  m_depthSubmissionMode: 0
  m_optimizeBufferDiscards: 0
  m_symmetricProjection: 0
  m_foveatedRenderingApi: 0
--- !u!114 &-908099341926555646
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b213d3e3c7f3109449eb46a4c8ee42f0, type: 3}
  m_Name: XrPerformanceSettingsFeature Standalone
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Extensions.PerformanceSettings.XrPerformanceSettingsFeature
  m_enabled: 0
  nameUi: XR Performance Settings
  version: 1.0.0
  featureIdInternal: com.unity.openxr.feature.extension.performance_settings
  openxrExtensionStrings: XR_EXT_performance_settings
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-890839401860086972
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2b7365b139f7aec43b23d26b7a48b5a6, type: 3}
  m_Name: MetaQuestTouchPlusControllerProfile Metro
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.MetaQuestTouchPlusControllerProfile
  m_enabled: 0
  nameUi: Meta Quest Touch Plus Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.metaquestplus
  openxrExtensionStrings: XR_META_touch_controller_plus
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-******************
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 761fdd4502cb7a84e9ec7a2b24f33f37, type: 3}
  m_Name: MicrosoftMotionControllerProfile Metro
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.MicrosoftMotionControllerProfile
  m_enabled: 0
  nameUi: Microsoft Motion Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.microsoftmotioncontroller
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-291267037096762504
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 056125dd64c0ed540b40a4af74f7b495, type: 3}
  m_Name: RuntimeDebuggerOpenXRFeature Android
  m_EditorClassIdentifier: Unity.XR.OpenXR.Features.RuntimeDebugger::UnityEngine.XR.OpenXR.Features.RuntimeDebugger.RuntimeDebuggerOpenXRFeature
  m_enabled: 0
  nameUi: Runtime Debugger
  version: 1
  featureIdInternal: com.unity.openxr.features.runtimedebugger
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
  cacheSize: 1048576
  perThreadCacheSize: 51200
--- !u!114 &-266069279656129245
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f928d0d73a35f294fbe357ca17aa3547, type: 3}
  m_Name: MicrosoftHandInteraction Android
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.MicrosoftHandInteraction
  m_enabled: 0
  nameUi: Microsoft Hand Interaction Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.handtracking
  openxrExtensionStrings: XR_MSFT_hand_interaction
  company: Unity
  priority: 0
  required: 0
--- !u!114 &-191136163449776178
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: feeef8d85de8db242bdda70cc7ff5acd, type: 3}
  m_Name: OculusTouchControllerProfile Android
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.OculusTouchControllerProfile
  m_enabled: 0
  nameUi: Oculus Touch Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.oculustouch
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9f0ebc320a151d3408ea1e9fce54d40e, type: 3}
  m_Name: OpenXR Package Settings
  m_EditorClassIdentifier: Unity.XR.OpenXR.Editor::UnityEditor.XR.OpenXR.OpenXRPackageSettings
  Keys: 0100000004000000070000000d0000000e000000
  Values:
  - {fileID: -1192592798070028359}
  - {fileID: *******************}
  - {fileID: 7486326653201892282}
  - {fileID: -8722676223645522548}
  - {fileID: 9105123503903098308}
--- !u!114 &533263605960200118
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f028123e2efe1d443875bc7609b4a98b, type: 3}
  m_Name: PalmPoseInteraction Metro
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.PalmPoseInteraction
  m_enabled: 0
  nameUi: Palm Pose
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.palmpose
  openxrExtensionStrings: XR_EXT_palm_pose
  company: Unity
  priority: 0
  required: 0
--- !u!114 &1345416912966043866
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2b7365b139f7aec43b23d26b7a48b5a6, type: 3}
  m_Name: MetaQuestTouchPlusControllerProfile Standalone
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.MetaQuestTouchPlusControllerProfile
  m_enabled: 0
  nameUi: Meta Quest Touch Plus Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.metaquestplus
  openxrExtensionStrings: XR_META_touch_controller_plus
  company: Unity
  priority: 0
  required: 0
--- !u!114 &1999288744007354579
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0d6ccd3d0ef0f1d458e69421dccbdae1, type: 3}
  m_Name: ValveIndexControllerProfile Metro
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.ValveIndexControllerProfile
  m_enabled: 0
  nameUi: Valve Index Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.valveindex
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
--- !u!114 &2533116505001888915
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f6a75d1f5ff90154ea2a8e58222a1f59, type: 3}
  m_Name: FoveatedRenderingFeature Android
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.FoveatedRenderingFeature
  m_enabled: 0
  nameUi: Foveated Rendering
  version: 1
  featureIdInternal: com.unity.openxr.feature.foveatedrendering
  openxrExtensionStrings: XR_UNITY_foveation XR_FB_foveation XR_FB_foveation_configuration
    XR_FB_swapchain_update_state XR_FB_foveation_vulkan
  company: Unity
  priority: 0
  required: 0
--- !u!114 &2866732040440588947
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2a24be4b5ebfe5f4d8ed1de9b25cb7aa, type: 3}
  m_Name: HandCommonPosesInteraction Android
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.HandCommonPosesInteraction
  m_enabled: 0
  nameUi: Hand Interaction Poses
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.handinteractionposes
  openxrExtensionStrings: XR_EXT_hand_interaction
  company: Unity
  priority: 0
  required: 0
--- !u!114 &3270633091156151966
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5019471fb2174e5c852ecd4047163007, type: 3}
  m_Name: HandInteractionProfile Android
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.HandInteractionProfile
  m_enabled: 0
  nameUi: Hand Interaction Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.handinteraction
  openxrExtensionStrings: XR_EXT_hand_interaction
  company: Unity
  priority: 0
  required: 0
--- !u!114 &4068059245991711878
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 486b5e28864f9a94b979b9620ce5006d, type: 3}
  m_Name: ConformanceAutomationFeature Standalone
  m_EditorClassIdentifier: Unity.XR.OpenXR.Features.ConformanceAutomation::UnityEngine.XR.OpenXR.Features.ConformanceAutomation.ConformanceAutomationFeature
  m_enabled: 0
  nameUi: Conformance Automation
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.conformance
  openxrExtensionStrings: XR_EXT_conformance_automation
  company: Unity
  priority: 0
  required: 0
--- !u!114 &*******************
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f647cc0545697264a9878224faada6d5, type: 3}
  m_Name: MetaQuestFeature Android
  m_EditorClassIdentifier: Unity.XR.OpenXR.Features.MetaQuestSupport::UnityEngine.XR.OpenXR.Features.MetaQuestSupport.MetaQuestFeature
  m_enabled: 0
  nameUi: Meta Quest Support
  version: 1.0.0
  featureIdInternal: com.unity.openxr.feature.metaquest
  openxrExtensionStrings: XR_OCULUS_android_initialize_loader
  company: Unity
  priority: 0
  required: 0
  targetDevices:
  - visibleName: Quest
    manifestName: quest
    enabled: 1
  - visibleName: Quest 2
    manifestName: quest2
    enabled: 1
  - visibleName: Quest Pro
    manifestName: cambria
    enabled: 1
  - visibleName: Quest 3
    manifestName: eureka
    enabled: 1
  forceRemoveInternetPermission: 0
  symmetricProjection: 0
  foveatedRenderingApi: 0
  systemSplashScreen: {fileID: 0}
  optimizeBufferDiscards: 1
  lateLatchingMode: 0
  lateLatchingDebug: 0
--- !u!114 &*******************
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5019471fb2174e5c852ecd4047163007, type: 3}
  m_Name: HandInteractionProfile Metro
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.HandInteractionProfile
  m_enabled: 0
  nameUi: Hand Interaction Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.handinteraction
  openxrExtensionStrings: XR_EXT_hand_interaction
  company: Unity
  priority: 0
  required: 0
--- !u!114 &*******************
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b5a1f07dc5afe854f9f12a4194aca3fb, type: 3}
  m_Name: iPhone
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.OpenXRSettings
  features:
  - {fileID: 5218177330188669152}
  m_renderMode: 1
  m_autoColorSubmissionMode: 1
  m_colorSubmissionModes:
    m_List: 00000000
  m_depthSubmissionMode: 0
  m_optimizeBufferDiscards: 0
  m_symmetricProjection: 0
  m_foveatedRenderingApi: 0
--- !u!114 &*******************
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 274c02963f889a64e90bc2e596e21d13, type: 3}
  m_Name: HTCViveControllerProfile Metro
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.HTCViveControllerProfile
  m_enabled: 0
  nameUi: HTC Vive Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.htcvive
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
--- !u!114 &5218177330188669152
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b213d3e3c7f3109449eb46a4c8ee42f0, type: 3}
  m_Name: XrPerformanceSettingsFeature iPhone
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Extensions.PerformanceSettings.XrPerformanceSettingsFeature
  m_enabled: 0
  nameUi: XR Performance Settings
  version: 1.0.0
  featureIdInternal: com.unity.openxr.feature.extension.performance_settings
  openxrExtensionStrings: XR_EXT_performance_settings
  company: Unity
  priority: 0
  required: 0
--- !u!114 &5319207346261618482
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 486b5e28864f9a94b979b9620ce5006d, type: 3}
  m_Name: ConformanceAutomationFeature Android
  m_EditorClassIdentifier: Unity.XR.OpenXR.Features.ConformanceAutomation::UnityEngine.XR.OpenXR.Features.ConformanceAutomation.ConformanceAutomationFeature
  m_enabled: 0
  nameUi: Conformance Automation
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.conformance
  openxrExtensionStrings: XR_EXT_conformance_automation
  company: Unity
  priority: 0
  required: 0
--- !u!114 &5511370303684768121
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c5b5af5107e35a43818d5411328bfc3, type: 3}
  m_Name: DPadInteraction Metro
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.DPadInteraction
  m_enabled: 0
  nameUi: D-Pad Binding
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.dpadinteraction
  openxrExtensionStrings: XR_KHR_binding_modification XR_EXT_dpad_binding
  company: Unity
  priority: 0
  required: 0
  forceThresholdLeft: 0.5
  forceThresholdReleaseLeft: 0.4
  centerRegionLeft: 0.5
  wedgeAngleLeft: 1.5707964
  isStickyLeft: 0
  forceThresholdRight: 0.5
  forceThresholdReleaseRight: 0.4
  centerRegionRight: 0.5
  wedgeAngleRight: 1.5707964
  isStickyRight: 0
  extensionStrings:
  - XR_KHR_binding_modification
  - XR_EXT_dpad_binding
--- !u!114 &5965618994070184593
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2a24be4b5ebfe5f4d8ed1de9b25cb7aa, type: 3}
  m_Name: HandCommonPosesInteraction Standalone
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.HandCommonPosesInteraction
  m_enabled: 0
  nameUi: Hand Interaction Poses
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.handinteractionposes
  openxrExtensionStrings: XR_EXT_hand_interaction
  company: Unity
  priority: 0
  required: 0
--- !u!114 &6070675274626678877
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f928d0d73a35f294fbe357ca17aa3547, type: 3}
  m_Name: MicrosoftHandInteraction Metro
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.MicrosoftHandInteraction
  m_enabled: 0
  nameUi: Microsoft Hand Interaction Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.handtracking
  openxrExtensionStrings: XR_MSFT_hand_interaction
  company: Unity
  priority: 0
  required: 0
--- !u!114 &6304870035552678362
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: feeef8d85de8db242bdda70cc7ff5acd, type: 3}
  m_Name: OculusTouchControllerProfile Metro
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.OculusTouchControllerProfile
  m_enabled: 0
  nameUi: Oculus Touch Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.oculustouch
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
--- !u!114 &6346184199619478059
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2a24be4b5ebfe5f4d8ed1de9b25cb7aa, type: 3}
  m_Name: HandCommonPosesInteraction Metro
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.HandCommonPosesInteraction
  m_enabled: 0
  nameUi: Hand Interaction Poses
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.handinteractionposes
  openxrExtensionStrings: XR_EXT_hand_interaction
  company: Unity
  priority: 0
  required: 0
--- !u!114 &6409565724951125710
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e5315f812f023cf4ebf26f7e5d2d70f2, type: 3}
  m_Name: HPReverbG2ControllerProfile Standalone
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.HPReverbG2ControllerProfile
  m_enabled: 0
  nameUi: HP Reverb G2 Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.hpreverb
  openxrExtensionStrings: XR_EXT_hp_mixed_reality_controller
  company: Unity
  priority: 0
  required: 0
--- !u!114 &6700583418693182871
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f928d0d73a35f294fbe357ca17aa3547, type: 3}
  m_Name: MicrosoftHandInteraction Standalone
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.MicrosoftHandInteraction
  m_enabled: 0
  nameUi: Microsoft Hand Interaction Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.handtracking
  openxrExtensionStrings: XR_MSFT_hand_interaction
  company: Unity
  priority: 0
  required: 0
--- !u!114 &6841866676431349355
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b213d3e3c7f3109449eb46a4c8ee42f0, type: 3}
  m_Name: XrPerformanceSettingsFeature Android
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Extensions.PerformanceSettings.XrPerformanceSettingsFeature
  m_enabled: 0
  nameUi: XR Performance Settings
  version: 1.0.0
  featureIdInternal: com.unity.openxr.feature.extension.performance_settings
  openxrExtensionStrings: XR_EXT_performance_settings
  company: Unity
  priority: 0
  required: 0
--- !u!114 &7486326653201892282
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b5a1f07dc5afe854f9f12a4194aca3fb, type: 3}
  m_Name: Android
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.OpenXRSettings
  features:
  - {fileID: 5319207346261618482}
  - {fileID: -9063101308175745777}
  - {fileID: -8853561329671338751}
  - {fileID: 2533116505001888915}
  - {fileID: 2866732040440588947}
  - {fileID: 3270633091156151966}
  - {fileID: 9125976477930568300}
  - {fileID: *******************}
  - {fileID: 8401136006716063580}
  - {fileID: 9197777341013583565}
  - {fileID: -266069279656129245}
  - {fileID: -7298370368015244802}
  - {fileID: -5272620634397097386}
  - {fileID: -191136163449776178}
  - {fileID: -3687921554898823784}
  - {fileID: -291267037096762504}
  - {fileID: 6841866676431349355}
  m_renderMode: 1
  m_autoColorSubmissionMode: 1
  m_colorSubmissionModes:
    m_List: 00000000
  m_depthSubmissionMode: 0
  m_optimizeBufferDiscards: 0
  m_symmetricProjection: 0
  m_foveatedRenderingApi: 0
--- !u!114 &8401136006716063580
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2b7365b139f7aec43b23d26b7a48b5a6, type: 3}
  m_Name: MetaQuestTouchPlusControllerProfile Android
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.MetaQuestTouchPlusControllerProfile
  m_enabled: 0
  nameUi: Meta Quest Touch Plus Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.metaquestplus
  openxrExtensionStrings: XR_META_touch_controller_plus
  company: Unity
  priority: 0
  required: 0
--- !u!114 &8918419020461172867
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7de993716e042c6499d0c18eed3a773c, type: 3}
  m_Name: MockRuntime Standalone
  m_EditorClassIdentifier: Unity.XR.OpenXR.Features.MockRuntime::UnityEngine.XR.OpenXR.Features.Mock.MockRuntime
  m_enabled: 0
  nameUi: Mock Runtime
  version: 0.0.2
  featureIdInternal: com.unity.openxr.feature.mockruntime
  openxrExtensionStrings: XR_UNITY_null_gfx XR_UNITY_android_present
  company: Unity
  priority: 0
  required: 0
  ignoreValidationErrors: 0
--- !u!114 &9105123503903098308
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b5a1f07dc5afe854f9f12a4194aca3fb, type: 3}
  m_Name: Metro
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.OpenXRSettings
  features:
  - {fileID: -9071722438011490116}
  - {fileID: 5511370303684768121}
  - {fileID: -1414158743305144816}
  - {fileID: -2663063290349964690}
  - {fileID: 6346184199619478059}
  - {fileID: *******************}
  - {fileID: -5289996257055175421}
  - {fileID: *******************}
  - {fileID: -8307080816009343457}
  - {fileID: -890839401860086972}
  - {fileID: -2932461902726770245}
  - {fileID: 6070675274626678877}
  - {fileID: -******************}
  - {fileID: 6304870035552678362}
  - {fileID: 533263605960200118}
  - {fileID: -2954405014581215592}
  - {fileID: 1999288744007354579}
  - {fileID: -7001963764414316062}
  m_renderMode: 1
  m_autoColorSubmissionMode: 1
  m_colorSubmissionModes:
    m_List: 00000000
  m_depthSubmissionMode: 0
  m_optimizeBufferDiscards: 0
  m_symmetricProjection: 0
  m_foveatedRenderingApi: 0
--- !u!114 &9125976477930568300
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0f6bfdbcb316ed242b30a8798c9eb853, type: 3}
  m_Name: KHRSimpleControllerProfile Android
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.KHRSimpleControllerProfile
  m_enabled: 0
  nameUi: Khronos Simple Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.khrsimpleprofile
  openxrExtensionStrings: 
  company: Unity
  priority: 0
  required: 0
--- !u!114 &9197777341013583565
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c4b862ee14fb479fbfe5fffe655d3ed3, type: 3}
  m_Name: MetaQuestTouchProControllerProfile Android
  m_EditorClassIdentifier: Unity.XR.OpenXR::UnityEngine.XR.OpenXR.Features.Interactions.MetaQuestTouchProControllerProfile
  m_enabled: 0
  nameUi: Meta Quest Touch Pro Controller Profile
  version: 0.0.1
  featureIdInternal: com.unity.openxr.feature.input.metaquestpro
  openxrExtensionStrings: XR_FB_touch_controller_pro
  company: Unity
  priority: 0
  required: 0
