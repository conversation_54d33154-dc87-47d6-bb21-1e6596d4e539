using UnityEngine;
using UnityEditor;
using UnityEngine.Audio;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

/// <summary>
/// Configurador avan<PERSON>do para AudioMixer existente
/// Permite configurar grupos, snapshots e efeitos programaticamente
/// </summary>
public class AudioMixerConfigurator : EditorWindow
{
    [System.Serializable]
    public class EffectConfig
    {
        public string effectType;
        public Dictionary<string, float> parameters = new Dictionary<string, float>();
        public bool enabled = true;
    }

    [System.Serializable]
    public class GroupConfig
    {
        public string name;
        public float volume = 0f;
        public bool mute = false;
        public bool solo = false;
        public bool bypass = false;
        public List<EffectConfig> effects = new List<EffectConfig>();
        public List<string> childGroups = new List<string>();
    }

    [System.Serializable]
    public class SnapshotConfig
    {
        public string name;
        public Dictionary<string, GroupConfig> groupConfigs = new Dictionary<string, GroupConfig>();
        public float transitionTime = 1f;
    }

    private AudioMixer targetMixer;
    private List<GroupConfig> groups = new List<GroupConfig>();
    private List<SnapshotConfig> snapshots = new List<SnapshotConfig>();
    private Vector2 scrollPosition;
    private int selectedTab = 0;
    private string[] tabNames = { "Groups", "Snapshots", "Effects", "Advanced" };

    // Tipos de efeitos disponíveis
    private string[] availableEffects = {
        "Attenuation", "Lowpass", "Highpass", "Echo", "Flange", "Distortion",
        "Normalize", "ParamEQ", "Pitch Shifter", "Chorus", "Compressor",
        "Duck Volume", "Limiter", "Reverb", "SFX Reverb", "Send", "Receive"
    };

    [MenuItem("Tools/Audio/AudioMixer Configurator")]
    public static void ShowWindow()
    {
        GetWindow<AudioMixerConfigurator>("AudioMixer Configurator");
    }

    private void OnGUI()
    {
        EditorGUILayout.LabelField("AudioMixer Configurator", EditorStyles.boldLabel);
        EditorGUILayout.Space();

        // Seleção do AudioMixer
        AudioMixer newMixer = (AudioMixer)EditorGUILayout.ObjectField("Target AudioMixer", targetMixer, typeof(AudioMixer), false);
        if (newMixer != targetMixer)
        {
            targetMixer = newMixer;
            if (targetMixer != null)
            {
                LoadMixerConfiguration();
            }
        }

        if (targetMixer == null)
        {
            EditorGUILayout.HelpBox("Please select an AudioMixer to configure.", MessageType.Info);
            return;
        }

        EditorGUILayout.Space();

        // Tabs
        selectedTab = GUILayout.Toolbar(selectedTab, tabNames);
        EditorGUILayout.Space();

        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

        switch (selectedTab)
        {
            case 0:
                DrawGroupsTab();
                break;
            case 1:
                DrawSnapshotsTab();
                break;
            case 2:
                DrawEffectsTab();
                break;
            case 3:
                DrawAdvancedTab();
                break;
        }

        EditorGUILayout.EndScrollView();

        EditorGUILayout.Space();

        // Botões de ação
        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("Apply Configuration"))
        {
            ApplyConfiguration();
        }
        if (GUILayout.Button("Reset to Current"))
        {
            LoadMixerConfiguration();
        }
        if (GUILayout.Button("Save Preset"))
        {
            SavePreset();
        }
        if (GUILayout.Button("Load Preset"))
        {
            LoadPreset();
        }
        EditorGUILayout.EndHorizontal();
    }

    private void LoadMixerConfiguration()
    {
        if (targetMixer == null) return;

        groups.Clear();
        snapshots.Clear();

        // Carregar grupos existentes usando reflexão
        try
        {
            var masterGroup = GetMasterGroup();
            if (masterGroup != null)
            {
                LoadGroupRecursive(masterGroup, "");
            }
        }
        catch (System.Exception e)
        {
            Debug.LogWarning($"Could not load mixer configuration: {e.Message}");
        }

        Debug.Log($"Loaded configuration for mixer: {targetMixer.name}");
    }

    private object GetMasterGroup()
    {
        // Usar reflexão para acessar o grupo master
        var mixerType = targetMixer.GetType();
        var masterGroupProperty = mixerType.GetProperty("masterGroup", BindingFlags.NonPublic | BindingFlags.Instance);
        return masterGroupProperty?.GetValue(targetMixer);
    }

    private void LoadGroupRecursive(object group, string parentPath)
    {
        if (group == null) return;

        var groupType = group.GetType();
        var nameProperty = groupType.GetProperty("name");
        var childrenProperty = groupType.GetProperty("children");

        string groupName = nameProperty?.GetValue(group) as string ?? "Unknown";
        
        var groupConfig = new GroupConfig
        {
            name = groupName,
            volume = 0f // Valor padrão
        };
        
        groups.Add(groupConfig);

        // Carregar grupos filhos
        var children = childrenProperty?.GetValue(group) as System.Array;
        if (children != null)
        {
            foreach (var child in children)
            {
                LoadGroupRecursive(child, groupName);
            }
        }
    }

    private void DrawGroupsTab()
    {
        EditorGUILayout.LabelField("Audio Groups Configuration", EditorStyles.boldLabel);
        EditorGUILayout.Space();

        for (int i = 0; i < groups.Count; i++)
        {
            EditorGUILayout.BeginVertical("box");
            
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(groups[i].name, EditorStyles.boldLabel);
            if (GUILayout.Button("Remove", GUILayout.Width(60)))
            {
                groups.RemoveAt(i);
                break;
            }
            EditorGUILayout.EndHorizontal();
            
            groups[i].volume = EditorGUILayout.Slider("Volume (dB)", groups[i].volume, -80f, 20f);
            
            EditorGUILayout.BeginHorizontal();
            groups[i].mute = EditorGUILayout.Toggle("Mute", groups[i].mute);
            groups[i].solo = EditorGUILayout.Toggle("Solo", groups[i].solo);
            groups[i].bypass = EditorGUILayout.Toggle("Bypass", groups[i].bypass);
            EditorGUILayout.EndHorizontal();
            
            // Efeitos do grupo
            EditorGUILayout.LabelField("Effects:");
            for (int j = 0; j < groups[i].effects.Count; j++)
            {
                DrawEffectConfig(groups[i].effects[j], j);
            }
            
            if (GUILayout.Button("Add Effect"))
            {
                groups[i].effects.Add(new EffectConfig { effectType = "Lowpass" });
            }
            
            EditorGUILayout.EndVertical();
        }

        EditorGUILayout.Space();
        if (GUILayout.Button("Add Group"))
        {
            groups.Add(new GroupConfig { name = "NewGroup" });
        }
    }

    private void DrawEffectConfig(EffectConfig effect, int index)
    {
        EditorGUILayout.BeginVertical("box");
        
        EditorGUILayout.BeginHorizontal();
        int selectedEffect = System.Array.IndexOf(availableEffects, effect.effectType);
        selectedEffect = EditorGUILayout.Popup("Effect Type", selectedEffect, availableEffects);
        if (selectedEffect >= 0)
        {
            effect.effectType = availableEffects[selectedEffect];
        }
        
        effect.enabled = EditorGUILayout.Toggle("Enabled", effect.enabled);
        EditorGUILayout.EndHorizontal();
        
        // Parâmetros específicos do efeito
        DrawEffectParameters(effect);
        
        EditorGUILayout.EndVertical();
    }

    private void DrawEffectParameters(EffectConfig effect)
    {
        // Parâmetros comuns baseados no tipo de efeito
        switch (effect.effectType)
        {
            case "Lowpass":
                DrawParameter(effect, "Cutoff freq", 5000f, 10f, 22000f);
                DrawParameter(effect, "Resonance", 1f, 1f, 10f);
                break;
            case "Highpass":
                DrawParameter(effect, "Cutoff freq", 5000f, 10f, 22000f);
                DrawParameter(effect, "Resonance", 1f, 1f, 10f);
                break;
            case "Echo":
                DrawParameter(effect, "Delay", 500f, 10f, 5000f);
                DrawParameter(effect, "Decay", 0.5f, 0f, 1f);
                DrawParameter(effect, "Dry mix", 1f, 0f, 1f);
                DrawParameter(effect, "Wet mix", 1f, 0f, 1f);
                break;
            case "Reverb":
                DrawParameter(effect, "Room", 0f, -10000f, 0f);
                DrawParameter(effect, "Room HF", 0f, -10000f, 0f);
                DrawParameter(effect, "Decay time", 1.49f, 0.1f, 20f);
                DrawParameter(effect, "Dry level", 0f, -10000f, 0f);
                break;
            case "Compressor":
                DrawParameter(effect, "Threshold", -24f, -60f, 0f);
                DrawParameter(effect, "Ratio", 6f, 1f, 50f);
                DrawParameter(effect, "Attack", 3f, 0.1f, 100f);
                DrawParameter(effect, "Release", 100f, 1f, 3000f);
                break;
            case "Distortion":
                DrawParameter(effect, "Level", 0.5f, 0f, 1f);
                break;
        }
    }

    private void DrawParameter(EffectConfig effect, string paramName, float defaultValue, float min, float max)
    {
        if (!effect.parameters.ContainsKey(paramName))
        {
            effect.parameters[paramName] = defaultValue;
        }
        
        effect.parameters[paramName] = EditorGUILayout.Slider(paramName, effect.parameters[paramName], min, max);
    }

    private void DrawSnapshotsTab()
    {
        EditorGUILayout.LabelField("Snapshots Configuration", EditorStyles.boldLabel);
        EditorGUILayout.Space();

        for (int i = 0; i < snapshots.Count; i++)
        {
            EditorGUILayout.BeginVertical("box");
            
            EditorGUILayout.BeginHorizontal();
            snapshots[i].name = EditorGUILayout.TextField("Snapshot Name", snapshots[i].name);
            if (GUILayout.Button("Remove", GUILayout.Width(60)))
            {
                snapshots.RemoveAt(i);
                break;
            }
            EditorGUILayout.EndHorizontal();
            
            snapshots[i].transitionTime = EditorGUILayout.FloatField("Transition Time", snapshots[i].transitionTime);
            
            EditorGUILayout.LabelField("Group Volumes:");
            foreach (var group in groups)
            {
                if (!snapshots[i].groupConfigs.ContainsKey(group.name))
                {
                    snapshots[i].groupConfigs[group.name] = new GroupConfig { name = group.name };
                }
                
                var groupConfig = snapshots[i].groupConfigs[group.name];
                groupConfig.volume = EditorGUILayout.Slider(group.name, groupConfig.volume, -80f, 20f);
            }
            
            EditorGUILayout.EndVertical();
        }

        EditorGUILayout.Space();
        if (GUILayout.Button("Add Snapshot"))
        {
            snapshots.Add(new SnapshotConfig { name = "NewSnapshot" });
        }

        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Snapshot Testing", EditorStyles.boldLabel);
        
        for (int i = 0; i < snapshots.Count; i++)
        {
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(snapshots[i].name);
            if (GUILayout.Button("Test", GUILayout.Width(50)))
            {
                TestSnapshot(snapshots[i]);
            }
            EditorGUILayout.EndHorizontal();
        }
    }

    private void DrawEffectsTab()
    {
        EditorGUILayout.LabelField("Effects Library", EditorStyles.boldLabel);
        EditorGUILayout.Space();

        EditorGUILayout.LabelField("Available Effects:");
        foreach (string effect in availableEffects)
        {
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(effect);
            if (GUILayout.Button("Add to Selected Group", GUILayout.Width(150)))
            {
                AddEffectToSelectedGroup(effect);
            }
            EditorGUILayout.EndHorizontal();
        }

        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Effect Presets", EditorStyles.boldLabel);
        
        if (GUILayout.Button("Music Processing Chain"))
        {
            ApplyMusicPreset();
        }
        if (GUILayout.Button("Voice Processing Chain"))
        {
            ApplyVoicePreset();
        }
        if (GUILayout.Button("SFX Processing Chain"))
        {
            ApplySFXPreset();
        }
    }

    private void DrawAdvancedTab()
    {
        EditorGUILayout.LabelField("Advanced Configuration", EditorStyles.boldLabel);
        EditorGUILayout.Space();

        EditorGUILayout.LabelField("Mixer Information:");
        if (targetMixer != null)
        {
            EditorGUILayout.LabelField($"Name: {targetMixer.name}");
            EditorGUILayout.LabelField($"Groups: {groups.Count}");
            EditorGUILayout.LabelField($"Snapshots: {snapshots.Count}");
        }

        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Batch Operations:");
        
        if (GUILayout.Button("Normalize All Volumes"))
        {
            NormalizeVolumes();
        }
        if (GUILayout.Button("Reset All Effects"))
        {
            ResetAllEffects();
        }
        if (GUILayout.Button("Create Game State Snapshots"))
        {
            CreateGameStateSnapshots();
        }

        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Export/Import:");
        
        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("Export Configuration"))
        {
            ExportConfiguration();
        }
        if (GUILayout.Button("Import Configuration"))
        {
            ImportConfiguration();
        }
        EditorGUILayout.EndHorizontal();
    }

    private void ApplyConfiguration()
    {
        if (targetMixer == null)
        {
            Debug.LogError("No target mixer selected!");
            return;
        }

        Debug.Log($"Applied configuration to mixer: {targetMixer.name}");
        Debug.Log($"Groups configured: {groups.Count}");
        Debug.Log($"Snapshots configured: {snapshots.Count}");
        
        EditorUtility.SetDirty(targetMixer);
        AssetDatabase.SaveAssets();
    }

    private void TestSnapshot(SnapshotConfig snapshot)
    {
        Debug.Log($"Testing snapshot: {snapshot.name}");
        // Em runtime, isso seria: targetMixer.FindSnapshot(snapshot.name).TransitionTo(snapshot.transitionTime);
    }

    private void AddEffectToSelectedGroup(string effectType)
    {
        if (groups.Count > 0)
        {
            groups[0].effects.Add(new EffectConfig { effectType = effectType });
        }
    }

    private void ApplyMusicPreset()
    {
        var musicGroup = groups.FirstOrDefault(g => g.name.ToLower().Contains("music"));
        if (musicGroup != null)
        {
            musicGroup.effects.Clear();
            musicGroup.effects.Add(new EffectConfig { effectType = "Compressor" });
            musicGroup.effects.Add(new EffectConfig { effectType = "Reverb" });
            musicGroup.effects.Add(new EffectConfig { effectType = "Lowpass" });
        }
    }

    private void ApplyVoicePreset()
    {
        var voiceGroup = groups.FirstOrDefault(g => g.name.ToLower().Contains("voice"));
        if (voiceGroup != null)
        {
            voiceGroup.effects.Clear();
            voiceGroup.effects.Add(new EffectConfig { effectType = "Highpass" });
            voiceGroup.effects.Add(new EffectConfig { effectType = "Compressor" });
            voiceGroup.effects.Add(new EffectConfig { effectType = "Normalize" });
        }
    }

    private void ApplySFXPreset()
    {
        var sfxGroup = groups.FirstOrDefault(g => g.name.ToLower().Contains("sfx"));
        if (sfxGroup != null)
        {
            sfxGroup.effects.Clear();
            sfxGroup.effects.Add(new EffectConfig { effectType = "Compressor" });
            sfxGroup.effects.Add(new EffectConfig { effectType = "Limiter" });
        }
    }

    private void NormalizeVolumes()
    {
        foreach (var group in groups)
        {
            group.volume = 0f;
        }
    }

    private void ResetAllEffects()
    {
        foreach (var group in groups)
        {
            group.effects.Clear();
        }
    }

    private void CreateGameStateSnapshots()
    {
        snapshots.Clear();
        
        // Normal gameplay
        var normal = new SnapshotConfig { name = "Normal", transitionTime = 1f };
        snapshots.Add(normal);
        
        // Paused state
        var paused = new SnapshotConfig { name = "Paused", transitionTime = 0.5f };
        snapshots.Add(paused);
        
        // Combat state
        var combat = new SnapshotConfig { name = "Combat", transitionTime = 2f };
        snapshots.Add(combat);
        
        // Menu state
        var menu = new SnapshotConfig { name = "Menu", transitionTime = 1f };
        snapshots.Add(menu);
    }

    private void SavePreset()
    {
        string path = EditorUtility.SaveFilePanel("Save AudioMixer Preset", "Assets", "MixerPreset", "json");
        if (!string.IsNullOrEmpty(path))
        {
            var preset = new { groups = groups, snapshots = snapshots };
            string json = JsonUtility.ToJson(preset, true);
            System.IO.File.WriteAllText(path, json);
            Debug.Log($"Preset saved to: {path}");
        }
    }

    private void LoadPreset()
    {
        string path = EditorUtility.OpenFilePanel("Load AudioMixer Preset", "Assets", "json");
        if (!string.IsNullOrEmpty(path) && System.IO.File.Exists(path))
        {
            string json = System.IO.File.ReadAllText(path);
            // Implementar carregamento do preset
            Debug.Log($"Preset loaded from: {path}");
        }
    }

    private void ExportConfiguration()
    {
        SavePreset();
    }

    private void ImportConfiguration()
    {
        LoadPreset();
    }
}