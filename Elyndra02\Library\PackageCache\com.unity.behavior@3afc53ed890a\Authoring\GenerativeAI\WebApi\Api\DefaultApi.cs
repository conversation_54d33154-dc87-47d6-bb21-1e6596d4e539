/*
 * Muse API
 *
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.2.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Net;
using System.Net.Mime;
using Unity.Behavior.WebApi.Client;
using Unity.Behavior.WebApi.Model;

namespace Unity.Behavior.WebApi.Api
{

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    internal interface IDefaultApiSync : IApiAccessor
    {
        #region Synchronous Operations
        /// <summary>
        /// Action Code Repair
        /// </summary>
        /// <remarks>
        /// Agent action code repairing route for repairing generated csharp scripts.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actionCodeRepairRequest"></param>
        /// <returns>Object</returns>
        [Obsolete]
        Object ActionCodeRepairMuseAgentCodeRepairPost(ActionCodeRepairRequest actionCodeRepairRequest);

        /// <summary>
        /// Action Code Repair
        /// </summary>
        /// <remarks>
        /// Agent action code repairing route for repairing generated csharp scripts.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actionCodeRepairRequest"></param>
        /// <returns>ApiResponse of Object</returns>
        [Obsolete]
        ApiResponse<Object> ActionCodeRepairMuseAgentCodeRepairPostWithHttpInfo(ActionCodeRepairRequest actionCodeRepairRequest);
        /// <summary>
        /// Action Code Repair
        /// </summary>
        /// <remarks>
        /// Agent action code repairing route for repairing generated csharp scripts.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actionCodeRepairRequest"></param>
        /// <returns>Object</returns>
        Object ActionCodeRepairV1MuseAgentCodeRepairPost(ActionCodeRepairRequest actionCodeRepairRequest);

        /// <summary>
        /// Action Code Repair
        /// </summary>
        /// <remarks>
        /// Agent action code repairing route for repairing generated csharp scripts.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actionCodeRepairRequest"></param>
        /// <returns>ApiResponse of Object</returns>
        ApiResponse<Object> ActionCodeRepairV1MuseAgentCodeRepairPostWithHttpInfo(ActionCodeRepairRequest actionCodeRepairRequest);
        /// <summary>
        /// Action
        /// </summary>
        /// <remarks>
        /// Agent action route for performing actions in the editor.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actionRequest"></param>
        /// <returns>Object</returns>
        [Obsolete]
        Object ActionMuseAgentActionPost(ActionRequest actionRequest);

        /// <summary>
        /// Action
        /// </summary>
        /// <remarks>
        /// Agent action route for performing actions in the editor.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actionRequest"></param>
        /// <returns>ApiResponse of Object</returns>
        [Obsolete]
        ApiResponse<Object> ActionMuseAgentActionPostWithHttpInfo(ActionRequest actionRequest);
        /// <summary>
        /// Action
        /// </summary>
        /// <remarks>
        /// Agent action route for performing actions in the editor.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actionRequest"></param>
        /// <returns>Object</returns>
        Object ActionV1MuseAgentActionPost(ActionRequest actionRequest);

        /// <summary>
        /// Action
        /// </summary>
        /// <remarks>
        /// Agent action route for performing actions in the editor.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actionRequest"></param>
        /// <returns>ApiResponse of Object</returns>
        ApiResponse<Object> ActionV1MuseAgentActionPostWithHttpInfo(ActionRequest actionRequest);
        /// <summary>
        /// Chat
        /// </summary>
        /// <remarks>
        /// Chat with Muse.  Args:     request (Request): FastAPI request object.     body (ChatRequest): Chat request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     StreamingResponse | ChatResponse | JSONResponse:         Either streaming response, at-once chat response, or JSON error message.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="chatRequest"></param>
        /// <returns>Object</returns>
        [Obsolete]
        Object ChatMuseChatPost(ChatRequest chatRequest);

        /// <summary>
        /// Chat
        /// </summary>
        /// <remarks>
        /// Chat with Muse.  Args:     request (Request): FastAPI request object.     body (ChatRequest): Chat request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     StreamingResponse | ChatResponse | JSONResponse:         Either streaming response, at-once chat response, or JSON error message.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="chatRequest"></param>
        /// <returns>ApiResponse of Object</returns>
        [Obsolete]
        ApiResponse<Object> ChatMuseChatPostWithHttpInfo(ChatRequest chatRequest);
        /// <summary>
        /// Chat
        /// </summary>
        /// <remarks>
        /// Chat with Muse.  Args:     request (Request): FastAPI request object.     body (ChatRequest): Chat request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     StreamingResponse | ChatResponse | JSONResponse:         Either streaming response, at-once chat response, or JSON error message.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="chatRequest"></param>
        /// <returns>Object</returns>
        Object ChatV1MuseChatPost(ChatRequest chatRequest);

        /// <summary>
        /// Chat
        /// </summary>
        /// <remarks>
        /// Chat with Muse.  Args:     request (Request): FastAPI request object.     body (ChatRequest): Chat request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     StreamingResponse | ChatResponse | JSONResponse:         Either streaming response, at-once chat response, or JSON error message.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="chatRequest"></param>
        /// <returns>ApiResponse of Object</returns>
        ApiResponse<Object> ChatV1MuseChatPostWithHttpInfo(ChatRequest chatRequest);
        /// <summary>
        /// Completion
        /// </summary>
        /// <remarks>
        /// Handles completion requests for a conversational AI model and manages associated user conversations and analytics.  Args:     request (Request): The request object, which provides access to all request-specific data.     body (LLMCompletionRequest): The request body containing data necessary for completion request.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Union[StreamingResponse, ChatResponse, JSONResponse]: Based on the &#x60;stream_response&#x60; flag in the request body,     this could be either a directly returned chat response, a streaming response,     or a JSON response containing the chat output.  Raises:     HTTPException: An error response with status code 500 in case of a failure during chat handling operations.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="lLMCompletionRequest"></param>
        /// <returns>Object</returns>
        [Obsolete]
        Object CompletionMuseCompletionPost(LLMCompletionRequest lLMCompletionRequest);

        /// <summary>
        /// Completion
        /// </summary>
        /// <remarks>
        /// Handles completion requests for a conversational AI model and manages associated user conversations and analytics.  Args:     request (Request): The request object, which provides access to all request-specific data.     body (LLMCompletionRequest): The request body containing data necessary for completion request.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Union[StreamingResponse, ChatResponse, JSONResponse]: Based on the &#x60;stream_response&#x60; flag in the request body,     this could be either a directly returned chat response, a streaming response,     or a JSON response containing the chat output.  Raises:     HTTPException: An error response with status code 500 in case of a failure during chat handling operations.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="lLMCompletionRequest"></param>
        /// <returns>ApiResponse of Object</returns>
        [Obsolete]
        ApiResponse<Object> CompletionMuseCompletionPostWithHttpInfo(LLMCompletionRequest lLMCompletionRequest);
        /// <summary>
        /// Completion
        /// </summary>
        /// <remarks>
        /// Handles completion requests for a conversational AI model and manages associated user conversations and analytics.  Args:     request (Request): The request object, which provides access to all request-specific data.     body (LLMCompletionRequest): The request body containing data necessary for completion request.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Union[StreamingResponse, ChatResponse, JSONResponse]: Based on the &#x60;stream_response&#x60; flag in the request body,     this could be either a directly returned chat response, a streaming response,     or a JSON response containing the chat output.  Raises:     HTTPException: An error response with status code 500 in case of a failure during chat handling operations.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="lLMCompletionRequest"></param>
        /// <returns>Object</returns>
        Object CompletionV1MuseCompletionPost(LLMCompletionRequest lLMCompletionRequest);

        /// <summary>
        /// Completion
        /// </summary>
        /// <remarks>
        /// Handles completion requests for a conversational AI model and manages associated user conversations and analytics.  Args:     request (Request): The request object, which provides access to all request-specific data.     body (LLMCompletionRequest): The request body containing data necessary for completion request.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Union[StreamingResponse, ChatResponse, JSONResponse]: Based on the &#x60;stream_response&#x60; flag in the request body,     this could be either a directly returned chat response, a streaming response,     or a JSON response containing the chat output.  Raises:     HTTPException: An error response with status code 500 in case of a failure during chat handling operations.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="lLMCompletionRequest"></param>
        /// <returns>ApiResponse of Object</returns>
        ApiResponse<Object> CompletionV1MuseCompletionPostWithHttpInfo(LLMCompletionRequest lLMCompletionRequest);
        /// <summary>
        /// Create Conversation
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="createConversationRequest"></param>
        /// <returns>Conversation</returns>
        [Obsolete]
        Conversation CreateConversationMuseConversationPost(CreateConversationRequest createConversationRequest);

        /// <summary>
        /// Create Conversation
        /// </summary>
        /// <remarks>
        ///
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="createConversationRequest"></param>
        /// <returns>ApiResponse of Conversation</returns>
        [Obsolete]
        ApiResponse<Conversation> CreateConversationMuseConversationPostWithHttpInfo(CreateConversationRequest createConversationRequest);
        /// <summary>
        /// Create Conversation
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="createConversationRequest"></param>
        /// <returns>Conversation</returns>
        Conversation CreateConversationV1MuseConversationPost(CreateConversationRequest createConversationRequest);

        /// <summary>
        /// Create Conversation
        /// </summary>
        /// <remarks>
        ///
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="createConversationRequest"></param>
        /// <returns>ApiResponse of Conversation</returns>
        ApiResponse<Conversation> CreateConversationV1MuseConversationPostWithHttpInfo(CreateConversationRequest createConversationRequest);
        /// <summary>
        /// Delete Conversation Fragment
        /// </summary>
        /// <remarks>
        /// Delete conversation fragment by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     fragment_id (str): Conversation fragment ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="fragmentId"></param>
        /// <returns>ErrorResponse</returns>
        [Obsolete]
        ErrorResponse DeleteConversationFragmentMuseConversationConversationIdFragmentFragmentIdDelete(string conversationId, string fragmentId);

        /// <summary>
        /// Delete Conversation Fragment
        /// </summary>
        /// <remarks>
        /// Delete conversation fragment by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     fragment_id (str): Conversation fragment ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="fragmentId"></param>
        /// <returns>ApiResponse of ErrorResponse</returns>
        [Obsolete]
        ApiResponse<ErrorResponse> DeleteConversationFragmentMuseConversationConversationIdFragmentFragmentIdDeleteWithHttpInfo(string conversationId, string fragmentId);
        /// <summary>
        /// Delete Conversation Fragment
        /// </summary>
        /// <remarks>
        /// Delete conversation fragment by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     fragment_id (str): Conversation fragment ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="fragmentId"></param>
        /// <returns>ErrorResponse</returns>
        ErrorResponse DeleteConversationFragmentV1MuseConversationConversationIdFragmentFragmentIdDelete(string conversationId, string fragmentId);

        /// <summary>
        /// Delete Conversation Fragment
        /// </summary>
        /// <remarks>
        /// Delete conversation fragment by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     fragment_id (str): Conversation fragment ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="fragmentId"></param>
        /// <returns>ApiResponse of ErrorResponse</returns>
        ApiResponse<ErrorResponse> DeleteConversationFragmentV1MuseConversationConversationIdFragmentFragmentIdDeleteWithHttpInfo(string conversationId, string fragmentId);
        /// <summary>
        /// Delete Conversation
        /// </summary>
        /// <remarks>
        /// Delete conversation by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <returns>ErrorResponse</returns>
        [Obsolete]
        ErrorResponse DeleteConversationMuseConversationConversationIdDelete(string conversationId);

        /// <summary>
        /// Delete Conversation
        /// </summary>
        /// <remarks>
        /// Delete conversation by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <returns>ApiResponse of ErrorResponse</returns>
        [Obsolete]
        ApiResponse<ErrorResponse> DeleteConversationMuseConversationConversationIdDeleteWithHttpInfo(string conversationId);
        /// <summary>
        /// Delete Conversation
        /// </summary>
        /// <remarks>
        /// Delete conversation by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <returns>ErrorResponse</returns>
        ErrorResponse DeleteConversationV1MuseConversationConversationIdDelete(string conversationId);

        /// <summary>
        /// Delete Conversation
        /// </summary>
        /// <remarks>
        /// Delete conversation by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <returns>ApiResponse of ErrorResponse</returns>
        ApiResponse<ErrorResponse> DeleteConversationV1MuseConversationConversationIdDeleteWithHttpInfo(string conversationId);
        /// <summary>
        /// Delete Conversations By Tags
        /// </summary>
        /// <remarks>
        /// Delete conversations by tags.  Args:     request (Request): FastAPI request object.     tags (list[str])): list of tags.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tags">List of tags to delete conversations by. (optional)</param>
        /// <returns>ErrorResponse</returns>
        [Obsolete]
        ErrorResponse DeleteConversationsByTagsMuseConversationsByTagsDelete(List<string> tags = default(List<string>));

        /// <summary>
        /// Delete Conversations By Tags
        /// </summary>
        /// <remarks>
        /// Delete conversations by tags.  Args:     request (Request): FastAPI request object.     tags (list[str])): list of tags.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tags">List of tags to delete conversations by. (optional)</param>
        /// <returns>ApiResponse of ErrorResponse</returns>
        [Obsolete]
        ApiResponse<ErrorResponse> DeleteConversationsByTagsMuseConversationsByTagsDeleteWithHttpInfo(List<string> tags = default(List<string>));
        /// <summary>
        /// Delete Conversations By Tags
        /// </summary>
        /// <remarks>
        /// Delete conversations by tags.  Args:     request (Request): FastAPI request object.     tags (list[str])): list of tags.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tags">List of tags to delete conversations by. (optional)</param>
        /// <returns>ErrorResponse</returns>
        ErrorResponse DeleteConversationsByTagsV1MuseConversationsByTagsDelete(List<string> tags = default(List<string>));

        /// <summary>
        /// Delete Conversations By Tags
        /// </summary>
        /// <remarks>
        /// Delete conversations by tags.  Args:     request (Request): FastAPI request object.     tags (list[str])): list of tags.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tags">List of tags to delete conversations by. (optional)</param>
        /// <returns>ApiResponse of ErrorResponse</returns>
        ApiResponse<ErrorResponse> DeleteConversationsByTagsV1MuseConversationsByTagsDeleteWithHttpInfo(List<string> tags = default(List<string>));
        /// <summary>
        /// Feedback
        /// </summary>
        /// <remarks>
        /// Provide feedback.  Args:     request (Request): FastAPI request object.     body (Feedback): Feedback request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="feedback"></param>
        /// <returns>ErrorResponse</returns>
        [Obsolete]
        ErrorResponse FeedbackMuseFeedbackPost(Feedback feedback);

        /// <summary>
        /// Feedback
        /// </summary>
        /// <remarks>
        /// Provide feedback.  Args:     request (Request): FastAPI request object.     body (Feedback): Feedback request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="feedback"></param>
        /// <returns>ApiResponse of ErrorResponse</returns>
        [Obsolete]
        ApiResponse<ErrorResponse> FeedbackMuseFeedbackPostWithHttpInfo(Feedback feedback);
        /// <summary>
        /// Feedback
        /// </summary>
        /// <remarks>
        /// Provide feedback.  Args:     request (Request): FastAPI request object.     body (Feedback): Feedback request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="feedback"></param>
        /// <returns>ErrorResponse</returns>
        ErrorResponse FeedbackV1MuseFeedbackPost(Feedback feedback);

        /// <summary>
        /// Feedback
        /// </summary>
        /// <remarks>
        /// Provide feedback.  Args:     request (Request): FastAPI request object.     body (Feedback): Feedback request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="feedback"></param>
        /// <returns>ApiResponse of ErrorResponse</returns>
        ApiResponse<ErrorResponse> FeedbackV1MuseFeedbackPostWithHttpInfo(Feedback feedback);
        /// <summary>
        /// Get Conversation
        /// </summary>
        /// <remarks>
        /// Get conversation by conversation ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     ClientConversation | JSONResponse:         ClientConversation corresponding to ID if it exists, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <returns>ResponseGetConversationMuseConversationConversationIdGet</returns>
        [Obsolete]
        ResponseGetConversationMuseConversationConversationIdGet GetConversationMuseConversationConversationIdGet(string conversationId);

        /// <summary>
        /// Get Conversation
        /// </summary>
        /// <remarks>
        /// Get conversation by conversation ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     ClientConversation | JSONResponse:         ClientConversation corresponding to ID if it exists, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <returns>ApiResponse of ResponseGetConversationMuseConversationConversationIdGet</returns>
        [Obsolete]
        ApiResponse<ResponseGetConversationMuseConversationConversationIdGet> GetConversationMuseConversationConversationIdGetWithHttpInfo(string conversationId);
        /// <summary>
        /// Get Conversation
        /// </summary>
        /// <remarks>
        /// Get conversation by conversation ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     ClientConversation | JSONResponse:         ClientConversation corresponding to ID if it exists, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <returns>ResponseGetConversationV1MuseConversationConversationIdGet</returns>
        ResponseGetConversationV1MuseConversationConversationIdGet GetConversationV1MuseConversationConversationIdGet(string conversationId);

        /// <summary>
        /// Get Conversation
        /// </summary>
        /// <remarks>
        /// Get conversation by conversation ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     ClientConversation | JSONResponse:         ClientConversation corresponding to ID if it exists, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <returns>ApiResponse of ResponseGetConversationV1MuseConversationConversationIdGet</returns>
        ApiResponse<ResponseGetConversationV1MuseConversationConversationIdGet> GetConversationV1MuseConversationConversationIdGetWithHttpInfo(string conversationId);
        /// <summary>
        /// Get Conversations
        /// </summary>
        /// <remarks>
        /// Get conversation summaries for user conversations.  Args:     request (Request): FastAPI request object.     user_info (UserInfo): User information extracted from bearer token.     tags (Optional[str], optional): Project ID to filter conversations by. Defaults to None.     skip_project_tag (bool, optional): Whether to skip conversations with a project tag.     limit (int, optional): Number of conversations to return. Defaults to 100.     skip (int, optional): Number of conversations to skip. Defaults to 0.  Returns:     list[ConversationInfo]: List of conversation summaries for user.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tags"> (optional)</param>
        /// <param name="skipProjectTag"> (optional)</param>
        /// <param name="limit"> (optional, default to 100)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <returns>List&lt;ConversationInfo&gt;</returns>
        [Obsolete]
        List<ConversationInfo> GetConversationsMuseConversationGet(string tags = default(string), bool? skipProjectTag = default(bool?), int? limit = default(int?), int? skip = default(int?));

        /// <summary>
        /// Get Conversations
        /// </summary>
        /// <remarks>
        /// Get conversation summaries for user conversations.  Args:     request (Request): FastAPI request object.     user_info (UserInfo): User information extracted from bearer token.     tags (Optional[str], optional): Project ID to filter conversations by. Defaults to None.     skip_project_tag (bool, optional): Whether to skip conversations with a project tag.     limit (int, optional): Number of conversations to return. Defaults to 100.     skip (int, optional): Number of conversations to skip. Defaults to 0.  Returns:     list[ConversationInfo]: List of conversation summaries for user.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tags"> (optional)</param>
        /// <param name="skipProjectTag"> (optional)</param>
        /// <param name="limit"> (optional, default to 100)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <returns>ApiResponse of List&lt;ConversationInfo&gt;</returns>
        [Obsolete]
        ApiResponse<List<ConversationInfo>> GetConversationsMuseConversationGetWithHttpInfo(string tags = default(string), bool? skipProjectTag = default(bool?), int? limit = default(int?), int? skip = default(int?));
        /// <summary>
        /// Get Conversations
        /// </summary>
        /// <remarks>
        /// Get conversation summaries for user conversations.  Args:     request (Request): FastAPI request object.     user_info (UserInfo): User information extracted from bearer token.     tags (Optional[str], optional): Project ID to filter conversations by. Defaults to None.     skip_project_tag (bool, optional): Whether to skip conversations with a project tag.     limit (int, optional): Number of conversations to return. Defaults to 100.     skip (int, optional): Number of conversations to skip. Defaults to 0.  Returns:     list[ConversationInfo]: List of conversation summaries for user.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tags"> (optional)</param>
        /// <param name="skipProjectTag"> (optional)</param>
        /// <param name="limit"> (optional, default to 100)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <returns>List&lt;ConversationInfo&gt;</returns>
        List<ConversationInfo> GetConversationsV1MuseConversationGet(string tags = default(string), bool? skipProjectTag = default(bool?), int? limit = default(int?), int? skip = default(int?));

        /// <summary>
        /// Get Conversations
        /// </summary>
        /// <remarks>
        /// Get conversation summaries for user conversations.  Args:     request (Request): FastAPI request object.     user_info (UserInfo): User information extracted from bearer token.     tags (Optional[str], optional): Project ID to filter conversations by. Defaults to None.     skip_project_tag (bool, optional): Whether to skip conversations with a project tag.     limit (int, optional): Number of conversations to return. Defaults to 100.     skip (int, optional): Number of conversations to skip. Defaults to 0.  Returns:     list[ConversationInfo]: List of conversation summaries for user.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tags"> (optional)</param>
        /// <param name="skipProjectTag"> (optional)</param>
        /// <param name="limit"> (optional, default to 100)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <returns>ApiResponse of List&lt;ConversationInfo&gt;</returns>
        ApiResponse<List<ConversationInfo>> GetConversationsV1MuseConversationGetWithHttpInfo(string tags = default(string), bool? skipProjectTag = default(bool?), int? limit = default(int?), int? skip = default(int?));
        /// <summary>
        /// Get Opt
        /// </summary>
        /// <remarks>
        /// Get the current opt status of the requesting user.  Args:     request (Request): _description_     user_info (UserInfo, optional): _description_. Defaults to Depends(extract_user_info).  Returns:     dict[OptType, OptDecision]: Opt status of the user.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <returns>Dictionary&lt;string, OptDecision&gt;</returns>
        [Obsolete]
        Dictionary<string, OptDecision> GetOptMuseOptGet();

        /// <summary>
        /// Get Opt
        /// </summary>
        /// <remarks>
        /// Get the current opt status of the requesting user.  Args:     request (Request): _description_     user_info (UserInfo, optional): _description_. Defaults to Depends(extract_user_info).  Returns:     dict[OptType, OptDecision]: Opt status of the user.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <returns>ApiResponse of Dictionary&lt;string, OptDecision&gt;</returns>
        [Obsolete]
        ApiResponse<Dictionary<string, OptDecision>> GetOptMuseOptGetWithHttpInfo();
        /// <summary>
        /// Get Opt
        /// </summary>
        /// <remarks>
        /// Get the current opt status of the requesting user.  Args:     request (Request): _description_     user_info (UserInfo, optional): _description_. Defaults to Depends(extract_user_info).  Returns:     dict[OptType, OptDecision]: Opt status of the user.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <returns>Dictionary&lt;string, OptDecision&gt;</returns>
        Dictionary<string, OptDecision> GetOptV1MuseOptGet();

        /// <summary>
        /// Get Opt
        /// </summary>
        /// <remarks>
        /// Get the current opt status of the requesting user.  Args:     request (Request): _description_     user_info (UserInfo, optional): _description_. Defaults to Depends(extract_user_info).  Returns:     dict[OptType, OptDecision]: Opt status of the user.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <returns>ApiResponse of Dictionary&lt;string, OptDecision&gt;</returns>
        ApiResponse<Dictionary<string, OptDecision>> GetOptV1MuseOptGetWithHttpInfo();
        /// <summary>
        /// Get Topic
        /// </summary>
        /// <remarks>
        /// Get topic title for conversation.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     organization_id (str): Organization ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     str | JSONResponse:         Plain-text topic if conversation exists, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="organizationId"></param>
        /// <returns>string</returns>
        [Obsolete]
        string GetTopicMuseTopicConversationIdGet(string conversationId, string organizationId);

        /// <summary>
        /// Get Topic
        /// </summary>
        /// <remarks>
        /// Get topic title for conversation.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     organization_id (str): Organization ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     str | JSONResponse:         Plain-text topic if conversation exists, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="organizationId"></param>
        /// <returns>ApiResponse of string</returns>
        [Obsolete]
        ApiResponse<string> GetTopicMuseTopicConversationIdGetWithHttpInfo(string conversationId, string organizationId);
        /// <summary>
        /// Get Topic
        /// </summary>
        /// <remarks>
        /// Get topic title for conversation.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     organization_id (str): Organization ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     str | JSONResponse:         Plain-text topic if conversation exists, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="organizationId"></param>
        /// <returns>string</returns>
        string GetTopicV1MuseTopicConversationIdGet(string conversationId, string organizationId);

        /// <summary>
        /// Get Topic
        /// </summary>
        /// <remarks>
        /// Get topic title for conversation.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     organization_id (str): Organization ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     str | JSONResponse:         Plain-text topic if conversation exists, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="organizationId"></param>
        /// <returns>ApiResponse of string</returns>
        ApiResponse<string> GetTopicV1MuseTopicConversationIdGetWithHttpInfo(string conversationId, string organizationId);
        /// <summary>
        /// Health Head
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <returns>Object</returns>
        Object HealthHeadHealthHead();

        /// <summary>
        /// Health Head
        /// </summary>
        /// <remarks>
        ///
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <returns>ApiResponse of Object</returns>
        ApiResponse<Object> HealthHeadHealthHeadWithHttpInfo();
        /// <summary>
        /// Health
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <returns>Object</returns>
        Object HealthHealthGet();

        /// <summary>
        /// Health
        /// </summary>
        /// <remarks>
        ///
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <returns>ApiResponse of Object</returns>
        ApiResponse<Object> HealthHealthGetWithHttpInfo();
        /// <summary>
        /// Healthz
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <returns>Object</returns>
        Object HealthzHealthzGet();

        /// <summary>
        /// Healthz
        /// </summary>
        /// <remarks>
        ///
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <returns>ApiResponse of Object</returns>
        ApiResponse<Object> HealthzHealthzGetWithHttpInfo();
        /// <summary>
        /// Opt
        /// </summary>
        /// <remarks>
        /// Opt in or out of model training.  Notes:     This is ideally a temporary solution. :)  Args:     request (Request): _description_     body (OptRequest): _description_     user_info (UserInfo, optional): _description_. Defaults to Depends(extract_user_info).
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="optRequest"></param>
        /// <returns>Object</returns>
        [Obsolete]
        Object OptMuseOptPost(OptRequest optRequest);

        /// <summary>
        /// Opt
        /// </summary>
        /// <remarks>
        /// Opt in or out of model training.  Notes:     This is ideally a temporary solution. :)  Args:     request (Request): _description_     body (OptRequest): _description_     user_info (UserInfo, optional): _description_. Defaults to Depends(extract_user_info).
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="optRequest"></param>
        /// <returns>ApiResponse of Object</returns>
        [Obsolete]
        ApiResponse<Object> OptMuseOptPostWithHttpInfo(OptRequest optRequest);
        /// <summary>
        /// Opt
        /// </summary>
        /// <remarks>
        /// Opt in or out of model training.  Notes:     This is ideally a temporary solution. :)  Args:     request (Request): _description_     body (OptRequest): _description_     user_info (UserInfo, optional): _description_. Defaults to Depends(extract_user_info).
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="optRequest"></param>
        /// <returns>Object</returns>
        Object OptV1MuseOptPost(OptRequest optRequest);

        /// <summary>
        /// Opt
        /// </summary>
        /// <remarks>
        /// Opt in or out of model training.  Notes:     This is ideally a temporary solution. :)  Args:     request (Request): _description_     body (OptRequest): _description_     user_info (UserInfo, optional): _description_. Defaults to Depends(extract_user_info).
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="optRequest"></param>
        /// <returns>ApiResponse of Object</returns>
        ApiResponse<Object> OptV1MuseOptPostWithHttpInfo(OptRequest optRequest);
        /// <summary>
        /// Patch Conversation Fragment Preference
        /// </summary>
        /// <remarks>
        /// Update conversation fragment by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     fragment_id (str): Conversation fragment ID.     body (ConversationPatchRequest): Patch request for changing conversation fragment.     user_info (UserInfo): User information extracted from bearer token.  Returns:     None | JSONResponse: None if successful, otherwise ErrorResponse.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="fragmentId"></param>
        /// <param name="conversationFragmentPatch"></param>
        /// <returns>ErrorResponse</returns>
        [Obsolete]
        ErrorResponse PatchConversationFragmentPreferenceMuseConversationConversationIdFragmentFragmentIdPatch(string conversationId, string fragmentId, ConversationFragmentPatch conversationFragmentPatch);

        /// <summary>
        /// Patch Conversation Fragment Preference
        /// </summary>
        /// <remarks>
        /// Update conversation fragment by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     fragment_id (str): Conversation fragment ID.     body (ConversationPatchRequest): Patch request for changing conversation fragment.     user_info (UserInfo): User information extracted from bearer token.  Returns:     None | JSONResponse: None if successful, otherwise ErrorResponse.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="fragmentId"></param>
        /// <param name="conversationFragmentPatch"></param>
        /// <returns>ApiResponse of ErrorResponse</returns>
        [Obsolete]
        ApiResponse<ErrorResponse> PatchConversationFragmentPreferenceMuseConversationConversationIdFragmentFragmentIdPatchWithHttpInfo(string conversationId, string fragmentId, ConversationFragmentPatch conversationFragmentPatch);
        /// <summary>
        /// Patch Conversation Fragment Preference
        /// </summary>
        /// <remarks>
        /// Update conversation fragment by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     fragment_id (str): Conversation fragment ID.     body (ConversationPatchRequest): Patch request for changing conversation fragment.     user_info (UserInfo): User information extracted from bearer token.  Returns:     None | JSONResponse: None if successful, otherwise ErrorResponse.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="fragmentId"></param>
        /// <param name="conversationFragmentPatch"></param>
        /// <returns>ErrorResponse</returns>
        ErrorResponse PatchConversationFragmentPreferenceV1MuseConversationConversationIdFragmentFragmentIdPatch(string conversationId, string fragmentId, ConversationFragmentPatch conversationFragmentPatch);

        /// <summary>
        /// Patch Conversation Fragment Preference
        /// </summary>
        /// <remarks>
        /// Update conversation fragment by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     fragment_id (str): Conversation fragment ID.     body (ConversationPatchRequest): Patch request for changing conversation fragment.     user_info (UserInfo): User information extracted from bearer token.  Returns:     None | JSONResponse: None if successful, otherwise ErrorResponse.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="fragmentId"></param>
        /// <param name="conversationFragmentPatch"></param>
        /// <returns>ApiResponse of ErrorResponse</returns>
        ApiResponse<ErrorResponse> PatchConversationFragmentPreferenceV1MuseConversationConversationIdFragmentFragmentIdPatchWithHttpInfo(string conversationId, string fragmentId, ConversationFragmentPatch conversationFragmentPatch);
        /// <summary>
        /// Patch Conversation
        /// </summary>
        /// <remarks>
        /// Update conversation by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     body (ConversationPatchRequest): Patch request for changing conversation.     user_info (UserInfo): User information extracted from bearer token.  Returns:     None | JSONResponse: None if successful, otherwise ErrorResponse.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="conversationPatchRequest"></param>
        /// <returns>ErrorResponse</returns>
        [Obsolete]
        ErrorResponse PatchConversationMuseConversationConversationIdPatch(string conversationId, ConversationPatchRequest conversationPatchRequest);

        /// <summary>
        /// Patch Conversation
        /// </summary>
        /// <remarks>
        /// Update conversation by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     body (ConversationPatchRequest): Patch request for changing conversation.     user_info (UserInfo): User information extracted from bearer token.  Returns:     None | JSONResponse: None if successful, otherwise ErrorResponse.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="conversationPatchRequest"></param>
        /// <returns>ApiResponse of ErrorResponse</returns>
        [Obsolete]
        ApiResponse<ErrorResponse> PatchConversationMuseConversationConversationIdPatchWithHttpInfo(string conversationId, ConversationPatchRequest conversationPatchRequest);
        /// <summary>
        /// Patch Conversation
        /// </summary>
        /// <remarks>
        /// Update conversation by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     body (ConversationPatchRequest): Patch request for changing conversation.     user_info (UserInfo): User information extracted from bearer token.  Returns:     None | JSONResponse: None if successful, otherwise ErrorResponse.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="conversationPatchRequest"></param>
        /// <returns>ErrorResponse</returns>
        ErrorResponse PatchConversationV1MuseConversationConversationIdPatch(string conversationId, ConversationPatchRequest conversationPatchRequest);

        /// <summary>
        /// Patch Conversation
        /// </summary>
        /// <remarks>
        /// Update conversation by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     body (ConversationPatchRequest): Patch request for changing conversation.     user_info (UserInfo): User information extracted from bearer token.  Returns:     None | JSONResponse: None if successful, otherwise ErrorResponse.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="conversationPatchRequest"></param>
        /// <returns>ApiResponse of ErrorResponse</returns>
        ApiResponse<ErrorResponse> PatchConversationV1MuseConversationConversationIdPatchWithHttpInfo(string conversationId, ConversationPatchRequest conversationPatchRequest);
        /// <summary>
        /// Smart Context
        /// </summary>
        /// <remarks>
        /// Handle smart context requests.  Args:     request (Request): FastAPI request object.     body (SmartContextRequest): Smart context request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     SmartContextResponse | JSONResponse:         Either smart context response or JSON error message.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="smartContextRequest"></param>
        /// <returns>SmartContextResponse</returns>
        [Obsolete]
        SmartContextResponse SmartContextSmartContextPost(SmartContextRequest smartContextRequest);

        /// <summary>
        /// Smart Context
        /// </summary>
        /// <remarks>
        /// Handle smart context requests.  Args:     request (Request): FastAPI request object.     body (SmartContextRequest): Smart context request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     SmartContextResponse | JSONResponse:         Either smart context response or JSON error message.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="smartContextRequest"></param>
        /// <returns>ApiResponse of SmartContextResponse</returns>
        [Obsolete]
        ApiResponse<SmartContextResponse> SmartContextSmartContextPostWithHttpInfo(SmartContextRequest smartContextRequest);
        /// <summary>
        /// Smart Context
        /// </summary>
        /// <remarks>
        /// Handle smart context requests.  Args:     request (Request): FastAPI request object.     body (SmartContextRequest): Smart context request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     SmartContextResponse | JSONResponse:         Either smart context response or JSON error message.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="smartContextRequest"></param>
        /// <returns>SmartContextResponse</returns>
        SmartContextResponse SmartContextV1SmartContextPost(SmartContextRequest smartContextRequest);

        /// <summary>
        /// Smart Context
        /// </summary>
        /// <remarks>
        /// Handle smart context requests.  Args:     request (Request): FastAPI request object.     body (SmartContextRequest): Smart context request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     SmartContextResponse | JSONResponse:         Either smart context response or JSON error message.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="smartContextRequest"></param>
        /// <returns>ApiResponse of SmartContextResponse</returns>
        ApiResponse<SmartContextResponse> SmartContextV1SmartContextPostWithHttpInfo(SmartContextRequest smartContextRequest);
        #endregion Synchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    internal interface IDefaultApiAsync : IApiAccessor
    {
        #region Asynchronous Operations
        /// <summary>
        /// Action Code Repair
        /// </summary>
        /// <remarks>
        /// Agent action code repairing route for repairing generated csharp scripts.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actionCodeRepairRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        [Obsolete]
        System.Threading.Tasks.Task<Object> ActionCodeRepairMuseAgentCodeRepairPostAsync(ActionCodeRepairRequest actionCodeRepairRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Action Code Repair
        /// </summary>
        /// <remarks>
        /// Agent action code repairing route for repairing generated csharp scripts.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actionCodeRepairRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        [Obsolete]
        System.Threading.Tasks.Task<ApiResponse<Object>> ActionCodeRepairMuseAgentCodeRepairPostWithHttpInfoAsync(ActionCodeRepairRequest actionCodeRepairRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Action Code Repair
        /// </summary>
        /// <remarks>
        /// Agent action code repairing route for repairing generated csharp scripts.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actionCodeRepairRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        System.Threading.Tasks.Task<Object> ActionCodeRepairV1MuseAgentCodeRepairPostAsync(ActionCodeRepairRequest actionCodeRepairRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Action Code Repair
        /// </summary>
        /// <remarks>
        /// Agent action code repairing route for repairing generated csharp scripts.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actionCodeRepairRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        System.Threading.Tasks.Task<ApiResponse<Object>> ActionCodeRepairV1MuseAgentCodeRepairPostWithHttpInfoAsync(ActionCodeRepairRequest actionCodeRepairRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Action
        /// </summary>
        /// <remarks>
        /// Agent action route for performing actions in the editor.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actionRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        [Obsolete]
        System.Threading.Tasks.Task<Object> ActionMuseAgentActionPostAsync(ActionRequest actionRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Action
        /// </summary>
        /// <remarks>
        /// Agent action route for performing actions in the editor.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actionRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        [Obsolete]
        System.Threading.Tasks.Task<ApiResponse<Object>> ActionMuseAgentActionPostWithHttpInfoAsync(ActionRequest actionRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Action
        /// </summary>
        /// <remarks>
        /// Agent action route for performing actions in the editor.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actionRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        System.Threading.Tasks.Task<Object> ActionV1MuseAgentActionPostAsync(ActionRequest actionRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Action
        /// </summary>
        /// <remarks>
        /// Agent action route for performing actions in the editor.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actionRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        System.Threading.Tasks.Task<ApiResponse<Object>> ActionV1MuseAgentActionPostWithHttpInfoAsync(ActionRequest actionRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Chat
        /// </summary>
        /// <remarks>
        /// Chat with Muse.  Args:     request (Request): FastAPI request object.     body (ChatRequest): Chat request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     StreamingResponse | ChatResponse | JSONResponse:         Either streaming response, at-once chat response, or JSON error message.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="chatRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        [Obsolete]
        System.Threading.Tasks.Task<Object> ChatMuseChatPostAsync(ChatRequest chatRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Chat
        /// </summary>
        /// <remarks>
        /// Chat with Muse.  Args:     request (Request): FastAPI request object.     body (ChatRequest): Chat request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     StreamingResponse | ChatResponse | JSONResponse:         Either streaming response, at-once chat response, or JSON error message.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="chatRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        [Obsolete]
        System.Threading.Tasks.Task<ApiResponse<Object>> ChatMuseChatPostWithHttpInfoAsync(ChatRequest chatRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Chat
        /// </summary>
        /// <remarks>
        /// Chat with Muse.  Args:     request (Request): FastAPI request object.     body (ChatRequest): Chat request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     StreamingResponse | ChatResponse | JSONResponse:         Either streaming response, at-once chat response, or JSON error message.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="chatRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        System.Threading.Tasks.Task<Object> ChatV1MuseChatPostAsync(ChatRequest chatRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Chat
        /// </summary>
        /// <remarks>
        /// Chat with Muse.  Args:     request (Request): FastAPI request object.     body (ChatRequest): Chat request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     StreamingResponse | ChatResponse | JSONResponse:         Either streaming response, at-once chat response, or JSON error message.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="chatRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        System.Threading.Tasks.Task<ApiResponse<Object>> ChatV1MuseChatPostWithHttpInfoAsync(ChatRequest chatRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Completion
        /// </summary>
        /// <remarks>
        /// Handles completion requests for a conversational AI model and manages associated user conversations and analytics.  Args:     request (Request): The request object, which provides access to all request-specific data.     body (LLMCompletionRequest): The request body containing data necessary for completion request.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Union[StreamingResponse, ChatResponse, JSONResponse]: Based on the &#x60;stream_response&#x60; flag in the request body,     this could be either a directly returned chat response, a streaming response,     or a JSON response containing the chat output.  Raises:     HTTPException: An error response with status code 500 in case of a failure during chat handling operations.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="lLMCompletionRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        [Obsolete]
        System.Threading.Tasks.Task<Object> CompletionMuseCompletionPostAsync(LLMCompletionRequest lLMCompletionRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Completion
        /// </summary>
        /// <remarks>
        /// Handles completion requests for a conversational AI model and manages associated user conversations and analytics.  Args:     request (Request): The request object, which provides access to all request-specific data.     body (LLMCompletionRequest): The request body containing data necessary for completion request.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Union[StreamingResponse, ChatResponse, JSONResponse]: Based on the &#x60;stream_response&#x60; flag in the request body,     this could be either a directly returned chat response, a streaming response,     or a JSON response containing the chat output.  Raises:     HTTPException: An error response with status code 500 in case of a failure during chat handling operations.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="lLMCompletionRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        [Obsolete]
        System.Threading.Tasks.Task<ApiResponse<Object>> CompletionMuseCompletionPostWithHttpInfoAsync(LLMCompletionRequest lLMCompletionRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Completion
        /// </summary>
        /// <remarks>
        /// Handles completion requests for a conversational AI model and manages associated user conversations and analytics.  Args:     request (Request): The request object, which provides access to all request-specific data.     body (LLMCompletionRequest): The request body containing data necessary for completion request.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Union[StreamingResponse, ChatResponse, JSONResponse]: Based on the &#x60;stream_response&#x60; flag in the request body,     this could be either a directly returned chat response, a streaming response,     or a JSON response containing the chat output.  Raises:     HTTPException: An error response with status code 500 in case of a failure during chat handling operations.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="lLMCompletionRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        System.Threading.Tasks.Task<Object> CompletionV1MuseCompletionPostAsync(LLMCompletionRequest lLMCompletionRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Completion
        /// </summary>
        /// <remarks>
        /// Handles completion requests for a conversational AI model and manages associated user conversations and analytics.  Args:     request (Request): The request object, which provides access to all request-specific data.     body (LLMCompletionRequest): The request body containing data necessary for completion request.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Union[StreamingResponse, ChatResponse, JSONResponse]: Based on the &#x60;stream_response&#x60; flag in the request body,     this could be either a directly returned chat response, a streaming response,     or a JSON response containing the chat output.  Raises:     HTTPException: An error response with status code 500 in case of a failure during chat handling operations.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="lLMCompletionRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        System.Threading.Tasks.Task<ApiResponse<Object>> CompletionV1MuseCompletionPostWithHttpInfoAsync(LLMCompletionRequest lLMCompletionRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Create Conversation
        /// </summary>
        /// <remarks>
        ///
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="createConversationRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Conversation</returns>
        [Obsolete]
        System.Threading.Tasks.Task<Conversation> CreateConversationMuseConversationPostAsync(CreateConversationRequest createConversationRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Create Conversation
        /// </summary>
        /// <remarks>
        ///
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="createConversationRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Conversation)</returns>
        [Obsolete]
        System.Threading.Tasks.Task<ApiResponse<Conversation>> CreateConversationMuseConversationPostWithHttpInfoAsync(CreateConversationRequest createConversationRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Create Conversation
        /// </summary>
        /// <remarks>
        ///
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="createConversationRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Conversation</returns>
        System.Threading.Tasks.Task<Conversation> CreateConversationV1MuseConversationPostAsync(CreateConversationRequest createConversationRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Create Conversation
        /// </summary>
        /// <remarks>
        ///
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="createConversationRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Conversation)</returns>
        System.Threading.Tasks.Task<ApiResponse<Conversation>> CreateConversationV1MuseConversationPostWithHttpInfoAsync(CreateConversationRequest createConversationRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Delete Conversation Fragment
        /// </summary>
        /// <remarks>
        /// Delete conversation fragment by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     fragment_id (str): Conversation fragment ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="fragmentId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ErrorResponse</returns>
        [Obsolete]
        System.Threading.Tasks.Task<ErrorResponse> DeleteConversationFragmentMuseConversationConversationIdFragmentFragmentIdDeleteAsync(string conversationId, string fragmentId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Delete Conversation Fragment
        /// </summary>
        /// <remarks>
        /// Delete conversation fragment by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     fragment_id (str): Conversation fragment ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="fragmentId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ErrorResponse)</returns>
        [Obsolete]
        System.Threading.Tasks.Task<ApiResponse<ErrorResponse>> DeleteConversationFragmentMuseConversationConversationIdFragmentFragmentIdDeleteWithHttpInfoAsync(string conversationId, string fragmentId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Delete Conversation Fragment
        /// </summary>
        /// <remarks>
        /// Delete conversation fragment by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     fragment_id (str): Conversation fragment ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="fragmentId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ErrorResponse</returns>
        System.Threading.Tasks.Task<ErrorResponse> DeleteConversationFragmentV1MuseConversationConversationIdFragmentFragmentIdDeleteAsync(string conversationId, string fragmentId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Delete Conversation Fragment
        /// </summary>
        /// <remarks>
        /// Delete conversation fragment by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     fragment_id (str): Conversation fragment ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="fragmentId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ErrorResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<ErrorResponse>> DeleteConversationFragmentV1MuseConversationConversationIdFragmentFragmentIdDeleteWithHttpInfoAsync(string conversationId, string fragmentId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Delete Conversation
        /// </summary>
        /// <remarks>
        /// Delete conversation by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ErrorResponse</returns>
        [Obsolete]
        System.Threading.Tasks.Task<ErrorResponse> DeleteConversationMuseConversationConversationIdDeleteAsync(string conversationId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Delete Conversation
        /// </summary>
        /// <remarks>
        /// Delete conversation by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ErrorResponse)</returns>
        [Obsolete]
        System.Threading.Tasks.Task<ApiResponse<ErrorResponse>> DeleteConversationMuseConversationConversationIdDeleteWithHttpInfoAsync(string conversationId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Delete Conversation
        /// </summary>
        /// <remarks>
        /// Delete conversation by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ErrorResponse</returns>
        System.Threading.Tasks.Task<ErrorResponse> DeleteConversationV1MuseConversationConversationIdDeleteAsync(string conversationId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Delete Conversation
        /// </summary>
        /// <remarks>
        /// Delete conversation by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ErrorResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<ErrorResponse>> DeleteConversationV1MuseConversationConversationIdDeleteWithHttpInfoAsync(string conversationId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Delete Conversations By Tags
        /// </summary>
        /// <remarks>
        /// Delete conversations by tags.  Args:     request (Request): FastAPI request object.     tags (list[str])): list of tags.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tags">List of tags to delete conversations by. (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ErrorResponse</returns>
        [Obsolete]
        System.Threading.Tasks.Task<ErrorResponse> DeleteConversationsByTagsMuseConversationsByTagsDeleteAsync(List<string> tags = default(List<string>), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Delete Conversations By Tags
        /// </summary>
        /// <remarks>
        /// Delete conversations by tags.  Args:     request (Request): FastAPI request object.     tags (list[str])): list of tags.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tags">List of tags to delete conversations by. (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ErrorResponse)</returns>
        [Obsolete]
        System.Threading.Tasks.Task<ApiResponse<ErrorResponse>> DeleteConversationsByTagsMuseConversationsByTagsDeleteWithHttpInfoAsync(List<string> tags = default(List<string>), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Delete Conversations By Tags
        /// </summary>
        /// <remarks>
        /// Delete conversations by tags.  Args:     request (Request): FastAPI request object.     tags (list[str])): list of tags.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tags">List of tags to delete conversations by. (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ErrorResponse</returns>
        System.Threading.Tasks.Task<ErrorResponse> DeleteConversationsByTagsV1MuseConversationsByTagsDeleteAsync(List<string> tags = default(List<string>), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Delete Conversations By Tags
        /// </summary>
        /// <remarks>
        /// Delete conversations by tags.  Args:     request (Request): FastAPI request object.     tags (list[str])): list of tags.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tags">List of tags to delete conversations by. (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ErrorResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<ErrorResponse>> DeleteConversationsByTagsV1MuseConversationsByTagsDeleteWithHttpInfoAsync(List<string> tags = default(List<string>), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Feedback
        /// </summary>
        /// <remarks>
        /// Provide feedback.  Args:     request (Request): FastAPI request object.     body (Feedback): Feedback request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="feedback"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ErrorResponse</returns>
        [Obsolete]
        System.Threading.Tasks.Task<ErrorResponse> FeedbackMuseFeedbackPostAsync(Feedback feedback, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Feedback
        /// </summary>
        /// <remarks>
        /// Provide feedback.  Args:     request (Request): FastAPI request object.     body (Feedback): Feedback request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="feedback"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ErrorResponse)</returns>
        [Obsolete]
        System.Threading.Tasks.Task<ApiResponse<ErrorResponse>> FeedbackMuseFeedbackPostWithHttpInfoAsync(Feedback feedback, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Feedback
        /// </summary>
        /// <remarks>
        /// Provide feedback.  Args:     request (Request): FastAPI request object.     body (Feedback): Feedback request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="feedback"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ErrorResponse</returns>
        System.Threading.Tasks.Task<ErrorResponse> FeedbackV1MuseFeedbackPostAsync(Feedback feedback, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Feedback
        /// </summary>
        /// <remarks>
        /// Provide feedback.  Args:     request (Request): FastAPI request object.     body (Feedback): Feedback request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="feedback"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ErrorResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<ErrorResponse>> FeedbackV1MuseFeedbackPostWithHttpInfoAsync(Feedback feedback, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Get Conversation
        /// </summary>
        /// <remarks>
        /// Get conversation by conversation ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     ClientConversation | JSONResponse:         ClientConversation corresponding to ID if it exists, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ResponseGetConversationMuseConversationConversationIdGet</returns>
        [Obsolete]
        System.Threading.Tasks.Task<ResponseGetConversationMuseConversationConversationIdGet> GetConversationMuseConversationConversationIdGetAsync(string conversationId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Get Conversation
        /// </summary>
        /// <remarks>
        /// Get conversation by conversation ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     ClientConversation | JSONResponse:         ClientConversation corresponding to ID if it exists, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ResponseGetConversationMuseConversationConversationIdGet)</returns>
        [Obsolete]
        System.Threading.Tasks.Task<ApiResponse<ResponseGetConversationMuseConversationConversationIdGet>> GetConversationMuseConversationConversationIdGetWithHttpInfoAsync(string conversationId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Get Conversation
        /// </summary>
        /// <remarks>
        /// Get conversation by conversation ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     ClientConversation | JSONResponse:         ClientConversation corresponding to ID if it exists, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ResponseGetConversationV1MuseConversationConversationIdGet</returns>
        System.Threading.Tasks.Task<ResponseGetConversationV1MuseConversationConversationIdGet> GetConversationV1MuseConversationConversationIdGetAsync(string conversationId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Get Conversation
        /// </summary>
        /// <remarks>
        /// Get conversation by conversation ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     ClientConversation | JSONResponse:         ClientConversation corresponding to ID if it exists, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ResponseGetConversationV1MuseConversationConversationIdGet)</returns>
        System.Threading.Tasks.Task<ApiResponse<ResponseGetConversationV1MuseConversationConversationIdGet>> GetConversationV1MuseConversationConversationIdGetWithHttpInfoAsync(string conversationId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Get Conversations
        /// </summary>
        /// <remarks>
        /// Get conversation summaries for user conversations.  Args:     request (Request): FastAPI request object.     user_info (UserInfo): User information extracted from bearer token.     tags (Optional[str], optional): Project ID to filter conversations by. Defaults to None.     skip_project_tag (bool, optional): Whether to skip conversations with a project tag.     limit (int, optional): Number of conversations to return. Defaults to 100.     skip (int, optional): Number of conversations to skip. Defaults to 0.  Returns:     list[ConversationInfo]: List of conversation summaries for user.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tags"> (optional)</param>
        /// <param name="skipProjectTag"> (optional)</param>
        /// <param name="limit"> (optional, default to 100)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of List&lt;ConversationInfo&gt;</returns>
        [Obsolete]
        System.Threading.Tasks.Task<List<ConversationInfo>> GetConversationsMuseConversationGetAsync(string tags = default(string), bool? skipProjectTag = default(bool?), int? limit = default(int?), int? skip = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Get Conversations
        /// </summary>
        /// <remarks>
        /// Get conversation summaries for user conversations.  Args:     request (Request): FastAPI request object.     user_info (UserInfo): User information extracted from bearer token.     tags (Optional[str], optional): Project ID to filter conversations by. Defaults to None.     skip_project_tag (bool, optional): Whether to skip conversations with a project tag.     limit (int, optional): Number of conversations to return. Defaults to 100.     skip (int, optional): Number of conversations to skip. Defaults to 0.  Returns:     list[ConversationInfo]: List of conversation summaries for user.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tags"> (optional)</param>
        /// <param name="skipProjectTag"> (optional)</param>
        /// <param name="limit"> (optional, default to 100)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (List&lt;ConversationInfo&gt;)</returns>
        [Obsolete]
        System.Threading.Tasks.Task<ApiResponse<List<ConversationInfo>>> GetConversationsMuseConversationGetWithHttpInfoAsync(string tags = default(string), bool? skipProjectTag = default(bool?), int? limit = default(int?), int? skip = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Get Conversations
        /// </summary>
        /// <remarks>
        /// Get conversation summaries for user conversations.  Args:     request (Request): FastAPI request object.     user_info (UserInfo): User information extracted from bearer token.     tags (Optional[str], optional): Project ID to filter conversations by. Defaults to None.     skip_project_tag (bool, optional): Whether to skip conversations with a project tag.     limit (int, optional): Number of conversations to return. Defaults to 100.     skip (int, optional): Number of conversations to skip. Defaults to 0.  Returns:     list[ConversationInfo]: List of conversation summaries for user.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tags"> (optional)</param>
        /// <param name="skipProjectTag"> (optional)</param>
        /// <param name="limit"> (optional, default to 100)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of List&lt;ConversationInfo&gt;</returns>
        System.Threading.Tasks.Task<List<ConversationInfo>> GetConversationsV1MuseConversationGetAsync(string tags = default(string), bool? skipProjectTag = default(bool?), int? limit = default(int?), int? skip = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Get Conversations
        /// </summary>
        /// <remarks>
        /// Get conversation summaries for user conversations.  Args:     request (Request): FastAPI request object.     user_info (UserInfo): User information extracted from bearer token.     tags (Optional[str], optional): Project ID to filter conversations by. Defaults to None.     skip_project_tag (bool, optional): Whether to skip conversations with a project tag.     limit (int, optional): Number of conversations to return. Defaults to 100.     skip (int, optional): Number of conversations to skip. Defaults to 0.  Returns:     list[ConversationInfo]: List of conversation summaries for user.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tags"> (optional)</param>
        /// <param name="skipProjectTag"> (optional)</param>
        /// <param name="limit"> (optional, default to 100)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (List&lt;ConversationInfo&gt;)</returns>
        System.Threading.Tasks.Task<ApiResponse<List<ConversationInfo>>> GetConversationsV1MuseConversationGetWithHttpInfoAsync(string tags = default(string), bool? skipProjectTag = default(bool?), int? limit = default(int?), int? skip = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Get Opt
        /// </summary>
        /// <remarks>
        /// Get the current opt status of the requesting user.  Args:     request (Request): _description_     user_info (UserInfo, optional): _description_. Defaults to Depends(extract_user_info).  Returns:     dict[OptType, OptDecision]: Opt status of the user.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Dictionary&lt;string, OptDecision&gt;</returns>
        [Obsolete]
        System.Threading.Tasks.Task<Dictionary<string, OptDecision>> GetOptMuseOptGetAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Get Opt
        /// </summary>
        /// <remarks>
        /// Get the current opt status of the requesting user.  Args:     request (Request): _description_     user_info (UserInfo, optional): _description_. Defaults to Depends(extract_user_info).  Returns:     dict[OptType, OptDecision]: Opt status of the user.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Dictionary&lt;string, OptDecision&gt;)</returns>
        [Obsolete]
        System.Threading.Tasks.Task<ApiResponse<Dictionary<string, OptDecision>>> GetOptMuseOptGetWithHttpInfoAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Get Opt
        /// </summary>
        /// <remarks>
        /// Get the current opt status of the requesting user.  Args:     request (Request): _description_     user_info (UserInfo, optional): _description_. Defaults to Depends(extract_user_info).  Returns:     dict[OptType, OptDecision]: Opt status of the user.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Dictionary&lt;string, OptDecision&gt;</returns>
        System.Threading.Tasks.Task<Dictionary<string, OptDecision>> GetOptV1MuseOptGetAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Get Opt
        /// </summary>
        /// <remarks>
        /// Get the current opt status of the requesting user.  Args:     request (Request): _description_     user_info (UserInfo, optional): _description_. Defaults to Depends(extract_user_info).  Returns:     dict[OptType, OptDecision]: Opt status of the user.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Dictionary&lt;string, OptDecision&gt;)</returns>
        System.Threading.Tasks.Task<ApiResponse<Dictionary<string, OptDecision>>> GetOptV1MuseOptGetWithHttpInfoAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Get Topic
        /// </summary>
        /// <remarks>
        /// Get topic title for conversation.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     organization_id (str): Organization ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     str | JSONResponse:         Plain-text topic if conversation exists, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="organizationId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of string</returns>
        [Obsolete]
        System.Threading.Tasks.Task<string> GetTopicMuseTopicConversationIdGetAsync(string conversationId, string organizationId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Get Topic
        /// </summary>
        /// <remarks>
        /// Get topic title for conversation.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     organization_id (str): Organization ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     str | JSONResponse:         Plain-text topic if conversation exists, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="organizationId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (string)</returns>
        [Obsolete]
        System.Threading.Tasks.Task<ApiResponse<string>> GetTopicMuseTopicConversationIdGetWithHttpInfoAsync(string conversationId, string organizationId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Get Topic
        /// </summary>
        /// <remarks>
        /// Get topic title for conversation.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     organization_id (str): Organization ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     str | JSONResponse:         Plain-text topic if conversation exists, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="organizationId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of string</returns>
        System.Threading.Tasks.Task<string> GetTopicV1MuseTopicConversationIdGetAsync(string conversationId, string organizationId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Get Topic
        /// </summary>
        /// <remarks>
        /// Get topic title for conversation.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     organization_id (str): Organization ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     str | JSONResponse:         Plain-text topic if conversation exists, otherwise JSONResponse with error.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="organizationId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (string)</returns>
        System.Threading.Tasks.Task<ApiResponse<string>> GetTopicV1MuseTopicConversationIdGetWithHttpInfoAsync(string conversationId, string organizationId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Health Head
        /// </summary>
        /// <remarks>
        ///
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        System.Threading.Tasks.Task<Object> HealthHeadHealthHeadAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Health Head
        /// </summary>
        /// <remarks>
        ///
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        System.Threading.Tasks.Task<ApiResponse<Object>> HealthHeadHealthHeadWithHttpInfoAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Health
        /// </summary>
        /// <remarks>
        ///
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        System.Threading.Tasks.Task<Object> HealthHealthGetAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Health
        /// </summary>
        /// <remarks>
        ///
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        System.Threading.Tasks.Task<ApiResponse<Object>> HealthHealthGetWithHttpInfoAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Healthz
        /// </summary>
        /// <remarks>
        ///
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        System.Threading.Tasks.Task<Object> HealthzHealthzGetAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Healthz
        /// </summary>
        /// <remarks>
        ///
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        System.Threading.Tasks.Task<ApiResponse<Object>> HealthzHealthzGetWithHttpInfoAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Opt
        /// </summary>
        /// <remarks>
        /// Opt in or out of model training.  Notes:     This is ideally a temporary solution. :)  Args:     request (Request): _description_     body (OptRequest): _description_     user_info (UserInfo, optional): _description_. Defaults to Depends(extract_user_info).
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="optRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        [Obsolete]
        System.Threading.Tasks.Task<Object> OptMuseOptPostAsync(OptRequest optRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Opt
        /// </summary>
        /// <remarks>
        /// Opt in or out of model training.  Notes:     This is ideally a temporary solution. :)  Args:     request (Request): _description_     body (OptRequest): _description_     user_info (UserInfo, optional): _description_. Defaults to Depends(extract_user_info).
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="optRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        [Obsolete]
        System.Threading.Tasks.Task<ApiResponse<Object>> OptMuseOptPostWithHttpInfoAsync(OptRequest optRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Opt
        /// </summary>
        /// <remarks>
        /// Opt in or out of model training.  Notes:     This is ideally a temporary solution. :)  Args:     request (Request): _description_     body (OptRequest): _description_     user_info (UserInfo, optional): _description_. Defaults to Depends(extract_user_info).
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="optRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        System.Threading.Tasks.Task<Object> OptV1MuseOptPostAsync(OptRequest optRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Opt
        /// </summary>
        /// <remarks>
        /// Opt in or out of model training.  Notes:     This is ideally a temporary solution. :)  Args:     request (Request): _description_     body (OptRequest): _description_     user_info (UserInfo, optional): _description_. Defaults to Depends(extract_user_info).
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="optRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        System.Threading.Tasks.Task<ApiResponse<Object>> OptV1MuseOptPostWithHttpInfoAsync(OptRequest optRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Patch Conversation Fragment Preference
        /// </summary>
        /// <remarks>
        /// Update conversation fragment by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     fragment_id (str): Conversation fragment ID.     body (ConversationPatchRequest): Patch request for changing conversation fragment.     user_info (UserInfo): User information extracted from bearer token.  Returns:     None | JSONResponse: None if successful, otherwise ErrorResponse.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="fragmentId"></param>
        /// <param name="conversationFragmentPatch"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ErrorResponse</returns>
        [Obsolete]
        System.Threading.Tasks.Task<ErrorResponse> PatchConversationFragmentPreferenceMuseConversationConversationIdFragmentFragmentIdPatchAsync(string conversationId, string fragmentId, ConversationFragmentPatch conversationFragmentPatch, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Patch Conversation Fragment Preference
        /// </summary>
        /// <remarks>
        /// Update conversation fragment by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     fragment_id (str): Conversation fragment ID.     body (ConversationPatchRequest): Patch request for changing conversation fragment.     user_info (UserInfo): User information extracted from bearer token.  Returns:     None | JSONResponse: None if successful, otherwise ErrorResponse.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="fragmentId"></param>
        /// <param name="conversationFragmentPatch"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ErrorResponse)</returns>
        [Obsolete]
        System.Threading.Tasks.Task<ApiResponse<ErrorResponse>> PatchConversationFragmentPreferenceMuseConversationConversationIdFragmentFragmentIdPatchWithHttpInfoAsync(string conversationId, string fragmentId, ConversationFragmentPatch conversationFragmentPatch, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Patch Conversation Fragment Preference
        /// </summary>
        /// <remarks>
        /// Update conversation fragment by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     fragment_id (str): Conversation fragment ID.     body (ConversationPatchRequest): Patch request for changing conversation fragment.     user_info (UserInfo): User information extracted from bearer token.  Returns:     None | JSONResponse: None if successful, otherwise ErrorResponse.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="fragmentId"></param>
        /// <param name="conversationFragmentPatch"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ErrorResponse</returns>
        System.Threading.Tasks.Task<ErrorResponse> PatchConversationFragmentPreferenceV1MuseConversationConversationIdFragmentFragmentIdPatchAsync(string conversationId, string fragmentId, ConversationFragmentPatch conversationFragmentPatch, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Patch Conversation Fragment Preference
        /// </summary>
        /// <remarks>
        /// Update conversation fragment by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     fragment_id (str): Conversation fragment ID.     body (ConversationPatchRequest): Patch request for changing conversation fragment.     user_info (UserInfo): User information extracted from bearer token.  Returns:     None | JSONResponse: None if successful, otherwise ErrorResponse.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="fragmentId"></param>
        /// <param name="conversationFragmentPatch"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ErrorResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<ErrorResponse>> PatchConversationFragmentPreferenceV1MuseConversationConversationIdFragmentFragmentIdPatchWithHttpInfoAsync(string conversationId, string fragmentId, ConversationFragmentPatch conversationFragmentPatch, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Patch Conversation
        /// </summary>
        /// <remarks>
        /// Update conversation by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     body (ConversationPatchRequest): Patch request for changing conversation.     user_info (UserInfo): User information extracted from bearer token.  Returns:     None | JSONResponse: None if successful, otherwise ErrorResponse.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="conversationPatchRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ErrorResponse</returns>
        [Obsolete]
        System.Threading.Tasks.Task<ErrorResponse> PatchConversationMuseConversationConversationIdPatchAsync(string conversationId, ConversationPatchRequest conversationPatchRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Patch Conversation
        /// </summary>
        /// <remarks>
        /// Update conversation by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     body (ConversationPatchRequest): Patch request for changing conversation.     user_info (UserInfo): User information extracted from bearer token.  Returns:     None | JSONResponse: None if successful, otherwise ErrorResponse.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="conversationPatchRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ErrorResponse)</returns>
        [Obsolete]
        System.Threading.Tasks.Task<ApiResponse<ErrorResponse>> PatchConversationMuseConversationConversationIdPatchWithHttpInfoAsync(string conversationId, ConversationPatchRequest conversationPatchRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Patch Conversation
        /// </summary>
        /// <remarks>
        /// Update conversation by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     body (ConversationPatchRequest): Patch request for changing conversation.     user_info (UserInfo): User information extracted from bearer token.  Returns:     None | JSONResponse: None if successful, otherwise ErrorResponse.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="conversationPatchRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ErrorResponse</returns>
        System.Threading.Tasks.Task<ErrorResponse> PatchConversationV1MuseConversationConversationIdPatchAsync(string conversationId, ConversationPatchRequest conversationPatchRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Patch Conversation
        /// </summary>
        /// <remarks>
        /// Update conversation by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     body (ConversationPatchRequest): Patch request for changing conversation.     user_info (UserInfo): User information extracted from bearer token.  Returns:     None | JSONResponse: None if successful, otherwise ErrorResponse.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="conversationPatchRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ErrorResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<ErrorResponse>> PatchConversationV1MuseConversationConversationIdPatchWithHttpInfoAsync(string conversationId, ConversationPatchRequest conversationPatchRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Smart Context
        /// </summary>
        /// <remarks>
        /// Handle smart context requests.  Args:     request (Request): FastAPI request object.     body (SmartContextRequest): Smart context request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     SmartContextResponse | JSONResponse:         Either smart context response or JSON error message.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="smartContextRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of SmartContextResponse</returns>
        [Obsolete]
        System.Threading.Tasks.Task<SmartContextResponse> SmartContextSmartContextPostAsync(SmartContextRequest smartContextRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Smart Context
        /// </summary>
        /// <remarks>
        /// Handle smart context requests.  Args:     request (Request): FastAPI request object.     body (SmartContextRequest): Smart context request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     SmartContextResponse | JSONResponse:         Either smart context response or JSON error message.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="smartContextRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (SmartContextResponse)</returns>
        [Obsolete]
        System.Threading.Tasks.Task<ApiResponse<SmartContextResponse>> SmartContextSmartContextPostWithHttpInfoAsync(SmartContextRequest smartContextRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        /// <summary>
        /// Smart Context
        /// </summary>
        /// <remarks>
        /// Handle smart context requests.  Args:     request (Request): FastAPI request object.     body (SmartContextRequest): Smart context request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     SmartContextResponse | JSONResponse:         Either smart context response or JSON error message.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="smartContextRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of SmartContextResponse</returns>
        System.Threading.Tasks.Task<SmartContextResponse> SmartContextV1SmartContextPostAsync(SmartContextRequest smartContextRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));

        /// <summary>
        /// Smart Context
        /// </summary>
        /// <remarks>
        /// Handle smart context requests.  Args:     request (Request): FastAPI request object.     body (SmartContextRequest): Smart context request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     SmartContextResponse | JSONResponse:         Either smart context response or JSON error message.
        /// </remarks>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="smartContextRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (SmartContextResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<SmartContextResponse>> SmartContextV1SmartContextPostWithHttpInfoAsync(SmartContextRequest smartContextRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken));
        #endregion Asynchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    internal interface IDefaultApi : IDefaultApiSync, IDefaultApiAsync
    {

    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    internal partial class DefaultApi : IDisposable, IDefaultApi
    {
        private Unity.Behavior.WebApi.Client.ExceptionFactory _exceptionFactory = (name, response) => null;

        /// <summary>
        /// Initializes a new instance of the <see cref="DefaultApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <returns></returns>
        public DefaultApi() : this((string)null)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DefaultApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="basePath">The target service's base path in URL format.</param>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        public DefaultApi(string basePath)
        {
            this.Configuration = Unity.Behavior.WebApi.Client.Configuration.MergeConfigurations(
                Unity.Behavior.WebApi.Client.GlobalConfiguration.Instance,
                new Unity.Behavior.WebApi.Client.Configuration { BasePath = basePath }
            );
            this.ApiClient = new Unity.Behavior.WebApi.Client.ApiClient(this.Configuration.BasePath);
            this.Client =  this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            this.ExceptionFactory = Unity.Behavior.WebApi.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DefaultApi"/> class using Configuration object.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="configuration">An instance of Configuration.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        public DefaultApi(Unity.Behavior.WebApi.Client.Configuration configuration)
        {
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Configuration = Unity.Behavior.WebApi.Client.Configuration.MergeConfigurations(
                Unity.Behavior.WebApi.Client.GlobalConfiguration.Instance,
                configuration
            );
            this.ApiClient = new Unity.Behavior.WebApi.Client.ApiClient(this.Configuration.BasePath);
            this.Client = this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            ExceptionFactory = Unity.Behavior.WebApi.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DefaultApi"/> class
        /// using a Configuration object and client instance.
        /// </summary>
        /// <param name="client">The client interface for synchronous API access.</param>
        /// <param name="asyncClient">The client interface for asynchronous API access.</param>
        /// <param name="configuration">The configuration object.</param>
        /// <exception cref="ArgumentNullException"></exception>
        public DefaultApi(Unity.Behavior.WebApi.Client.ISynchronousClient client, Unity.Behavior.WebApi.Client.IAsynchronousClient asyncClient, Unity.Behavior.WebApi.Client.IReadableConfiguration configuration)
        {
            if (client == null) throw new ArgumentNullException("client");
            if (asyncClient == null) throw new ArgumentNullException("asyncClient");
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Client = client;
            this.AsynchronousClient = asyncClient;
            this.Configuration = configuration;
            this.ExceptionFactory = Unity.Behavior.WebApi.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Disposes resources if they were created by us
        /// </summary>
        public void Dispose()
        {
            this.ApiClient?.Dispose();
        }

        /// <summary>
        /// Holds the ApiClient if created
        /// </summary>
        public Unity.Behavior.WebApi.Client.ApiClient ApiClient { get; set; } = null;

        /// <summary>
        /// The client for accessing this underlying API asynchronously.
        /// </summary>
        public Unity.Behavior.WebApi.Client.IAsynchronousClient AsynchronousClient { get; set; }

        /// <summary>
        /// The client for accessing this underlying API synchronously.
        /// </summary>
        public Unity.Behavior.WebApi.Client.ISynchronousClient Client { get; set; }

        /// <summary>
        /// Gets the base path of the API client.
        /// </summary>
        /// <value>The base path</value>
        public string GetBasePath()
        {
            return this.Configuration.BasePath;
        }

        /// <summary>
        /// Gets or sets the configuration object
        /// </summary>
        /// <value>An instance of the Configuration</value>
        public Unity.Behavior.WebApi.Client.IReadableConfiguration Configuration { get; set; }

        /// <summary>
        /// Provides a factory method hook for the creation of exceptions.
        /// </summary>
        public Unity.Behavior.WebApi.Client.ExceptionFactory ExceptionFactory
        {
            get
            {
                if (_exceptionFactory != null && _exceptionFactory.GetInvocationList().Length > 1)
                {
                    throw new InvalidOperationException("Multicast delegate for ExceptionFactory is unsupported.");
                }
                return _exceptionFactory;
            }
            set { _exceptionFactory = value; }
        }

        /// <summary>
        /// Action Code Repair Agent action code repairing route for repairing generated csharp scripts.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actionCodeRepairRequest"></param>
        /// <returns>Object</returns>
        [Obsolete]
        public Object ActionCodeRepairMuseAgentCodeRepairPost(ActionCodeRepairRequest actionCodeRepairRequest)
        {
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = ActionCodeRepairMuseAgentCodeRepairPostWithHttpInfo(actionCodeRepairRequest);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Action Code Repair Agent action code repairing route for repairing generated csharp scripts.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actionCodeRepairRequest"></param>
        /// <returns>ApiResponse of Object</returns>
        [Obsolete]
        public Unity.Behavior.WebApi.Client.ApiResponse<Object> ActionCodeRepairMuseAgentCodeRepairPostWithHttpInfo(ActionCodeRepairRequest actionCodeRepairRequest)
        {
            // verify the required parameter 'actionCodeRepairRequest' is set
            if (actionCodeRepairRequest == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'actionCodeRepairRequest' when calling DefaultApi->ActionCodeRepairMuseAgentCodeRepairPost");

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = actionCodeRepairRequest;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<Object>("/muse/agent/code_repair", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActionCodeRepairMuseAgentCodeRepairPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Action Code Repair Agent action code repairing route for repairing generated csharp scripts.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actionCodeRepairRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<Object> ActionCodeRepairMuseAgentCodeRepairPostAsync(ActionCodeRepairRequest actionCodeRepairRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = ActionCodeRepairMuseAgentCodeRepairPostWithHttpInfoAsync(actionCodeRepairRequest, cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Action Code Repair Agent action code repairing route for repairing generated csharp scripts.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actionCodeRepairRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<Object>> ActionCodeRepairMuseAgentCodeRepairPostWithHttpInfoAsync(ActionCodeRepairRequest actionCodeRepairRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'actionCodeRepairRequest' is set
            if (actionCodeRepairRequest == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'actionCodeRepairRequest' when calling DefaultApi->ActionCodeRepairMuseAgentCodeRepairPost");


            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = actionCodeRepairRequest;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request

            var task = this.AsynchronousClient.PostAsync<Object>("/muse/agent/code_repair", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActionCodeRepairMuseAgentCodeRepairPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Action Code Repair Agent action code repairing route for repairing generated csharp scripts.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actionCodeRepairRequest"></param>
        /// <returns>Object</returns>
        public Object ActionCodeRepairV1MuseAgentCodeRepairPost(ActionCodeRepairRequest actionCodeRepairRequest)
        {
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = ActionCodeRepairV1MuseAgentCodeRepairPostWithHttpInfo(actionCodeRepairRequest);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Action Code Repair Agent action code repairing route for repairing generated csharp scripts.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actionCodeRepairRequest"></param>
        /// <returns>ApiResponse of Object</returns>
        public Unity.Behavior.WebApi.Client.ApiResponse<Object> ActionCodeRepairV1MuseAgentCodeRepairPostWithHttpInfo(ActionCodeRepairRequest actionCodeRepairRequest)
        {
            // verify the required parameter 'actionCodeRepairRequest' is set
            if (actionCodeRepairRequest == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'actionCodeRepairRequest' when calling DefaultApi->ActionCodeRepairV1MuseAgentCodeRepairPost");

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = actionCodeRepairRequest;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<Object>("/v1/muse/agent/code_repair", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActionCodeRepairV1MuseAgentCodeRepairPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Action Code Repair Agent action code repairing route for repairing generated csharp scripts.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actionCodeRepairRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        public async System.Threading.Tasks.Task<Object> ActionCodeRepairV1MuseAgentCodeRepairPostAsync(ActionCodeRepairRequest actionCodeRepairRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = ActionCodeRepairV1MuseAgentCodeRepairPostWithHttpInfoAsync(actionCodeRepairRequest, cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Action Code Repair Agent action code repairing route for repairing generated csharp scripts.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actionCodeRepairRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<Object>> ActionCodeRepairV1MuseAgentCodeRepairPostWithHttpInfoAsync(ActionCodeRepairRequest actionCodeRepairRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'actionCodeRepairRequest' is set
            if (actionCodeRepairRequest == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'actionCodeRepairRequest' when calling DefaultApi->ActionCodeRepairV1MuseAgentCodeRepairPost");


            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = actionCodeRepairRequest;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request

            var task = this.AsynchronousClient.PostAsync<Object>("/v1/muse/agent/code_repair", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActionCodeRepairV1MuseAgentCodeRepairPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Action Agent action route for performing actions in the editor.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actionRequest"></param>
        /// <returns>Object</returns>
        [Obsolete]
        public Object ActionMuseAgentActionPost(ActionRequest actionRequest)
        {
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = ActionMuseAgentActionPostWithHttpInfo(actionRequest);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Action Agent action route for performing actions in the editor.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actionRequest"></param>
        /// <returns>ApiResponse of Object</returns>
        [Obsolete]
        public Unity.Behavior.WebApi.Client.ApiResponse<Object> ActionMuseAgentActionPostWithHttpInfo(ActionRequest actionRequest)
        {
            // verify the required parameter 'actionRequest' is set
            if (actionRequest == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'actionRequest' when calling DefaultApi->ActionMuseAgentActionPost");

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = actionRequest;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<Object>("/muse/agent/action", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActionMuseAgentActionPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Action Agent action route for performing actions in the editor.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actionRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<Object> ActionMuseAgentActionPostAsync(ActionRequest actionRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = ActionMuseAgentActionPostWithHttpInfoAsync(actionRequest, cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Action Agent action route for performing actions in the editor.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actionRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<Object>> ActionMuseAgentActionPostWithHttpInfoAsync(ActionRequest actionRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'actionRequest' is set
            if (actionRequest == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'actionRequest' when calling DefaultApi->ActionMuseAgentActionPost");


            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = actionRequest;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request

            var task = this.AsynchronousClient.PostAsync<Object>("/muse/agent/action", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActionMuseAgentActionPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Action Agent action route for performing actions in the editor.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actionRequest"></param>
        /// <returns>Object</returns>
        public Object ActionV1MuseAgentActionPost(ActionRequest actionRequest)
        {
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = ActionV1MuseAgentActionPostWithHttpInfo(actionRequest);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Action Agent action route for performing actions in the editor.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actionRequest"></param>
        /// <returns>ApiResponse of Object</returns>
        public Unity.Behavior.WebApi.Client.ApiResponse<Object> ActionV1MuseAgentActionPostWithHttpInfo(ActionRequest actionRequest)
        {
            // verify the required parameter 'actionRequest' is set
            if (actionRequest == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'actionRequest' when calling DefaultApi->ActionV1MuseAgentActionPost");

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = actionRequest;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<Object>("/v1/muse/agent/action", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActionV1MuseAgentActionPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Action Agent action route for performing actions in the editor.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actionRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        public async System.Threading.Tasks.Task<Object> ActionV1MuseAgentActionPostAsync(ActionRequest actionRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = ActionV1MuseAgentActionPostWithHttpInfoAsync(actionRequest, cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Action Agent action route for performing actions in the editor.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actionRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<Object>> ActionV1MuseAgentActionPostWithHttpInfoAsync(ActionRequest actionRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'actionRequest' is set
            if (actionRequest == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'actionRequest' when calling DefaultApi->ActionV1MuseAgentActionPost");


            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = actionRequest;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request

            var task = this.AsynchronousClient.PostAsync<Object>("/v1/muse/agent/action", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActionV1MuseAgentActionPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Chat Chat with Muse.  Args:     request (Request): FastAPI request object.     body (ChatRequest): Chat request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     StreamingResponse | ChatResponse | JSONResponse:         Either streaming response, at-once chat response, or JSON error message.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="chatRequest"></param>
        /// <returns>Object</returns>
        [Obsolete]
        public Object ChatMuseChatPost(ChatRequest chatRequest)
        {
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = ChatMuseChatPostWithHttpInfo(chatRequest);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Chat Chat with Muse.  Args:     request (Request): FastAPI request object.     body (ChatRequest): Chat request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     StreamingResponse | ChatResponse | JSONResponse:         Either streaming response, at-once chat response, or JSON error message.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="chatRequest"></param>
        /// <returns>ApiResponse of Object</returns>
        [Obsolete]
        public Unity.Behavior.WebApi.Client.ApiResponse<Object> ChatMuseChatPostWithHttpInfo(ChatRequest chatRequest)
        {
            // verify the required parameter 'chatRequest' is set
            if (chatRequest == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'chatRequest' when calling DefaultApi->ChatMuseChatPost");

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = chatRequest;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<Object>("/muse/chat", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ChatMuseChatPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Chat Chat with Muse.  Args:     request (Request): FastAPI request object.     body (ChatRequest): Chat request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     StreamingResponse | ChatResponse | JSONResponse:         Either streaming response, at-once chat response, or JSON error message.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="chatRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<Object> ChatMuseChatPostAsync(ChatRequest chatRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = ChatMuseChatPostWithHttpInfoAsync(chatRequest, cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Chat Chat with Muse.  Args:     request (Request): FastAPI request object.     body (ChatRequest): Chat request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     StreamingResponse | ChatResponse | JSONResponse:         Either streaming response, at-once chat response, or JSON error message.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="chatRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<Object>> ChatMuseChatPostWithHttpInfoAsync(ChatRequest chatRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'chatRequest' is set
            if (chatRequest == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'chatRequest' when calling DefaultApi->ChatMuseChatPost");


            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = chatRequest;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request

            var task = this.AsynchronousClient.PostAsync<Object>("/muse/chat", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ChatMuseChatPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Chat Chat with Muse.  Args:     request (Request): FastAPI request object.     body (ChatRequest): Chat request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     StreamingResponse | ChatResponse | JSONResponse:         Either streaming response, at-once chat response, or JSON error message.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="chatRequest"></param>
        /// <returns>Object</returns>
        public Object ChatV1MuseChatPost(ChatRequest chatRequest)
        {
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = ChatV1MuseChatPostWithHttpInfo(chatRequest);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Chat Chat with Muse.  Args:     request (Request): FastAPI request object.     body (ChatRequest): Chat request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     StreamingResponse | ChatResponse | JSONResponse:         Either streaming response, at-once chat response, or JSON error message.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="chatRequest"></param>
        /// <returns>ApiResponse of Object</returns>
        public Unity.Behavior.WebApi.Client.ApiResponse<Object> ChatV1MuseChatPostWithHttpInfo(ChatRequest chatRequest)
        {
            // verify the required parameter 'chatRequest' is set
            if (chatRequest == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'chatRequest' when calling DefaultApi->ChatV1MuseChatPost");

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = chatRequest;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<Object>("/v1/muse/chat", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ChatV1MuseChatPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Chat Chat with Muse.  Args:     request (Request): FastAPI request object.     body (ChatRequest): Chat request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     StreamingResponse | ChatResponse | JSONResponse:         Either streaming response, at-once chat response, or JSON error message.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="chatRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        public async System.Threading.Tasks.Task<Object> ChatV1MuseChatPostAsync(ChatRequest chatRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = ChatV1MuseChatPostWithHttpInfoAsync(chatRequest, cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Chat Chat with Muse.  Args:     request (Request): FastAPI request object.     body (ChatRequest): Chat request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     StreamingResponse | ChatResponse | JSONResponse:         Either streaming response, at-once chat response, or JSON error message.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="chatRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<Object>> ChatV1MuseChatPostWithHttpInfoAsync(ChatRequest chatRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'chatRequest' is set
            if (chatRequest == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'chatRequest' when calling DefaultApi->ChatV1MuseChatPost");


            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = chatRequest;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request

            var task = this.AsynchronousClient.PostAsync<Object>("/v1/muse/chat", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ChatV1MuseChatPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Completion Handles completion requests for a conversational AI model and manages associated user conversations and analytics.  Args:     request (Request): The request object, which provides access to all request-specific data.     body (LLMCompletionRequest): The request body containing data necessary for completion request.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Union[StreamingResponse, ChatResponse, JSONResponse]: Based on the &#x60;stream_response&#x60; flag in the request body,     this could be either a directly returned chat response, a streaming response,     or a JSON response containing the chat output.  Raises:     HTTPException: An error response with status code 500 in case of a failure during chat handling operations.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="lLMCompletionRequest"></param>
        /// <returns>Object</returns>
        [Obsolete]
        public Object CompletionMuseCompletionPost(LLMCompletionRequest lLMCompletionRequest)
        {
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = CompletionMuseCompletionPostWithHttpInfo(lLMCompletionRequest);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Completion Handles completion requests for a conversational AI model and manages associated user conversations and analytics.  Args:     request (Request): The request object, which provides access to all request-specific data.     body (LLMCompletionRequest): The request body containing data necessary for completion request.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Union[StreamingResponse, ChatResponse, JSONResponse]: Based on the &#x60;stream_response&#x60; flag in the request body,     this could be either a directly returned chat response, a streaming response,     or a JSON response containing the chat output.  Raises:     HTTPException: An error response with status code 500 in case of a failure during chat handling operations.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="lLMCompletionRequest"></param>
        /// <returns>ApiResponse of Object</returns>
        [Obsolete]
        public Unity.Behavior.WebApi.Client.ApiResponse<Object> CompletionMuseCompletionPostWithHttpInfo(LLMCompletionRequest lLMCompletionRequest)
        {
            // verify the required parameter 'lLMCompletionRequest' is set
            if (lLMCompletionRequest == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'lLMCompletionRequest' when calling DefaultApi->CompletionMuseCompletionPost");

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = lLMCompletionRequest;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<Object>("/muse/completion", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("CompletionMuseCompletionPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Completion Handles completion requests for a conversational AI model and manages associated user conversations and analytics.  Args:     request (Request): The request object, which provides access to all request-specific data.     body (LLMCompletionRequest): The request body containing data necessary for completion request.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Union[StreamingResponse, ChatResponse, JSONResponse]: Based on the &#x60;stream_response&#x60; flag in the request body,     this could be either a directly returned chat response, a streaming response,     or a JSON response containing the chat output.  Raises:     HTTPException: An error response with status code 500 in case of a failure during chat handling operations.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="lLMCompletionRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<Object> CompletionMuseCompletionPostAsync(LLMCompletionRequest lLMCompletionRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = CompletionMuseCompletionPostWithHttpInfoAsync(lLMCompletionRequest, cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Completion Handles completion requests for a conversational AI model and manages associated user conversations and analytics.  Args:     request (Request): The request object, which provides access to all request-specific data.     body (LLMCompletionRequest): The request body containing data necessary for completion request.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Union[StreamingResponse, ChatResponse, JSONResponse]: Based on the &#x60;stream_response&#x60; flag in the request body,     this could be either a directly returned chat response, a streaming response,     or a JSON response containing the chat output.  Raises:     HTTPException: An error response with status code 500 in case of a failure during chat handling operations.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="lLMCompletionRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<Object>> CompletionMuseCompletionPostWithHttpInfoAsync(LLMCompletionRequest lLMCompletionRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'lLMCompletionRequest' is set
            if (lLMCompletionRequest == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'lLMCompletionRequest' when calling DefaultApi->CompletionMuseCompletionPost");


            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = lLMCompletionRequest;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request

            var task = this.AsynchronousClient.PostAsync<Object>("/muse/completion", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("CompletionMuseCompletionPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Completion Handles completion requests for a conversational AI model and manages associated user conversations and analytics.  Args:     request (Request): The request object, which provides access to all request-specific data.     body (LLMCompletionRequest): The request body containing data necessary for completion request.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Union[StreamingResponse, ChatResponse, JSONResponse]: Based on the &#x60;stream_response&#x60; flag in the request body,     this could be either a directly returned chat response, a streaming response,     or a JSON response containing the chat output.  Raises:     HTTPException: An error response with status code 500 in case of a failure during chat handling operations.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="lLMCompletionRequest"></param>
        /// <returns>Object</returns>
        public Object CompletionV1MuseCompletionPost(LLMCompletionRequest lLMCompletionRequest)
        {
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = CompletionV1MuseCompletionPostWithHttpInfo(lLMCompletionRequest);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Completion Handles completion requests for a conversational AI model and manages associated user conversations and analytics.  Args:     request (Request): The request object, which provides access to all request-specific data.     body (LLMCompletionRequest): The request body containing data necessary for completion request.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Union[StreamingResponse, ChatResponse, JSONResponse]: Based on the &#x60;stream_response&#x60; flag in the request body,     this could be either a directly returned chat response, a streaming response,     or a JSON response containing the chat output.  Raises:     HTTPException: An error response with status code 500 in case of a failure during chat handling operations.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="lLMCompletionRequest"></param>
        /// <returns>ApiResponse of Object</returns>
        public Unity.Behavior.WebApi.Client.ApiResponse<Object> CompletionV1MuseCompletionPostWithHttpInfo(LLMCompletionRequest lLMCompletionRequest)
        {
            // verify the required parameter 'lLMCompletionRequest' is set
            if (lLMCompletionRequest == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'lLMCompletionRequest' when calling DefaultApi->CompletionV1MuseCompletionPost");

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = lLMCompletionRequest;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<Object>("/v1/muse/completion", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("CompletionV1MuseCompletionPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Completion Handles completion requests for a conversational AI model and manages associated user conversations and analytics.  Args:     request (Request): The request object, which provides access to all request-specific data.     body (LLMCompletionRequest): The request body containing data necessary for completion request.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Union[StreamingResponse, ChatResponse, JSONResponse]: Based on the &#x60;stream_response&#x60; flag in the request body,     this could be either a directly returned chat response, a streaming response,     or a JSON response containing the chat output.  Raises:     HTTPException: An error response with status code 500 in case of a failure during chat handling operations.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="lLMCompletionRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        public async System.Threading.Tasks.Task<Object> CompletionV1MuseCompletionPostAsync(LLMCompletionRequest lLMCompletionRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = CompletionV1MuseCompletionPostWithHttpInfoAsync(lLMCompletionRequest, cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Completion Handles completion requests for a conversational AI model and manages associated user conversations and analytics.  Args:     request (Request): The request object, which provides access to all request-specific data.     body (LLMCompletionRequest): The request body containing data necessary for completion request.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Union[StreamingResponse, ChatResponse, JSONResponse]: Based on the &#x60;stream_response&#x60; flag in the request body,     this could be either a directly returned chat response, a streaming response,     or a JSON response containing the chat output.  Raises:     HTTPException: An error response with status code 500 in case of a failure during chat handling operations.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="lLMCompletionRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<Object>> CompletionV1MuseCompletionPostWithHttpInfoAsync(LLMCompletionRequest lLMCompletionRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'lLMCompletionRequest' is set
            if (lLMCompletionRequest == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'lLMCompletionRequest' when calling DefaultApi->CompletionV1MuseCompletionPost");


            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = lLMCompletionRequest;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request

            var task = this.AsynchronousClient.PostAsync<Object>("/v1/muse/completion", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("CompletionV1MuseCompletionPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Create Conversation
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="createConversationRequest"></param>
        /// <returns>Conversation</returns>
        [Obsolete]
        public Conversation CreateConversationMuseConversationPost(CreateConversationRequest createConversationRequest)
        {
            Unity.Behavior.WebApi.Client.ApiResponse<Conversation> localVarResponse = CreateConversationMuseConversationPostWithHttpInfo(createConversationRequest);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Create Conversation
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="createConversationRequest"></param>
        /// <returns>ApiResponse of Conversation</returns>
        [Obsolete]
        public Unity.Behavior.WebApi.Client.ApiResponse<Conversation> CreateConversationMuseConversationPostWithHttpInfo(CreateConversationRequest createConversationRequest)
        {
            // verify the required parameter 'createConversationRequest' is set
            if (createConversationRequest == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'createConversationRequest' when calling DefaultApi->CreateConversationMuseConversationPost");

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = createConversationRequest;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<Conversation>("/muse/conversation", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("CreateConversationMuseConversationPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Create Conversation
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="createConversationRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Conversation</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<Conversation> CreateConversationMuseConversationPostAsync(CreateConversationRequest createConversationRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = CreateConversationMuseConversationPostWithHttpInfoAsync(createConversationRequest, cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<Conversation> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<Conversation> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Create Conversation
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="createConversationRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Conversation)</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<Conversation>> CreateConversationMuseConversationPostWithHttpInfoAsync(CreateConversationRequest createConversationRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'createConversationRequest' is set
            if (createConversationRequest == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'createConversationRequest' when calling DefaultApi->CreateConversationMuseConversationPost");


            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = createConversationRequest;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request

            var task = this.AsynchronousClient.PostAsync<Conversation>("/muse/conversation", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("CreateConversationMuseConversationPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Create Conversation
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="createConversationRequest"></param>
        /// <returns>Conversation</returns>
        public Conversation CreateConversationV1MuseConversationPost(CreateConversationRequest createConversationRequest)
        {
            Unity.Behavior.WebApi.Client.ApiResponse<Conversation> localVarResponse = CreateConversationV1MuseConversationPostWithHttpInfo(createConversationRequest);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Create Conversation
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="createConversationRequest"></param>
        /// <returns>ApiResponse of Conversation</returns>
        public Unity.Behavior.WebApi.Client.ApiResponse<Conversation> CreateConversationV1MuseConversationPostWithHttpInfo(CreateConversationRequest createConversationRequest)
        {
            // verify the required parameter 'createConversationRequest' is set
            if (createConversationRequest == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'createConversationRequest' when calling DefaultApi->CreateConversationV1MuseConversationPost");

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = createConversationRequest;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<Conversation>("/v1/muse/conversation", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("CreateConversationV1MuseConversationPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Create Conversation
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="createConversationRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Conversation</returns>
        public async System.Threading.Tasks.Task<Conversation> CreateConversationV1MuseConversationPostAsync(CreateConversationRequest createConversationRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = CreateConversationV1MuseConversationPostWithHttpInfoAsync(createConversationRequest, cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<Conversation> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<Conversation> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Create Conversation
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="createConversationRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Conversation)</returns>
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<Conversation>> CreateConversationV1MuseConversationPostWithHttpInfoAsync(CreateConversationRequest createConversationRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'createConversationRequest' is set
            if (createConversationRequest == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'createConversationRequest' when calling DefaultApi->CreateConversationV1MuseConversationPost");


            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = createConversationRequest;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request

            var task = this.AsynchronousClient.PostAsync<Conversation>("/v1/muse/conversation", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("CreateConversationV1MuseConversationPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Delete Conversation Fragment Delete conversation fragment by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     fragment_id (str): Conversation fragment ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="fragmentId"></param>
        /// <returns>ErrorResponse</returns>
        [Obsolete]
        public ErrorResponse DeleteConversationFragmentMuseConversationConversationIdFragmentFragmentIdDelete(string conversationId, string fragmentId)
        {
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = DeleteConversationFragmentMuseConversationConversationIdFragmentFragmentIdDeleteWithHttpInfo(conversationId, fragmentId);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Delete Conversation Fragment Delete conversation fragment by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     fragment_id (str): Conversation fragment ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="fragmentId"></param>
        /// <returns>ApiResponse of ErrorResponse</returns>
        [Obsolete]
        public Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> DeleteConversationFragmentMuseConversationConversationIdFragmentFragmentIdDeleteWithHttpInfo(string conversationId, string fragmentId)
        {
            // verify the required parameter 'conversationId' is set
            if (conversationId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'conversationId' when calling DefaultApi->DeleteConversationFragmentMuseConversationConversationIdFragmentFragmentIdDelete");

            // verify the required parameter 'fragmentId' is set
            if (fragmentId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'fragmentId' when calling DefaultApi->DeleteConversationFragmentMuseConversationConversationIdFragmentFragmentIdDelete");

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("conversation_id", Unity.Behavior.WebApi.Client.ClientUtils.ParameterToString(conversationId)); // path parameter
            localVarRequestOptions.PathParameters.Add("fragment_id", Unity.Behavior.WebApi.Client.ClientUtils.ParameterToString(fragmentId)); // path parameter

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Delete<ErrorResponse>("/muse/conversation/{conversation_id}/fragment/{fragment_id}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteConversationFragmentMuseConversationConversationIdFragmentFragmentIdDelete", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Delete Conversation Fragment Delete conversation fragment by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     fragment_id (str): Conversation fragment ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="fragmentId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ErrorResponse</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<ErrorResponse> DeleteConversationFragmentMuseConversationConversationIdFragmentFragmentIdDeleteAsync(string conversationId, string fragmentId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = DeleteConversationFragmentMuseConversationConversationIdFragmentFragmentIdDeleteWithHttpInfoAsync(conversationId, fragmentId, cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Delete Conversation Fragment Delete conversation fragment by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     fragment_id (str): Conversation fragment ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="fragmentId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ErrorResponse)</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse>> DeleteConversationFragmentMuseConversationConversationIdFragmentFragmentIdDeleteWithHttpInfoAsync(string conversationId, string fragmentId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'conversationId' is set
            if (conversationId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'conversationId' when calling DefaultApi->DeleteConversationFragmentMuseConversationConversationIdFragmentFragmentIdDelete");

            // verify the required parameter 'fragmentId' is set
            if (fragmentId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'fragmentId' when calling DefaultApi->DeleteConversationFragmentMuseConversationConversationIdFragmentFragmentIdDelete");


            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("conversation_id", Unity.Behavior.WebApi.Client.ClientUtils.ParameterToString(conversationId)); // path parameter
            localVarRequestOptions.PathParameters.Add("fragment_id", Unity.Behavior.WebApi.Client.ClientUtils.ParameterToString(fragmentId)); // path parameter

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request

            var task = this.AsynchronousClient.DeleteAsync<ErrorResponse>("/muse/conversation/{conversation_id}/fragment/{fragment_id}", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteConversationFragmentMuseConversationConversationIdFragmentFragmentIdDelete", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Delete Conversation Fragment Delete conversation fragment by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     fragment_id (str): Conversation fragment ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="fragmentId"></param>
        /// <returns>ErrorResponse</returns>
        public ErrorResponse DeleteConversationFragmentV1MuseConversationConversationIdFragmentFragmentIdDelete(string conversationId, string fragmentId)
        {
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = DeleteConversationFragmentV1MuseConversationConversationIdFragmentFragmentIdDeleteWithHttpInfo(conversationId, fragmentId);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Delete Conversation Fragment Delete conversation fragment by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     fragment_id (str): Conversation fragment ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="fragmentId"></param>
        /// <returns>ApiResponse of ErrorResponse</returns>
        public Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> DeleteConversationFragmentV1MuseConversationConversationIdFragmentFragmentIdDeleteWithHttpInfo(string conversationId, string fragmentId)
        {
            // verify the required parameter 'conversationId' is set
            if (conversationId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'conversationId' when calling DefaultApi->DeleteConversationFragmentV1MuseConversationConversationIdFragmentFragmentIdDelete");

            // verify the required parameter 'fragmentId' is set
            if (fragmentId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'fragmentId' when calling DefaultApi->DeleteConversationFragmentV1MuseConversationConversationIdFragmentFragmentIdDelete");

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("conversation_id", Unity.Behavior.WebApi.Client.ClientUtils.ParameterToString(conversationId)); // path parameter
            localVarRequestOptions.PathParameters.Add("fragment_id", Unity.Behavior.WebApi.Client.ClientUtils.ParameterToString(fragmentId)); // path parameter

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Delete<ErrorResponse>("/v1/muse/conversation/{conversation_id}/fragment/{fragment_id}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteConversationFragmentV1MuseConversationConversationIdFragmentFragmentIdDelete", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Delete Conversation Fragment Delete conversation fragment by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     fragment_id (str): Conversation fragment ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="fragmentId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ErrorResponse</returns>
        public async System.Threading.Tasks.Task<ErrorResponse> DeleteConversationFragmentV1MuseConversationConversationIdFragmentFragmentIdDeleteAsync(string conversationId, string fragmentId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = DeleteConversationFragmentV1MuseConversationConversationIdFragmentFragmentIdDeleteWithHttpInfoAsync(conversationId, fragmentId, cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Delete Conversation Fragment Delete conversation fragment by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     fragment_id (str): Conversation fragment ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="fragmentId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ErrorResponse)</returns>
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse>> DeleteConversationFragmentV1MuseConversationConversationIdFragmentFragmentIdDeleteWithHttpInfoAsync(string conversationId, string fragmentId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'conversationId' is set
            if (conversationId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'conversationId' when calling DefaultApi->DeleteConversationFragmentV1MuseConversationConversationIdFragmentFragmentIdDelete");

            // verify the required parameter 'fragmentId' is set
            if (fragmentId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'fragmentId' when calling DefaultApi->DeleteConversationFragmentV1MuseConversationConversationIdFragmentFragmentIdDelete");


            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("conversation_id", Unity.Behavior.WebApi.Client.ClientUtils.ParameterToString(conversationId)); // path parameter
            localVarRequestOptions.PathParameters.Add("fragment_id", Unity.Behavior.WebApi.Client.ClientUtils.ParameterToString(fragmentId)); // path parameter

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request

            var task = this.AsynchronousClient.DeleteAsync<ErrorResponse>("/v1/muse/conversation/{conversation_id}/fragment/{fragment_id}", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteConversationFragmentV1MuseConversationConversationIdFragmentFragmentIdDelete", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Delete Conversation Delete conversation by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <returns>ErrorResponse</returns>
        [Obsolete]
        public ErrorResponse DeleteConversationMuseConversationConversationIdDelete(string conversationId)
        {
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = DeleteConversationMuseConversationConversationIdDeleteWithHttpInfo(conversationId);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Delete Conversation Delete conversation by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <returns>ApiResponse of ErrorResponse</returns>
        [Obsolete]
        public Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> DeleteConversationMuseConversationConversationIdDeleteWithHttpInfo(string conversationId)
        {
            // verify the required parameter 'conversationId' is set
            if (conversationId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'conversationId' when calling DefaultApi->DeleteConversationMuseConversationConversationIdDelete");

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("conversation_id", Unity.Behavior.WebApi.Client.ClientUtils.ParameterToString(conversationId)); // path parameter

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Delete<ErrorResponse>("/muse/conversation/{conversation_id}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteConversationMuseConversationConversationIdDelete", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Delete Conversation Delete conversation by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ErrorResponse</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<ErrorResponse> DeleteConversationMuseConversationConversationIdDeleteAsync(string conversationId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = DeleteConversationMuseConversationConversationIdDeleteWithHttpInfoAsync(conversationId, cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Delete Conversation Delete conversation by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ErrorResponse)</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse>> DeleteConversationMuseConversationConversationIdDeleteWithHttpInfoAsync(string conversationId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'conversationId' is set
            if (conversationId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'conversationId' when calling DefaultApi->DeleteConversationMuseConversationConversationIdDelete");


            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("conversation_id", Unity.Behavior.WebApi.Client.ClientUtils.ParameterToString(conversationId)); // path parameter

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request

            var task = this.AsynchronousClient.DeleteAsync<ErrorResponse>("/muse/conversation/{conversation_id}", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteConversationMuseConversationConversationIdDelete", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Delete Conversation Delete conversation by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <returns>ErrorResponse</returns>
        public ErrorResponse DeleteConversationV1MuseConversationConversationIdDelete(string conversationId)
        {
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = DeleteConversationV1MuseConversationConversationIdDeleteWithHttpInfo(conversationId);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Delete Conversation Delete conversation by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <returns>ApiResponse of ErrorResponse</returns>
        public Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> DeleteConversationV1MuseConversationConversationIdDeleteWithHttpInfo(string conversationId)
        {
            // verify the required parameter 'conversationId' is set
            if (conversationId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'conversationId' when calling DefaultApi->DeleteConversationV1MuseConversationConversationIdDelete");

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("conversation_id", Unity.Behavior.WebApi.Client.ClientUtils.ParameterToString(conversationId)); // path parameter

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Delete<ErrorResponse>("/v1/muse/conversation/{conversation_id}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteConversationV1MuseConversationConversationIdDelete", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Delete Conversation Delete conversation by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ErrorResponse</returns>
        public async System.Threading.Tasks.Task<ErrorResponse> DeleteConversationV1MuseConversationConversationIdDeleteAsync(string conversationId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = DeleteConversationV1MuseConversationConversationIdDeleteWithHttpInfoAsync(conversationId, cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Delete Conversation Delete conversation by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ErrorResponse)</returns>
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse>> DeleteConversationV1MuseConversationConversationIdDeleteWithHttpInfoAsync(string conversationId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'conversationId' is set
            if (conversationId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'conversationId' when calling DefaultApi->DeleteConversationV1MuseConversationConversationIdDelete");


            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("conversation_id", Unity.Behavior.WebApi.Client.ClientUtils.ParameterToString(conversationId)); // path parameter

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request

            var task = this.AsynchronousClient.DeleteAsync<ErrorResponse>("/v1/muse/conversation/{conversation_id}", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteConversationV1MuseConversationConversationIdDelete", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Delete Conversations By Tags Delete conversations by tags.  Args:     request (Request): FastAPI request object.     tags (list[str])): list of tags.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tags">List of tags to delete conversations by. (optional)</param>
        /// <returns>ErrorResponse</returns>
        [Obsolete]
        public ErrorResponse DeleteConversationsByTagsMuseConversationsByTagsDelete(List<string> tags = default(List<string>))
        {
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = DeleteConversationsByTagsMuseConversationsByTagsDeleteWithHttpInfo(tags);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Delete Conversations By Tags Delete conversations by tags.  Args:     request (Request): FastAPI request object.     tags (list[str])): list of tags.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tags">List of tags to delete conversations by. (optional)</param>
        /// <returns>ApiResponse of ErrorResponse</returns>
        [Obsolete]
        public Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> DeleteConversationsByTagsMuseConversationsByTagsDeleteWithHttpInfo(List<string> tags = default(List<string>))
        {
            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            if (tags != null)
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("multi", "tags", tags));
            }

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Delete<ErrorResponse>("/muse/conversations/by-tags", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteConversationsByTagsMuseConversationsByTagsDelete", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Delete Conversations By Tags Delete conversations by tags.  Args:     request (Request): FastAPI request object.     tags (list[str])): list of tags.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tags">List of tags to delete conversations by. (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ErrorResponse</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<ErrorResponse> DeleteConversationsByTagsMuseConversationsByTagsDeleteAsync(List<string> tags = default(List<string>), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = DeleteConversationsByTagsMuseConversationsByTagsDeleteWithHttpInfoAsync(tags, cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Delete Conversations By Tags Delete conversations by tags.  Args:     request (Request): FastAPI request object.     tags (list[str])): list of tags.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tags">List of tags to delete conversations by. (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ErrorResponse)</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse>> DeleteConversationsByTagsMuseConversationsByTagsDeleteWithHttpInfoAsync(List<string> tags = default(List<string>), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            if (tags != null)
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("multi", "tags", tags));
            }

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request

            var task = this.AsynchronousClient.DeleteAsync<ErrorResponse>("/muse/conversations/by-tags", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteConversationsByTagsMuseConversationsByTagsDelete", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Delete Conversations By Tags Delete conversations by tags.  Args:     request (Request): FastAPI request object.     tags (list[str])): list of tags.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tags">List of tags to delete conversations by. (optional)</param>
        /// <returns>ErrorResponse</returns>
        public ErrorResponse DeleteConversationsByTagsV1MuseConversationsByTagsDelete(List<string> tags = default(List<string>))
        {
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = DeleteConversationsByTagsV1MuseConversationsByTagsDeleteWithHttpInfo(tags);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Delete Conversations By Tags Delete conversations by tags.  Args:     request (Request): FastAPI request object.     tags (list[str])): list of tags.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tags">List of tags to delete conversations by. (optional)</param>
        /// <returns>ApiResponse of ErrorResponse</returns>
        public Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> DeleteConversationsByTagsV1MuseConversationsByTagsDeleteWithHttpInfo(List<string> tags = default(List<string>))
        {
            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            if (tags != null)
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("multi", "tags", tags));
            }

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Delete<ErrorResponse>("/v1/muse/conversations/by-tags", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteConversationsByTagsV1MuseConversationsByTagsDelete", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Delete Conversations By Tags Delete conversations by tags.  Args:     request (Request): FastAPI request object.     tags (list[str])): list of tags.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tags">List of tags to delete conversations by. (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ErrorResponse</returns>
        public async System.Threading.Tasks.Task<ErrorResponse> DeleteConversationsByTagsV1MuseConversationsByTagsDeleteAsync(List<string> tags = default(List<string>), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = DeleteConversationsByTagsV1MuseConversationsByTagsDeleteWithHttpInfoAsync(tags, cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Delete Conversations By Tags Delete conversations by tags.  Args:     request (Request): FastAPI request object.     tags (list[str])): list of tags.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tags">List of tags to delete conversations by. (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ErrorResponse)</returns>
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse>> DeleteConversationsByTagsV1MuseConversationsByTagsDeleteWithHttpInfoAsync(List<string> tags = default(List<string>), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            if (tags != null)
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("multi", "tags", tags));
            }

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request

            var task = this.AsynchronousClient.DeleteAsync<ErrorResponse>("/v1/muse/conversations/by-tags", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DeleteConversationsByTagsV1MuseConversationsByTagsDelete", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Feedback Provide feedback.  Args:     request (Request): FastAPI request object.     body (Feedback): Feedback request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="feedback"></param>
        /// <returns>ErrorResponse</returns>
        [Obsolete]
        public ErrorResponse FeedbackMuseFeedbackPost(Feedback feedback)
        {
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = FeedbackMuseFeedbackPostWithHttpInfo(feedback);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Feedback Provide feedback.  Args:     request (Request): FastAPI request object.     body (Feedback): Feedback request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="feedback"></param>
        /// <returns>ApiResponse of ErrorResponse</returns>
        [Obsolete]
        public Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> FeedbackMuseFeedbackPostWithHttpInfo(Feedback feedback)
        {
            // verify the required parameter 'feedback' is set
            if (feedback == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'feedback' when calling DefaultApi->FeedbackMuseFeedbackPost");

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = feedback;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<ErrorResponse>("/muse/feedback", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("FeedbackMuseFeedbackPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Feedback Provide feedback.  Args:     request (Request): FastAPI request object.     body (Feedback): Feedback request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="feedback"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ErrorResponse</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<ErrorResponse> FeedbackMuseFeedbackPostAsync(Feedback feedback, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = FeedbackMuseFeedbackPostWithHttpInfoAsync(feedback, cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Feedback Provide feedback.  Args:     request (Request): FastAPI request object.     body (Feedback): Feedback request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="feedback"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ErrorResponse)</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse>> FeedbackMuseFeedbackPostWithHttpInfoAsync(Feedback feedback, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'feedback' is set
            if (feedback == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'feedback' when calling DefaultApi->FeedbackMuseFeedbackPost");


            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = feedback;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request

            var task = this.AsynchronousClient.PostAsync<ErrorResponse>("/muse/feedback", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("FeedbackMuseFeedbackPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Feedback Provide feedback.  Args:     request (Request): FastAPI request object.     body (Feedback): Feedback request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="feedback"></param>
        /// <returns>ErrorResponse</returns>
        public ErrorResponse FeedbackV1MuseFeedbackPost(Feedback feedback)
        {
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = FeedbackV1MuseFeedbackPostWithHttpInfo(feedback);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Feedback Provide feedback.  Args:     request (Request): FastAPI request object.     body (Feedback): Feedback request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="feedback"></param>
        /// <returns>ApiResponse of ErrorResponse</returns>
        public Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> FeedbackV1MuseFeedbackPostWithHttpInfo(Feedback feedback)
        {
            // verify the required parameter 'feedback' is set
            if (feedback == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'feedback' when calling DefaultApi->FeedbackV1MuseFeedbackPost");

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = feedback;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<ErrorResponse>("/v1/muse/feedback", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("FeedbackV1MuseFeedbackPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Feedback Provide feedback.  Args:     request (Request): FastAPI request object.     body (Feedback): Feedback request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="feedback"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ErrorResponse</returns>
        public async System.Threading.Tasks.Task<ErrorResponse> FeedbackV1MuseFeedbackPostAsync(Feedback feedback, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = FeedbackV1MuseFeedbackPostWithHttpInfoAsync(feedback, cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Feedback Provide feedback.  Args:     request (Request): FastAPI request object.     body (Feedback): Feedback request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     Optional[JSONResponse]: Nothing if successful, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="feedback"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ErrorResponse)</returns>
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse>> FeedbackV1MuseFeedbackPostWithHttpInfoAsync(Feedback feedback, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'feedback' is set
            if (feedback == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'feedback' when calling DefaultApi->FeedbackV1MuseFeedbackPost");


            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = feedback;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request

            var task = this.AsynchronousClient.PostAsync<ErrorResponse>("/v1/muse/feedback", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("FeedbackV1MuseFeedbackPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get Conversation Get conversation by conversation ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     ClientConversation | JSONResponse:         ClientConversation corresponding to ID if it exists, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <returns>ResponseGetConversationMuseConversationConversationIdGet</returns>
        [Obsolete]
        public ResponseGetConversationMuseConversationConversationIdGet GetConversationMuseConversationConversationIdGet(string conversationId)
        {
            Unity.Behavior.WebApi.Client.ApiResponse<ResponseGetConversationMuseConversationConversationIdGet> localVarResponse = GetConversationMuseConversationConversationIdGetWithHttpInfo(conversationId);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get Conversation Get conversation by conversation ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     ClientConversation | JSONResponse:         ClientConversation corresponding to ID if it exists, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <returns>ApiResponse of ResponseGetConversationMuseConversationConversationIdGet</returns>
        [Obsolete]
        public Unity.Behavior.WebApi.Client.ApiResponse<ResponseGetConversationMuseConversationConversationIdGet> GetConversationMuseConversationConversationIdGetWithHttpInfo(string conversationId)
        {
            // verify the required parameter 'conversationId' is set
            if (conversationId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'conversationId' when calling DefaultApi->GetConversationMuseConversationConversationIdGet");

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("conversation_id", Unity.Behavior.WebApi.Client.ClientUtils.ParameterToString(conversationId)); // path parameter

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<ResponseGetConversationMuseConversationConversationIdGet>("/muse/conversation/{conversation_id}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetConversationMuseConversationConversationIdGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get Conversation Get conversation by conversation ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     ClientConversation | JSONResponse:         ClientConversation corresponding to ID if it exists, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ResponseGetConversationMuseConversationConversationIdGet</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<ResponseGetConversationMuseConversationConversationIdGet> GetConversationMuseConversationConversationIdGetAsync(string conversationId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = GetConversationMuseConversationConversationIdGetWithHttpInfoAsync(conversationId, cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<ResponseGetConversationMuseConversationConversationIdGet> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<ResponseGetConversationMuseConversationConversationIdGet> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get Conversation Get conversation by conversation ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     ClientConversation | JSONResponse:         ClientConversation corresponding to ID if it exists, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ResponseGetConversationMuseConversationConversationIdGet)</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<ResponseGetConversationMuseConversationConversationIdGet>> GetConversationMuseConversationConversationIdGetWithHttpInfoAsync(string conversationId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'conversationId' is set
            if (conversationId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'conversationId' when calling DefaultApi->GetConversationMuseConversationConversationIdGet");


            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("conversation_id", Unity.Behavior.WebApi.Client.ClientUtils.ParameterToString(conversationId)); // path parameter

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request

            var task = this.AsynchronousClient.GetAsync<ResponseGetConversationMuseConversationConversationIdGet>("/muse/conversation/{conversation_id}", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetConversationMuseConversationConversationIdGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get Conversation Get conversation by conversation ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     ClientConversation | JSONResponse:         ClientConversation corresponding to ID if it exists, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <returns>ResponseGetConversationV1MuseConversationConversationIdGet</returns>
        public ResponseGetConversationV1MuseConversationConversationIdGet GetConversationV1MuseConversationConversationIdGet(string conversationId)
        {
            Unity.Behavior.WebApi.Client.ApiResponse<ResponseGetConversationV1MuseConversationConversationIdGet> localVarResponse = GetConversationV1MuseConversationConversationIdGetWithHttpInfo(conversationId);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get Conversation Get conversation by conversation ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     ClientConversation | JSONResponse:         ClientConversation corresponding to ID if it exists, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <returns>ApiResponse of ResponseGetConversationV1MuseConversationConversationIdGet</returns>
        public Unity.Behavior.WebApi.Client.ApiResponse<ResponseGetConversationV1MuseConversationConversationIdGet> GetConversationV1MuseConversationConversationIdGetWithHttpInfo(string conversationId)
        {
            // verify the required parameter 'conversationId' is set
            if (conversationId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'conversationId' when calling DefaultApi->GetConversationV1MuseConversationConversationIdGet");

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("conversation_id", Unity.Behavior.WebApi.Client.ClientUtils.ParameterToString(conversationId)); // path parameter

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<ResponseGetConversationV1MuseConversationConversationIdGet>("/v1/muse/conversation/{conversation_id}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetConversationV1MuseConversationConversationIdGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get Conversation Get conversation by conversation ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     ClientConversation | JSONResponse:         ClientConversation corresponding to ID if it exists, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ResponseGetConversationV1MuseConversationConversationIdGet</returns>
        public async System.Threading.Tasks.Task<ResponseGetConversationV1MuseConversationConversationIdGet> GetConversationV1MuseConversationConversationIdGetAsync(string conversationId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = GetConversationV1MuseConversationConversationIdGetWithHttpInfoAsync(conversationId, cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<ResponseGetConversationV1MuseConversationConversationIdGet> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<ResponseGetConversationV1MuseConversationConversationIdGet> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get Conversation Get conversation by conversation ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     ClientConversation | JSONResponse:         ClientConversation corresponding to ID if it exists, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ResponseGetConversationV1MuseConversationConversationIdGet)</returns>
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<ResponseGetConversationV1MuseConversationConversationIdGet>> GetConversationV1MuseConversationConversationIdGetWithHttpInfoAsync(string conversationId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'conversationId' is set
            if (conversationId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'conversationId' when calling DefaultApi->GetConversationV1MuseConversationConversationIdGet");


            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("conversation_id", Unity.Behavior.WebApi.Client.ClientUtils.ParameterToString(conversationId)); // path parameter

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request

            var task = this.AsynchronousClient.GetAsync<ResponseGetConversationV1MuseConversationConversationIdGet>("/v1/muse/conversation/{conversation_id}", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetConversationV1MuseConversationConversationIdGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get Conversations Get conversation summaries for user conversations.  Args:     request (Request): FastAPI request object.     user_info (UserInfo): User information extracted from bearer token.     tags (Optional[str], optional): Project ID to filter conversations by. Defaults to None.     skip_project_tag (bool, optional): Whether to skip conversations with a project tag.     limit (int, optional): Number of conversations to return. Defaults to 100.     skip (int, optional): Number of conversations to skip. Defaults to 0.  Returns:     list[ConversationInfo]: List of conversation summaries for user.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tags"> (optional)</param>
        /// <param name="skipProjectTag"> (optional)</param>
        /// <param name="limit"> (optional, default to 100)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <returns>List&lt;ConversationInfo&gt;</returns>
        [Obsolete]
        public List<ConversationInfo> GetConversationsMuseConversationGet(string tags = default(string), bool? skipProjectTag = default(bool?), int? limit = default(int?), int? skip = default(int?))
        {
            Unity.Behavior.WebApi.Client.ApiResponse<List<ConversationInfo>> localVarResponse = GetConversationsMuseConversationGetWithHttpInfo(tags, skipProjectTag, limit, skip);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get Conversations Get conversation summaries for user conversations.  Args:     request (Request): FastAPI request object.     user_info (UserInfo): User information extracted from bearer token.     tags (Optional[str], optional): Project ID to filter conversations by. Defaults to None.     skip_project_tag (bool, optional): Whether to skip conversations with a project tag.     limit (int, optional): Number of conversations to return. Defaults to 100.     skip (int, optional): Number of conversations to skip. Defaults to 0.  Returns:     list[ConversationInfo]: List of conversation summaries for user.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tags"> (optional)</param>
        /// <param name="skipProjectTag"> (optional)</param>
        /// <param name="limit"> (optional, default to 100)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <returns>ApiResponse of List&lt;ConversationInfo&gt;</returns>
        [Obsolete]
        public Unity.Behavior.WebApi.Client.ApiResponse<List<ConversationInfo>> GetConversationsMuseConversationGetWithHttpInfo(string tags = default(string), bool? skipProjectTag = default(bool?), int? limit = default(int?), int? skip = default(int?))
        {
            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            if (tags != null)
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "tags", tags));
            }
            if (skipProjectTag != null)
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "skip_project_tag", skipProjectTag));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (skip != null)
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "skip", skip));
            }

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<List<ConversationInfo>>("/muse/conversation", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetConversationsMuseConversationGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get Conversations Get conversation summaries for user conversations.  Args:     request (Request): FastAPI request object.     user_info (UserInfo): User information extracted from bearer token.     tags (Optional[str], optional): Project ID to filter conversations by. Defaults to None.     skip_project_tag (bool, optional): Whether to skip conversations with a project tag.     limit (int, optional): Number of conversations to return. Defaults to 100.     skip (int, optional): Number of conversations to skip. Defaults to 0.  Returns:     list[ConversationInfo]: List of conversation summaries for user.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tags"> (optional)</param>
        /// <param name="skipProjectTag"> (optional)</param>
        /// <param name="limit"> (optional, default to 100)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of List&lt;ConversationInfo&gt;</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<List<ConversationInfo>> GetConversationsMuseConversationGetAsync(string tags = default(string), bool? skipProjectTag = default(bool?), int? limit = default(int?), int? skip = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = GetConversationsMuseConversationGetWithHttpInfoAsync(tags, skipProjectTag, limit, skip, cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<List<ConversationInfo>> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<List<ConversationInfo>> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get Conversations Get conversation summaries for user conversations.  Args:     request (Request): FastAPI request object.     user_info (UserInfo): User information extracted from bearer token.     tags (Optional[str], optional): Project ID to filter conversations by. Defaults to None.     skip_project_tag (bool, optional): Whether to skip conversations with a project tag.     limit (int, optional): Number of conversations to return. Defaults to 100.     skip (int, optional): Number of conversations to skip. Defaults to 0.  Returns:     list[ConversationInfo]: List of conversation summaries for user.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tags"> (optional)</param>
        /// <param name="skipProjectTag"> (optional)</param>
        /// <param name="limit"> (optional, default to 100)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (List&lt;ConversationInfo&gt;)</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<List<ConversationInfo>>> GetConversationsMuseConversationGetWithHttpInfoAsync(string tags = default(string), bool? skipProjectTag = default(bool?), int? limit = default(int?), int? skip = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            if (tags != null)
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "tags", tags));
            }
            if (skipProjectTag != null)
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "skip_project_tag", skipProjectTag));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (skip != null)
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "skip", skip));
            }

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request

            var task = this.AsynchronousClient.GetAsync<List<ConversationInfo>>("/muse/conversation", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetConversationsMuseConversationGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get Conversations Get conversation summaries for user conversations.  Args:     request (Request): FastAPI request object.     user_info (UserInfo): User information extracted from bearer token.     tags (Optional[str], optional): Project ID to filter conversations by. Defaults to None.     skip_project_tag (bool, optional): Whether to skip conversations with a project tag.     limit (int, optional): Number of conversations to return. Defaults to 100.     skip (int, optional): Number of conversations to skip. Defaults to 0.  Returns:     list[ConversationInfo]: List of conversation summaries for user.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tags"> (optional)</param>
        /// <param name="skipProjectTag"> (optional)</param>
        /// <param name="limit"> (optional, default to 100)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <returns>List&lt;ConversationInfo&gt;</returns>
        public List<ConversationInfo> GetConversationsV1MuseConversationGet(string tags = default(string), bool? skipProjectTag = default(bool?), int? limit = default(int?), int? skip = default(int?))
        {
            Unity.Behavior.WebApi.Client.ApiResponse<List<ConversationInfo>> localVarResponse = GetConversationsV1MuseConversationGetWithHttpInfo(tags, skipProjectTag, limit, skip);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get Conversations Get conversation summaries for user conversations.  Args:     request (Request): FastAPI request object.     user_info (UserInfo): User information extracted from bearer token.     tags (Optional[str], optional): Project ID to filter conversations by. Defaults to None.     skip_project_tag (bool, optional): Whether to skip conversations with a project tag.     limit (int, optional): Number of conversations to return. Defaults to 100.     skip (int, optional): Number of conversations to skip. Defaults to 0.  Returns:     list[ConversationInfo]: List of conversation summaries for user.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tags"> (optional)</param>
        /// <param name="skipProjectTag"> (optional)</param>
        /// <param name="limit"> (optional, default to 100)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <returns>ApiResponse of List&lt;ConversationInfo&gt;</returns>
        public Unity.Behavior.WebApi.Client.ApiResponse<List<ConversationInfo>> GetConversationsV1MuseConversationGetWithHttpInfo(string tags = default(string), bool? skipProjectTag = default(bool?), int? limit = default(int?), int? skip = default(int?))
        {
            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            if (tags != null)
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "tags", tags));
            }
            if (skipProjectTag != null)
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "skip_project_tag", skipProjectTag));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (skip != null)
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "skip", skip));
            }

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<List<ConversationInfo>>("/v1/muse/conversation", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetConversationsV1MuseConversationGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get Conversations Get conversation summaries for user conversations.  Args:     request (Request): FastAPI request object.     user_info (UserInfo): User information extracted from bearer token.     tags (Optional[str], optional): Project ID to filter conversations by. Defaults to None.     skip_project_tag (bool, optional): Whether to skip conversations with a project tag.     limit (int, optional): Number of conversations to return. Defaults to 100.     skip (int, optional): Number of conversations to skip. Defaults to 0.  Returns:     list[ConversationInfo]: List of conversation summaries for user.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tags"> (optional)</param>
        /// <param name="skipProjectTag"> (optional)</param>
        /// <param name="limit"> (optional, default to 100)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of List&lt;ConversationInfo&gt;</returns>
        public async System.Threading.Tasks.Task<List<ConversationInfo>> GetConversationsV1MuseConversationGetAsync(string tags = default(string), bool? skipProjectTag = default(bool?), int? limit = default(int?), int? skip = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = GetConversationsV1MuseConversationGetWithHttpInfoAsync(tags, skipProjectTag, limit, skip, cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<List<ConversationInfo>> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<List<ConversationInfo>> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get Conversations Get conversation summaries for user conversations.  Args:     request (Request): FastAPI request object.     user_info (UserInfo): User information extracted from bearer token.     tags (Optional[str], optional): Project ID to filter conversations by. Defaults to None.     skip_project_tag (bool, optional): Whether to skip conversations with a project tag.     limit (int, optional): Number of conversations to return. Defaults to 100.     skip (int, optional): Number of conversations to skip. Defaults to 0.  Returns:     list[ConversationInfo]: List of conversation summaries for user.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="tags"> (optional)</param>
        /// <param name="skipProjectTag"> (optional)</param>
        /// <param name="limit"> (optional, default to 100)</param>
        /// <param name="skip"> (optional, default to 0)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (List&lt;ConversationInfo&gt;)</returns>
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<List<ConversationInfo>>> GetConversationsV1MuseConversationGetWithHttpInfoAsync(string tags = default(string), bool? skipProjectTag = default(bool?), int? limit = default(int?), int? skip = default(int?), System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            if (tags != null)
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "tags", tags));
            }
            if (skipProjectTag != null)
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "skip_project_tag", skipProjectTag));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (skip != null)
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "skip", skip));
            }

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request

            var task = this.AsynchronousClient.GetAsync<List<ConversationInfo>>("/v1/muse/conversation", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetConversationsV1MuseConversationGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get Opt Get the current opt status of the requesting user.  Args:     request (Request): _description_     user_info (UserInfo, optional): _description_. Defaults to Depends(extract_user_info).  Returns:     dict[OptType, OptDecision]: Opt status of the user.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <returns>Dictionary&lt;string, OptDecision&gt;</returns>
        [Obsolete]
        public Dictionary<string, OptDecision> GetOptMuseOptGet()
        {
            Unity.Behavior.WebApi.Client.ApiResponse<Dictionary<string, OptDecision>> localVarResponse = GetOptMuseOptGetWithHttpInfo();
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get Opt Get the current opt status of the requesting user.  Args:     request (Request): _description_     user_info (UserInfo, optional): _description_. Defaults to Depends(extract_user_info).  Returns:     dict[OptType, OptDecision]: Opt status of the user.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <returns>ApiResponse of Dictionary&lt;string, OptDecision&gt;</returns>
        [Obsolete]
        public Unity.Behavior.WebApi.Client.ApiResponse<Dictionary<string, OptDecision>> GetOptMuseOptGetWithHttpInfo()
        {
            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);


            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<Dictionary<string, OptDecision>>("/muse/opt", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetOptMuseOptGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get Opt Get the current opt status of the requesting user.  Args:     request (Request): _description_     user_info (UserInfo, optional): _description_. Defaults to Depends(extract_user_info).  Returns:     dict[OptType, OptDecision]: Opt status of the user.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Dictionary&lt;string, OptDecision&gt;</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<Dictionary<string, OptDecision>> GetOptMuseOptGetAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = GetOptMuseOptGetWithHttpInfoAsync(cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<Dictionary<string, OptDecision>> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<Dictionary<string, OptDecision>> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get Opt Get the current opt status of the requesting user.  Args:     request (Request): _description_     user_info (UserInfo, optional): _description_. Defaults to Depends(extract_user_info).  Returns:     dict[OptType, OptDecision]: Opt status of the user.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Dictionary&lt;string, OptDecision&gt;)</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<Dictionary<string, OptDecision>>> GetOptMuseOptGetWithHttpInfoAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);


            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request

            var task = this.AsynchronousClient.GetAsync<Dictionary<string, OptDecision>>("/muse/opt", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetOptMuseOptGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get Opt Get the current opt status of the requesting user.  Args:     request (Request): _description_     user_info (UserInfo, optional): _description_. Defaults to Depends(extract_user_info).  Returns:     dict[OptType, OptDecision]: Opt status of the user.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <returns>Dictionary&lt;string, OptDecision&gt;</returns>
        public Dictionary<string, OptDecision> GetOptV1MuseOptGet()
        {
            Unity.Behavior.WebApi.Client.ApiResponse<Dictionary<string, OptDecision>> localVarResponse = GetOptV1MuseOptGetWithHttpInfo();
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get Opt Get the current opt status of the requesting user.  Args:     request (Request): _description_     user_info (UserInfo, optional): _description_. Defaults to Depends(extract_user_info).  Returns:     dict[OptType, OptDecision]: Opt status of the user.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <returns>ApiResponse of Dictionary&lt;string, OptDecision&gt;</returns>
        public Unity.Behavior.WebApi.Client.ApiResponse<Dictionary<string, OptDecision>> GetOptV1MuseOptGetWithHttpInfo()
        {
            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);


            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<Dictionary<string, OptDecision>>("/v1/muse/opt", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetOptV1MuseOptGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get Opt Get the current opt status of the requesting user.  Args:     request (Request): _description_     user_info (UserInfo, optional): _description_. Defaults to Depends(extract_user_info).  Returns:     dict[OptType, OptDecision]: Opt status of the user.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Dictionary&lt;string, OptDecision&gt;</returns>
        public async System.Threading.Tasks.Task<Dictionary<string, OptDecision>> GetOptV1MuseOptGetAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = GetOptV1MuseOptGetWithHttpInfoAsync(cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<Dictionary<string, OptDecision>> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<Dictionary<string, OptDecision>> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get Opt Get the current opt status of the requesting user.  Args:     request (Request): _description_     user_info (UserInfo, optional): _description_. Defaults to Depends(extract_user_info).  Returns:     dict[OptType, OptDecision]: Opt status of the user.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Dictionary&lt;string, OptDecision&gt;)</returns>
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<Dictionary<string, OptDecision>>> GetOptV1MuseOptGetWithHttpInfoAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);


            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request

            var task = this.AsynchronousClient.GetAsync<Dictionary<string, OptDecision>>("/v1/muse/opt", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetOptV1MuseOptGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get Topic Get topic title for conversation.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     organization_id (str): Organization ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     str | JSONResponse:         Plain-text topic if conversation exists, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="organizationId"></param>
        /// <returns>string</returns>
        [Obsolete]
        public string GetTopicMuseTopicConversationIdGet(string conversationId, string organizationId)
        {
            Unity.Behavior.WebApi.Client.ApiResponse<string> localVarResponse = GetTopicMuseTopicConversationIdGetWithHttpInfo(conversationId, organizationId);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get Topic Get topic title for conversation.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     organization_id (str): Organization ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     str | JSONResponse:         Plain-text topic if conversation exists, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="organizationId"></param>
        /// <returns>ApiResponse of string</returns>
        [Obsolete]
        public Unity.Behavior.WebApi.Client.ApiResponse<string> GetTopicMuseTopicConversationIdGetWithHttpInfo(string conversationId, string organizationId)
        {
            // verify the required parameter 'conversationId' is set
            if (conversationId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'conversationId' when calling DefaultApi->GetTopicMuseTopicConversationIdGet");

            // verify the required parameter 'organizationId' is set
            if (organizationId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'organizationId' when calling DefaultApi->GetTopicMuseTopicConversationIdGet");

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("conversation_id", Unity.Behavior.WebApi.Client.ClientUtils.ParameterToString(conversationId)); // path parameter
            localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "organization_id", organizationId));

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<string>("/muse/topic/{conversation_id}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetTopicMuseTopicConversationIdGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get Topic Get topic title for conversation.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     organization_id (str): Organization ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     str | JSONResponse:         Plain-text topic if conversation exists, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="organizationId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of string</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<string> GetTopicMuseTopicConversationIdGetAsync(string conversationId, string organizationId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = GetTopicMuseTopicConversationIdGetWithHttpInfoAsync(conversationId, organizationId, cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<string> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<string> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get Topic Get topic title for conversation.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     organization_id (str): Organization ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     str | JSONResponse:         Plain-text topic if conversation exists, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="organizationId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (string)</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<string>> GetTopicMuseTopicConversationIdGetWithHttpInfoAsync(string conversationId, string organizationId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'conversationId' is set
            if (conversationId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'conversationId' when calling DefaultApi->GetTopicMuseTopicConversationIdGet");

            // verify the required parameter 'organizationId' is set
            if (organizationId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'organizationId' when calling DefaultApi->GetTopicMuseTopicConversationIdGet");


            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("conversation_id", Unity.Behavior.WebApi.Client.ClientUtils.ParameterToString(conversationId)); // path parameter
            localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "organization_id", organizationId));

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request

            var task = this.AsynchronousClient.GetAsync<string>("/muse/topic/{conversation_id}", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetTopicMuseTopicConversationIdGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get Topic Get topic title for conversation.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     organization_id (str): Organization ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     str | JSONResponse:         Plain-text topic if conversation exists, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="organizationId"></param>
        /// <returns>string</returns>
        public string GetTopicV1MuseTopicConversationIdGet(string conversationId, string organizationId)
        {
            Unity.Behavior.WebApi.Client.ApiResponse<string> localVarResponse = GetTopicV1MuseTopicConversationIdGetWithHttpInfo(conversationId, organizationId);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get Topic Get topic title for conversation.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     organization_id (str): Organization ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     str | JSONResponse:         Plain-text topic if conversation exists, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="organizationId"></param>
        /// <returns>ApiResponse of string</returns>
        public Unity.Behavior.WebApi.Client.ApiResponse<string> GetTopicV1MuseTopicConversationIdGetWithHttpInfo(string conversationId, string organizationId)
        {
            // verify the required parameter 'conversationId' is set
            if (conversationId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'conversationId' when calling DefaultApi->GetTopicV1MuseTopicConversationIdGet");

            // verify the required parameter 'organizationId' is set
            if (organizationId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'organizationId' when calling DefaultApi->GetTopicV1MuseTopicConversationIdGet");

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("conversation_id", Unity.Behavior.WebApi.Client.ClientUtils.ParameterToString(conversationId)); // path parameter
            localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "organization_id", organizationId));

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<string>("/v1/muse/topic/{conversation_id}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetTopicV1MuseTopicConversationIdGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get Topic Get topic title for conversation.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     organization_id (str): Organization ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     str | JSONResponse:         Plain-text topic if conversation exists, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="organizationId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of string</returns>
        public async System.Threading.Tasks.Task<string> GetTopicV1MuseTopicConversationIdGetAsync(string conversationId, string organizationId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = GetTopicV1MuseTopicConversationIdGetWithHttpInfoAsync(conversationId, organizationId, cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<string> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<string> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get Topic Get topic title for conversation.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     organization_id (str): Organization ID.     user_info (UserInfo): User information extracted from bearer token.  Returns:     str | JSONResponse:         Plain-text topic if conversation exists, otherwise JSONResponse with error.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="organizationId"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (string)</returns>
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<string>> GetTopicV1MuseTopicConversationIdGetWithHttpInfoAsync(string conversationId, string organizationId, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'conversationId' is set
            if (conversationId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'conversationId' when calling DefaultApi->GetTopicV1MuseTopicConversationIdGet");

            // verify the required parameter 'organizationId' is set
            if (organizationId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'organizationId' when calling DefaultApi->GetTopicV1MuseTopicConversationIdGet");


            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("conversation_id", Unity.Behavior.WebApi.Client.ClientUtils.ParameterToString(conversationId)); // path parameter
            localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "organization_id", organizationId));

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request

            var task = this.AsynchronousClient.GetAsync<string>("/v1/muse/topic/{conversation_id}", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("GetTopicV1MuseTopicConversationIdGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Health Head
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <returns>Object</returns>
        public Object HealthHeadHealthHead()
        {
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = HealthHeadHealthHeadWithHttpInfo();
            return localVarResponse.Data;
        }

        /// <summary>
        /// Health Head
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <returns>ApiResponse of Object</returns>
        public Unity.Behavior.WebApi.Client.ApiResponse<Object> HealthHeadHealthHeadWithHttpInfo()
        {
            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);



            // make the HTTP request
            var localVarResponse = this.Client.Head<Object>("/health", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("HealthHeadHealthHead", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Health Head
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        public async System.Threading.Tasks.Task<Object> HealthHeadHealthHeadAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = HealthHeadHealthHeadWithHttpInfoAsync(cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Health Head
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<Object>> HealthHeadHealthHeadWithHttpInfoAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);



            // make the HTTP request

            var task = this.AsynchronousClient.HeadAsync<Object>("/health", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("HealthHeadHealthHead", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Health
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <returns>Object</returns>
        public Object HealthHealthGet()
        {
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = HealthHealthGetWithHttpInfo();
            return localVarResponse.Data;
        }

        /// <summary>
        /// Health
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <returns>ApiResponse of Object</returns>
        public Unity.Behavior.WebApi.Client.ApiResponse<Object> HealthHealthGetWithHttpInfo()
        {
            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);



            // make the HTTP request
            var localVarResponse = this.Client.Get<Object>("/health", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("HealthHealthGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Health
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        public async System.Threading.Tasks.Task<Object> HealthHealthGetAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = HealthHealthGetWithHttpInfoAsync(cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Health
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<Object>> HealthHealthGetWithHttpInfoAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);



            // make the HTTP request

            var task = this.AsynchronousClient.GetAsync<Object>("/health", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("HealthHealthGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Healthz
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <returns>Object</returns>
        public Object HealthzHealthzGet()
        {
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = HealthzHealthzGetWithHttpInfo();
            return localVarResponse.Data;
        }

        /// <summary>
        /// Healthz
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <returns>ApiResponse of Object</returns>
        public Unity.Behavior.WebApi.Client.ApiResponse<Object> HealthzHealthzGetWithHttpInfo()
        {
            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);



            // make the HTTP request
            var localVarResponse = this.Client.Get<Object>("/healthz", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("HealthzHealthzGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Healthz
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        public async System.Threading.Tasks.Task<Object> HealthzHealthzGetAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = HealthzHealthzGetWithHttpInfoAsync(cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Healthz
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<Object>> HealthzHealthzGetWithHttpInfoAsync(System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);



            // make the HTTP request

            var task = this.AsynchronousClient.GetAsync<Object>("/healthz", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("HealthzHealthzGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Opt Opt in or out of model training.  Notes:     This is ideally a temporary solution. :)  Args:     request (Request): _description_     body (OptRequest): _description_     user_info (UserInfo, optional): _description_. Defaults to Depends(extract_user_info).
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="optRequest"></param>
        /// <returns>Object</returns>
        [Obsolete]
        public Object OptMuseOptPost(OptRequest optRequest)
        {
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = OptMuseOptPostWithHttpInfo(optRequest);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Opt Opt in or out of model training.  Notes:     This is ideally a temporary solution. :)  Args:     request (Request): _description_     body (OptRequest): _description_     user_info (UserInfo, optional): _description_. Defaults to Depends(extract_user_info).
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="optRequest"></param>
        /// <returns>ApiResponse of Object</returns>
        [Obsolete]
        public Unity.Behavior.WebApi.Client.ApiResponse<Object> OptMuseOptPostWithHttpInfo(OptRequest optRequest)
        {
            // verify the required parameter 'optRequest' is set
            if (optRequest == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'optRequest' when calling DefaultApi->OptMuseOptPost");

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = optRequest;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<Object>("/muse/opt", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("OptMuseOptPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Opt Opt in or out of model training.  Notes:     This is ideally a temporary solution. :)  Args:     request (Request): _description_     body (OptRequest): _description_     user_info (UserInfo, optional): _description_. Defaults to Depends(extract_user_info).
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="optRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<Object> OptMuseOptPostAsync(OptRequest optRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = OptMuseOptPostWithHttpInfoAsync(optRequest, cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Opt Opt in or out of model training.  Notes:     This is ideally a temporary solution. :)  Args:     request (Request): _description_     body (OptRequest): _description_     user_info (UserInfo, optional): _description_. Defaults to Depends(extract_user_info).
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="optRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<Object>> OptMuseOptPostWithHttpInfoAsync(OptRequest optRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'optRequest' is set
            if (optRequest == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'optRequest' when calling DefaultApi->OptMuseOptPost");


            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = optRequest;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request

            var task = this.AsynchronousClient.PostAsync<Object>("/muse/opt", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("OptMuseOptPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Opt Opt in or out of model training.  Notes:     This is ideally a temporary solution. :)  Args:     request (Request): _description_     body (OptRequest): _description_     user_info (UserInfo, optional): _description_. Defaults to Depends(extract_user_info).
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="optRequest"></param>
        /// <returns>Object</returns>
        public Object OptV1MuseOptPost(OptRequest optRequest)
        {
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = OptV1MuseOptPostWithHttpInfo(optRequest);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Opt Opt in or out of model training.  Notes:     This is ideally a temporary solution. :)  Args:     request (Request): _description_     body (OptRequest): _description_     user_info (UserInfo, optional): _description_. Defaults to Depends(extract_user_info).
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="optRequest"></param>
        /// <returns>ApiResponse of Object</returns>
        public Unity.Behavior.WebApi.Client.ApiResponse<Object> OptV1MuseOptPostWithHttpInfo(OptRequest optRequest)
        {
            // verify the required parameter 'optRequest' is set
            if (optRequest == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'optRequest' when calling DefaultApi->OptV1MuseOptPost");

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = optRequest;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<Object>("/v1/muse/opt", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("OptV1MuseOptPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Opt Opt in or out of model training.  Notes:     This is ideally a temporary solution. :)  Args:     request (Request): _description_     body (OptRequest): _description_     user_info (UserInfo, optional): _description_. Defaults to Depends(extract_user_info).
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="optRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        public async System.Threading.Tasks.Task<Object> OptV1MuseOptPostAsync(OptRequest optRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = OptV1MuseOptPostWithHttpInfoAsync(optRequest, cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<Object> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Opt Opt in or out of model training.  Notes:     This is ideally a temporary solution. :)  Args:     request (Request): _description_     body (OptRequest): _description_     user_info (UserInfo, optional): _description_. Defaults to Depends(extract_user_info).
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="optRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<Object>> OptV1MuseOptPostWithHttpInfoAsync(OptRequest optRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'optRequest' is set
            if (optRequest == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'optRequest' when calling DefaultApi->OptV1MuseOptPost");


            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = optRequest;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request

            var task = this.AsynchronousClient.PostAsync<Object>("/v1/muse/opt", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("OptV1MuseOptPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Patch Conversation Fragment Preference Update conversation fragment by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     fragment_id (str): Conversation fragment ID.     body (ConversationPatchRequest): Patch request for changing conversation fragment.     user_info (UserInfo): User information extracted from bearer token.  Returns:     None | JSONResponse: None if successful, otherwise ErrorResponse.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="fragmentId"></param>
        /// <param name="conversationFragmentPatch"></param>
        /// <returns>ErrorResponse</returns>
        [Obsolete]
        public ErrorResponse PatchConversationFragmentPreferenceMuseConversationConversationIdFragmentFragmentIdPatch(string conversationId, string fragmentId, ConversationFragmentPatch conversationFragmentPatch)
        {
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = PatchConversationFragmentPreferenceMuseConversationConversationIdFragmentFragmentIdPatchWithHttpInfo(conversationId, fragmentId, conversationFragmentPatch);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Patch Conversation Fragment Preference Update conversation fragment by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     fragment_id (str): Conversation fragment ID.     body (ConversationPatchRequest): Patch request for changing conversation fragment.     user_info (UserInfo): User information extracted from bearer token.  Returns:     None | JSONResponse: None if successful, otherwise ErrorResponse.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="fragmentId"></param>
        /// <param name="conversationFragmentPatch"></param>
        /// <returns>ApiResponse of ErrorResponse</returns>
        [Obsolete]
        public Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> PatchConversationFragmentPreferenceMuseConversationConversationIdFragmentFragmentIdPatchWithHttpInfo(string conversationId, string fragmentId, ConversationFragmentPatch conversationFragmentPatch)
        {
            // verify the required parameter 'conversationId' is set
            if (conversationId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'conversationId' when calling DefaultApi->PatchConversationFragmentPreferenceMuseConversationConversationIdFragmentFragmentIdPatch");

            // verify the required parameter 'fragmentId' is set
            if (fragmentId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'fragmentId' when calling DefaultApi->PatchConversationFragmentPreferenceMuseConversationConversationIdFragmentFragmentIdPatch");

            // verify the required parameter 'conversationFragmentPatch' is set
            if (conversationFragmentPatch == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'conversationFragmentPatch' when calling DefaultApi->PatchConversationFragmentPreferenceMuseConversationConversationIdFragmentFragmentIdPatch");

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("conversation_id", Unity.Behavior.WebApi.Client.ClientUtils.ParameterToString(conversationId)); // path parameter
            localVarRequestOptions.PathParameters.Add("fragment_id", Unity.Behavior.WebApi.Client.ClientUtils.ParameterToString(fragmentId)); // path parameter
            localVarRequestOptions.Data = conversationFragmentPatch;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Patch<ErrorResponse>("/muse/conversation/{conversation_id}/fragment/{fragment_id}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("PatchConversationFragmentPreferenceMuseConversationConversationIdFragmentFragmentIdPatch", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Patch Conversation Fragment Preference Update conversation fragment by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     fragment_id (str): Conversation fragment ID.     body (ConversationPatchRequest): Patch request for changing conversation fragment.     user_info (UserInfo): User information extracted from bearer token.  Returns:     None | JSONResponse: None if successful, otherwise ErrorResponse.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="fragmentId"></param>
        /// <param name="conversationFragmentPatch"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ErrorResponse</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<ErrorResponse> PatchConversationFragmentPreferenceMuseConversationConversationIdFragmentFragmentIdPatchAsync(string conversationId, string fragmentId, ConversationFragmentPatch conversationFragmentPatch, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = PatchConversationFragmentPreferenceMuseConversationConversationIdFragmentFragmentIdPatchWithHttpInfoAsync(conversationId, fragmentId, conversationFragmentPatch, cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Patch Conversation Fragment Preference Update conversation fragment by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     fragment_id (str): Conversation fragment ID.     body (ConversationPatchRequest): Patch request for changing conversation fragment.     user_info (UserInfo): User information extracted from bearer token.  Returns:     None | JSONResponse: None if successful, otherwise ErrorResponse.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="fragmentId"></param>
        /// <param name="conversationFragmentPatch"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ErrorResponse)</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse>> PatchConversationFragmentPreferenceMuseConversationConversationIdFragmentFragmentIdPatchWithHttpInfoAsync(string conversationId, string fragmentId, ConversationFragmentPatch conversationFragmentPatch, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'conversationId' is set
            if (conversationId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'conversationId' when calling DefaultApi->PatchConversationFragmentPreferenceMuseConversationConversationIdFragmentFragmentIdPatch");

            // verify the required parameter 'fragmentId' is set
            if (fragmentId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'fragmentId' when calling DefaultApi->PatchConversationFragmentPreferenceMuseConversationConversationIdFragmentFragmentIdPatch");

            // verify the required parameter 'conversationFragmentPatch' is set
            if (conversationFragmentPatch == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'conversationFragmentPatch' when calling DefaultApi->PatchConversationFragmentPreferenceMuseConversationConversationIdFragmentFragmentIdPatch");


            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("conversation_id", Unity.Behavior.WebApi.Client.ClientUtils.ParameterToString(conversationId)); // path parameter
            localVarRequestOptions.PathParameters.Add("fragment_id", Unity.Behavior.WebApi.Client.ClientUtils.ParameterToString(fragmentId)); // path parameter
            localVarRequestOptions.Data = conversationFragmentPatch;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request

            var task = this.AsynchronousClient.PatchAsync<ErrorResponse>("/muse/conversation/{conversation_id}/fragment/{fragment_id}", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("PatchConversationFragmentPreferenceMuseConversationConversationIdFragmentFragmentIdPatch", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Patch Conversation Fragment Preference Update conversation fragment by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     fragment_id (str): Conversation fragment ID.     body (ConversationPatchRequest): Patch request for changing conversation fragment.     user_info (UserInfo): User information extracted from bearer token.  Returns:     None | JSONResponse: None if successful, otherwise ErrorResponse.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="fragmentId"></param>
        /// <param name="conversationFragmentPatch"></param>
        /// <returns>ErrorResponse</returns>
        public ErrorResponse PatchConversationFragmentPreferenceV1MuseConversationConversationIdFragmentFragmentIdPatch(string conversationId, string fragmentId, ConversationFragmentPatch conversationFragmentPatch)
        {
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = PatchConversationFragmentPreferenceV1MuseConversationConversationIdFragmentFragmentIdPatchWithHttpInfo(conversationId, fragmentId, conversationFragmentPatch);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Patch Conversation Fragment Preference Update conversation fragment by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     fragment_id (str): Conversation fragment ID.     body (ConversationPatchRequest): Patch request for changing conversation fragment.     user_info (UserInfo): User information extracted from bearer token.  Returns:     None | JSONResponse: None if successful, otherwise ErrorResponse.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="fragmentId"></param>
        /// <param name="conversationFragmentPatch"></param>
        /// <returns>ApiResponse of ErrorResponse</returns>
        public Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> PatchConversationFragmentPreferenceV1MuseConversationConversationIdFragmentFragmentIdPatchWithHttpInfo(string conversationId, string fragmentId, ConversationFragmentPatch conversationFragmentPatch)
        {
            // verify the required parameter 'conversationId' is set
            if (conversationId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'conversationId' when calling DefaultApi->PatchConversationFragmentPreferenceV1MuseConversationConversationIdFragmentFragmentIdPatch");

            // verify the required parameter 'fragmentId' is set
            if (fragmentId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'fragmentId' when calling DefaultApi->PatchConversationFragmentPreferenceV1MuseConversationConversationIdFragmentFragmentIdPatch");

            // verify the required parameter 'conversationFragmentPatch' is set
            if (conversationFragmentPatch == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'conversationFragmentPatch' when calling DefaultApi->PatchConversationFragmentPreferenceV1MuseConversationConversationIdFragmentFragmentIdPatch");

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("conversation_id", Unity.Behavior.WebApi.Client.ClientUtils.ParameterToString(conversationId)); // path parameter
            localVarRequestOptions.PathParameters.Add("fragment_id", Unity.Behavior.WebApi.Client.ClientUtils.ParameterToString(fragmentId)); // path parameter
            localVarRequestOptions.Data = conversationFragmentPatch;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Patch<ErrorResponse>("/v1/muse/conversation/{conversation_id}/fragment/{fragment_id}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("PatchConversationFragmentPreferenceV1MuseConversationConversationIdFragmentFragmentIdPatch", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Patch Conversation Fragment Preference Update conversation fragment by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     fragment_id (str): Conversation fragment ID.     body (ConversationPatchRequest): Patch request for changing conversation fragment.     user_info (UserInfo): User information extracted from bearer token.  Returns:     None | JSONResponse: None if successful, otherwise ErrorResponse.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="fragmentId"></param>
        /// <param name="conversationFragmentPatch"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ErrorResponse</returns>
        public async System.Threading.Tasks.Task<ErrorResponse> PatchConversationFragmentPreferenceV1MuseConversationConversationIdFragmentFragmentIdPatchAsync(string conversationId, string fragmentId, ConversationFragmentPatch conversationFragmentPatch, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = PatchConversationFragmentPreferenceV1MuseConversationConversationIdFragmentFragmentIdPatchWithHttpInfoAsync(conversationId, fragmentId, conversationFragmentPatch, cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Patch Conversation Fragment Preference Update conversation fragment by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     fragment_id (str): Conversation fragment ID.     body (ConversationPatchRequest): Patch request for changing conversation fragment.     user_info (UserInfo): User information extracted from bearer token.  Returns:     None | JSONResponse: None if successful, otherwise ErrorResponse.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="fragmentId"></param>
        /// <param name="conversationFragmentPatch"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ErrorResponse)</returns>
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse>> PatchConversationFragmentPreferenceV1MuseConversationConversationIdFragmentFragmentIdPatchWithHttpInfoAsync(string conversationId, string fragmentId, ConversationFragmentPatch conversationFragmentPatch, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'conversationId' is set
            if (conversationId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'conversationId' when calling DefaultApi->PatchConversationFragmentPreferenceV1MuseConversationConversationIdFragmentFragmentIdPatch");

            // verify the required parameter 'fragmentId' is set
            if (fragmentId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'fragmentId' when calling DefaultApi->PatchConversationFragmentPreferenceV1MuseConversationConversationIdFragmentFragmentIdPatch");

            // verify the required parameter 'conversationFragmentPatch' is set
            if (conversationFragmentPatch == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'conversationFragmentPatch' when calling DefaultApi->PatchConversationFragmentPreferenceV1MuseConversationConversationIdFragmentFragmentIdPatch");


            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("conversation_id", Unity.Behavior.WebApi.Client.ClientUtils.ParameterToString(conversationId)); // path parameter
            localVarRequestOptions.PathParameters.Add("fragment_id", Unity.Behavior.WebApi.Client.ClientUtils.ParameterToString(fragmentId)); // path parameter
            localVarRequestOptions.Data = conversationFragmentPatch;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request

            var task = this.AsynchronousClient.PatchAsync<ErrorResponse>("/v1/muse/conversation/{conversation_id}/fragment/{fragment_id}", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("PatchConversationFragmentPreferenceV1MuseConversationConversationIdFragmentFragmentIdPatch", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Patch Conversation Update conversation by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     body (ConversationPatchRequest): Patch request for changing conversation.     user_info (UserInfo): User information extracted from bearer token.  Returns:     None | JSONResponse: None if successful, otherwise ErrorResponse.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="conversationPatchRequest"></param>
        /// <returns>ErrorResponse</returns>
        [Obsolete]
        public ErrorResponse PatchConversationMuseConversationConversationIdPatch(string conversationId, ConversationPatchRequest conversationPatchRequest)
        {
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = PatchConversationMuseConversationConversationIdPatchWithHttpInfo(conversationId, conversationPatchRequest);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Patch Conversation Update conversation by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     body (ConversationPatchRequest): Patch request for changing conversation.     user_info (UserInfo): User information extracted from bearer token.  Returns:     None | JSONResponse: None if successful, otherwise ErrorResponse.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="conversationPatchRequest"></param>
        /// <returns>ApiResponse of ErrorResponse</returns>
        [Obsolete]
        public Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> PatchConversationMuseConversationConversationIdPatchWithHttpInfo(string conversationId, ConversationPatchRequest conversationPatchRequest)
        {
            // verify the required parameter 'conversationId' is set
            if (conversationId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'conversationId' when calling DefaultApi->PatchConversationMuseConversationConversationIdPatch");

            // verify the required parameter 'conversationPatchRequest' is set
            if (conversationPatchRequest == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'conversationPatchRequest' when calling DefaultApi->PatchConversationMuseConversationConversationIdPatch");

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("conversation_id", Unity.Behavior.WebApi.Client.ClientUtils.ParameterToString(conversationId)); // path parameter
            localVarRequestOptions.Data = conversationPatchRequest;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Patch<ErrorResponse>("/muse/conversation/{conversation_id}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("PatchConversationMuseConversationConversationIdPatch", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Patch Conversation Update conversation by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     body (ConversationPatchRequest): Patch request for changing conversation.     user_info (UserInfo): User information extracted from bearer token.  Returns:     None | JSONResponse: None if successful, otherwise ErrorResponse.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="conversationPatchRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ErrorResponse</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<ErrorResponse> PatchConversationMuseConversationConversationIdPatchAsync(string conversationId, ConversationPatchRequest conversationPatchRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = PatchConversationMuseConversationConversationIdPatchWithHttpInfoAsync(conversationId, conversationPatchRequest, cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Patch Conversation Update conversation by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     body (ConversationPatchRequest): Patch request for changing conversation.     user_info (UserInfo): User information extracted from bearer token.  Returns:     None | JSONResponse: None if successful, otherwise ErrorResponse.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="conversationPatchRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ErrorResponse)</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse>> PatchConversationMuseConversationConversationIdPatchWithHttpInfoAsync(string conversationId, ConversationPatchRequest conversationPatchRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'conversationId' is set
            if (conversationId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'conversationId' when calling DefaultApi->PatchConversationMuseConversationConversationIdPatch");

            // verify the required parameter 'conversationPatchRequest' is set
            if (conversationPatchRequest == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'conversationPatchRequest' when calling DefaultApi->PatchConversationMuseConversationConversationIdPatch");


            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("conversation_id", Unity.Behavior.WebApi.Client.ClientUtils.ParameterToString(conversationId)); // path parameter
            localVarRequestOptions.Data = conversationPatchRequest;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request

            var task = this.AsynchronousClient.PatchAsync<ErrorResponse>("/muse/conversation/{conversation_id}", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("PatchConversationMuseConversationConversationIdPatch", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Patch Conversation Update conversation by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     body (ConversationPatchRequest): Patch request for changing conversation.     user_info (UserInfo): User information extracted from bearer token.  Returns:     None | JSONResponse: None if successful, otherwise ErrorResponse.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="conversationPatchRequest"></param>
        /// <returns>ErrorResponse</returns>
        public ErrorResponse PatchConversationV1MuseConversationConversationIdPatch(string conversationId, ConversationPatchRequest conversationPatchRequest)
        {
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = PatchConversationV1MuseConversationConversationIdPatchWithHttpInfo(conversationId, conversationPatchRequest);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Patch Conversation Update conversation by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     body (ConversationPatchRequest): Patch request for changing conversation.     user_info (UserInfo): User information extracted from bearer token.  Returns:     None | JSONResponse: None if successful, otherwise ErrorResponse.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="conversationPatchRequest"></param>
        /// <returns>ApiResponse of ErrorResponse</returns>
        public Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> PatchConversationV1MuseConversationConversationIdPatchWithHttpInfo(string conversationId, ConversationPatchRequest conversationPatchRequest)
        {
            // verify the required parameter 'conversationId' is set
            if (conversationId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'conversationId' when calling DefaultApi->PatchConversationV1MuseConversationConversationIdPatch");

            // verify the required parameter 'conversationPatchRequest' is set
            if (conversationPatchRequest == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'conversationPatchRequest' when calling DefaultApi->PatchConversationV1MuseConversationConversationIdPatch");

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("conversation_id", Unity.Behavior.WebApi.Client.ClientUtils.ParameterToString(conversationId)); // path parameter
            localVarRequestOptions.Data = conversationPatchRequest;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Patch<ErrorResponse>("/v1/muse/conversation/{conversation_id}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("PatchConversationV1MuseConversationConversationIdPatch", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Patch Conversation Update conversation by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     body (ConversationPatchRequest): Patch request for changing conversation.     user_info (UserInfo): User information extracted from bearer token.  Returns:     None | JSONResponse: None if successful, otherwise ErrorResponse.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="conversationPatchRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ErrorResponse</returns>
        public async System.Threading.Tasks.Task<ErrorResponse> PatchConversationV1MuseConversationConversationIdPatchAsync(string conversationId, ConversationPatchRequest conversationPatchRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = PatchConversationV1MuseConversationConversationIdPatchWithHttpInfoAsync(conversationId, conversationPatchRequest, cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Patch Conversation Update conversation by ID.  Args:     request (Request): FastAPI request object.     conversation_id (str): Conversation ID.     body (ConversationPatchRequest): Patch request for changing conversation.     user_info (UserInfo): User information extracted from bearer token.  Returns:     None | JSONResponse: None if successful, otherwise ErrorResponse.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="conversationId"></param>
        /// <param name="conversationPatchRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ErrorResponse)</returns>
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<ErrorResponse>> PatchConversationV1MuseConversationConversationIdPatchWithHttpInfoAsync(string conversationId, ConversationPatchRequest conversationPatchRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'conversationId' is set
            if (conversationId == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'conversationId' when calling DefaultApi->PatchConversationV1MuseConversationConversationIdPatch");

            // verify the required parameter 'conversationPatchRequest' is set
            if (conversationPatchRequest == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'conversationPatchRequest' when calling DefaultApi->PatchConversationV1MuseConversationConversationIdPatch");


            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("conversation_id", Unity.Behavior.WebApi.Client.ClientUtils.ParameterToString(conversationId)); // path parameter
            localVarRequestOptions.Data = conversationPatchRequest;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request

            var task = this.AsynchronousClient.PatchAsync<ErrorResponse>("/v1/muse/conversation/{conversation_id}", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("PatchConversationV1MuseConversationConversationIdPatch", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Smart Context Handle smart context requests.  Args:     request (Request): FastAPI request object.     body (SmartContextRequest): Smart context request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     SmartContextResponse | JSONResponse:         Either smart context response or JSON error message.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="smartContextRequest"></param>
        /// <returns>SmartContextResponse</returns>
        [Obsolete]
        public SmartContextResponse SmartContextSmartContextPost(SmartContextRequest smartContextRequest)
        {
            Unity.Behavior.WebApi.Client.ApiResponse<SmartContextResponse> localVarResponse = SmartContextSmartContextPostWithHttpInfo(smartContextRequest);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Smart Context Handle smart context requests.  Args:     request (Request): FastAPI request object.     body (SmartContextRequest): Smart context request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     SmartContextResponse | JSONResponse:         Either smart context response or JSON error message.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="smartContextRequest"></param>
        /// <returns>ApiResponse of SmartContextResponse</returns>
        [Obsolete]
        public Unity.Behavior.WebApi.Client.ApiResponse<SmartContextResponse> SmartContextSmartContextPostWithHttpInfo(SmartContextRequest smartContextRequest)
        {
            // verify the required parameter 'smartContextRequest' is set
            if (smartContextRequest == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'smartContextRequest' when calling DefaultApi->SmartContextSmartContextPost");

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = smartContextRequest;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<SmartContextResponse>("/smart-context", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("SmartContextSmartContextPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Smart Context Handle smart context requests.  Args:     request (Request): FastAPI request object.     body (SmartContextRequest): Smart context request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     SmartContextResponse | JSONResponse:         Either smart context response or JSON error message.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="smartContextRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of SmartContextResponse</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<SmartContextResponse> SmartContextSmartContextPostAsync(SmartContextRequest smartContextRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = SmartContextSmartContextPostWithHttpInfoAsync(smartContextRequest, cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<SmartContextResponse> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<SmartContextResponse> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Smart Context Handle smart context requests.  Args:     request (Request): FastAPI request object.     body (SmartContextRequest): Smart context request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     SmartContextResponse | JSONResponse:         Either smart context response or JSON error message.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="smartContextRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (SmartContextResponse)</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<SmartContextResponse>> SmartContextSmartContextPostWithHttpInfoAsync(SmartContextRequest smartContextRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'smartContextRequest' is set
            if (smartContextRequest == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'smartContextRequest' when calling DefaultApi->SmartContextSmartContextPost");


            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = smartContextRequest;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request

            var task = this.AsynchronousClient.PostAsync<SmartContextResponse>("/smart-context", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("SmartContextSmartContextPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Smart Context Handle smart context requests.  Args:     request (Request): FastAPI request object.     body (SmartContextRequest): Smart context request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     SmartContextResponse | JSONResponse:         Either smart context response or JSON error message.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="smartContextRequest"></param>
        /// <returns>SmartContextResponse</returns>
        public SmartContextResponse SmartContextV1SmartContextPost(SmartContextRequest smartContextRequest)
        {
            Unity.Behavior.WebApi.Client.ApiResponse<SmartContextResponse> localVarResponse = SmartContextV1SmartContextPostWithHttpInfo(smartContextRequest);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Smart Context Handle smart context requests.  Args:     request (Request): FastAPI request object.     body (SmartContextRequest): Smart context request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     SmartContextResponse | JSONResponse:         Either smart context response or JSON error message.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="smartContextRequest"></param>
        /// <returns>ApiResponse of SmartContextResponse</returns>
        public Unity.Behavior.WebApi.Client.ApiResponse<SmartContextResponse> SmartContextV1SmartContextPostWithHttpInfo(SmartContextRequest smartContextRequest)
        {
            // verify the required parameter 'smartContextRequest' is set
            if (smartContextRequest == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'smartContextRequest' when calling DefaultApi->SmartContextV1SmartContextPost");

            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = smartContextRequest;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<SmartContextResponse>("/v1/smart-context", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("SmartContextV1SmartContextPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Smart Context Handle smart context requests.  Args:     request (Request): FastAPI request object.     body (SmartContextRequest): Smart context request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     SmartContextResponse | JSONResponse:         Either smart context response or JSON error message.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="smartContextRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of SmartContextResponse</returns>
        public async System.Threading.Tasks.Task<SmartContextResponse> SmartContextV1SmartContextPostAsync(SmartContextRequest smartContextRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            var task = SmartContextV1SmartContextPostWithHttpInfoAsync(smartContextRequest, cancellationToken);
#if UNITY_EDITOR || !UNITY_WEBGL
            Unity.Behavior.WebApi.Client.ApiResponse<SmartContextResponse> localVarResponse = await task.ConfigureAwait(false);
#else
            Unity.Behavior.WebApi.Client.ApiResponse<SmartContextResponse> localVarResponse = await task;
#endif
            return localVarResponse.Data;
        }

        /// <summary>
        /// Smart Context Handle smart context requests.  Args:     request (Request): FastAPI request object.     body (SmartContextRequest): Smart context request body.     user_info (UserInfo): User information extracted from bearer token.  Returns:     SmartContextResponse | JSONResponse:         Either smart context response or JSON error message.
        /// </summary>
        /// <exception cref="Unity.Behavior.WebApi.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="smartContextRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (SmartContextResponse)</returns>
        public async System.Threading.Tasks.Task<Unity.Behavior.WebApi.Client.ApiResponse<SmartContextResponse>> SmartContextV1SmartContextPostWithHttpInfoAsync(SmartContextRequest smartContextRequest, System.Threading.CancellationToken cancellationToken = default(System.Threading.CancellationToken))
        {
            // verify the required parameter 'smartContextRequest' is set
            if (smartContextRequest == null)
                throw new Unity.Behavior.WebApi.Client.ApiException(400, "Missing required parameter 'smartContextRequest' when calling DefaultApi->SmartContextV1SmartContextPost");


            Unity.Behavior.WebApi.Client.RequestOptions localVarRequestOptions = new Unity.Behavior.WebApi.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Unity.Behavior.WebApi.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = smartContextRequest;

            // authentication (APIKeyHeader) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.HeaderParameters.Add("access_token", this.Configuration.GetApiKeyWithPrefix("access_token"));
            }
            // authentication (APIKeyQuery) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("access_token")))
            {
                localVarRequestOptions.QueryParameters.Add(Unity.Behavior.WebApi.Client.ClientUtils.ParameterToMultiMap("", "access_token", this.Configuration.GetApiKeyWithPrefix("access_token")));
            }

            // make the HTTP request

            var task = this.AsynchronousClient.PostAsync<SmartContextResponse>("/v1/smart-context", localVarRequestOptions, this.Configuration, cancellationToken);

#if UNITY_EDITOR || !UNITY_WEBGL
            var localVarResponse = await task.ConfigureAwait(false);
#else
            var localVarResponse = await task;
#endif

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("SmartContextV1SmartContextPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

    }
}
