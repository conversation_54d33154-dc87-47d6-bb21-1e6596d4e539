using UnityEngine;

[CreateAssetMenu(fileName = "NewKaelen_Guardian_CompletePreset", menuName = "Character Customization/Kaelen_Guardian_Complete Preset")]
public class Kaelen_Guardian_CompletePreset : ScriptableObject
{
    [Header("Preset Information")]
    public string presetName = "New Preset";
    [TextArea(3, 5)]
    public string description = "Custom character preset";

    [Header("Customization Values")]
    [Range(0, 10)] public int armor_color = 0;
    [Range(0, 10)] public int cape_style = 0;
    [Range(0, 10)] public int spear_type = 0;
    [Range(0, 10)] public int helmet = 0;

    public void ApplyToCharacter(GameObject character)
    {
        var customization = character.GetComponent<Kaelen_Guardian_CompleteCustomization>();
        if (customization != null)
        {
            customization.ApplyCustomization("armor_color", armor_color);
            customization.ApplyCustomization("cape_style", cape_style);
            customization.ApplyCustomization("spear_type", spear_type);
            customization.ApplyCustomization("helmet", helmet);
        }
        else
        {
            Debug.LogWarning($"No customization component found on {character.name}");
        }
    }
}
