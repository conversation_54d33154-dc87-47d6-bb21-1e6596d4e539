using UnityEngine;
using UnityEngine.InputSystem;

public class AdvancedInputController : MonoBehaviour
{
    [Header("Input Settings")]
    public float mouseSensitivity = 2f;
    public float moveSpeed = 5f;
    public float jumpForce = 10f;
    
    private Vector2 moveInput;
    private Vector2 lookInput;
    private bool jumpInput;
    private Rigidbody rb;
    private Camera playerCamera;
    private float xRotation = 0f;
    
    private void Start()
    {
        rb = GetComponent<Rigidbody>();
        playerCamera = Camera.main;
        if (playerCamera == null)
            playerCamera = FindFirstObjectByType<Camera>();
            
        Cursor.lockState = CursorLockMode.Locked;
    }
    
    public void OnMove(InputAction.CallbackContext context)
    {
        moveInput = context.ReadValue<Vector2>();
    }
    
    public void OnLook(InputAction.CallbackContext context)
    {
        lookInput = context.ReadValue<Vector2>();
    }
    
    public void OnJump(InputAction.CallbackContext context)
    {
        jumpInput = context.performed;
    }
    
    private void Update()
    {
        HandleLook();
        HandleMovement();
        HandleJump();
    }
    
    private void HandleLook()
    {
        if (playerCamera != null)
        {
            float mouseX = lookInput.x * mouseSensitivity;
            float mouseY = lookInput.y * mouseSensitivity;
            
            xRotation -= mouseY;
            xRotation = Mathf.Clamp(xRotation, -90f, 90f);
            
            playerCamera.transform.localRotation = Quaternion.Euler(xRotation, 0f, 0f);
            transform.Rotate(Vector3.up * mouseX);
        }
    }
    
    private void HandleMovement()
    {
        Vector3 direction = transform.right * moveInput.x + transform.forward * moveInput.y;
        Vector3 moveVelocity = direction * moveSpeed;
        
        if (rb != null)
        {
            rb.linearVelocity = new Vector3(moveVelocity.x, rb.linearVelocity.y, moveVelocity.z);
        }
        else
        {
            transform.Translate(direction * moveSpeed * Time.deltaTime, Space.World);
        }
    }
    
    private void HandleJump()
    {
        if (jumpInput && rb != null && IsGrounded())
        {
            rb.AddForce(Vector3.up * jumpForce, ForceMode.Impulse);
        }
        jumpInput = false;
    }
    
    private bool IsGrounded()
    {
        return Physics.Raycast(transform.position, Vector3.down, 1.1f);
    }
}