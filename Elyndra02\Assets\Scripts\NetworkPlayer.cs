using UnityEngine;

public class NetworkPlayer : MonoBehaviour
{
    public string playerId;
    public bool isLocalPlayer = false;
    public float moveSpeed = 5f;
    
    private Vector3 networkPosition;
    private Quaternion networkRotation;
    
    void Start()
    {
        playerId = System.Guid.NewGuid().ToString();
        networkPosition = transform.position;
        networkRotation = transform.rotation;
        
        // Register this player with the NetworkManager
        if (NetworkManager.Instance != null)
        {
            NetworkManager.Instance.RegisterPlayer(this);
        }
        
        Debug.Log($"Network Player created with ID: {playerId}");
    }
    
    void Update()
    {
        if (isLocalPlayer)
        {
            HandleInput();
        }
        else
        {
            InterpolatePosition();
        }
    }
    
    void HandleInput()
    {
        float horizontal = Input.GetAxis("Horizontal");
        float vertical = Input.GetAxis("Vertical");
        
        Vector3 movement = new Vector3(horizontal, 0, vertical) * moveSpeed * Time.deltaTime;
        transform.Translate(movement);
        
        if (movement.magnitude > 0.1f)
        {
            SendPositionUpdate();
        }
    }
    
    void InterpolatePosition()
    {
        transform.position = Vector3.Lerp(transform.position, networkPosition, Time.deltaTime * 10f);
        transform.rotation = Quaternion.Lerp(transform.rotation, networkRotation, Time.deltaTime * 10f);
    }
    
    public void UpdateNetworkPosition(Vector3 position, Quaternion rotation)
    {
        networkPosition = position;
        networkRotation = rotation;
    }
    
    void SendPositionUpdate()
    {
        NetworkManager networkManager = NetworkManager.Instance;
        if (networkManager != null)
        {
            string message = $"MOVE:{playerId},{transform.position.x},{transform.position.y},{transform.position.z}";
            networkManager.SendMessage(message);
            Debug.Log($"Sending position update: {message}");
        }
        else
        {
            Debug.LogWarning("NetworkManager instance not found. Cannot send position update.");
        }
    }
    
    void OnDestroy()
    {
        // Unregister this player when the object is destroyed
        if (NetworkManager.Instance != null && !string.IsNullOrEmpty(playerId))
        {
            NetworkManager.Instance.UnregisterPlayer(playerId);
        }
    }
}