using UnityEngine;
using UnityEngine.Rendering;
using UnityEditor;

public class LightingSettingsTest
{
    [MenuItem("Debug/Test LightingSettings Creation")]
    public static void TestLightingSettingsCreation()
    {
        Debug.Log("[TEST] Starting LightingSettings test...");
        
        try
        {
            // Check current state
            var current = Lightmapping.lightingSettings;
            Debug.Log($"[TEST] Current lightingSettings: {(current == null ? "NULL" : "EXISTS")}");
            
            // Create new LightingSettings
            var newSettings = new LightingSettings();
            Debug.Log("[TEST] Created new LightingSettings object");
            
            // Save as asset
            string assetPath = "Assets/TestLightmaps/TestLightingSettings.lighting";
            AssetDatabase.CreateAsset(newSettings, assetPath);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            Debug.Log($"[TEST] Saved LightingSettings asset to {assetPath}");
            
            // Load and assign
            var loadedSettings = AssetDatabase.LoadAssetAtPath<LightingSettings>(assetPath);
            Debug.Log($"[TEST] Loaded asset: {(loadedSettings == null ? "NULL" : "EXISTS")}");
            
            Lightmapping.lightingSettings = loadedSettings;
            Debug.Log("[TEST] Assigned to Lightmapping.lightingSettings");
            
            // Verify assignment
            var verified = Lightmapping.lightingSettings;
            Debug.Log($"[TEST] Verification: {(verified == null ? "NULL" : "EXISTS")}");
            
            Debug.Log("[TEST] LightingSettings test completed successfully!");
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"[TEST] Exception: {ex.Message}");
            Debug.LogError($"[TEST] Stack trace: {ex.StackTrace}");
        }
    }
}