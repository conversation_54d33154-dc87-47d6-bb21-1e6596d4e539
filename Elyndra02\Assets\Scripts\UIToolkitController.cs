using UnityEngine;
using UnityEngine.UIElements;
using System.Collections.Generic;

public class UIToolkitController : MonoBehaviour
{
    [Header("UI Document Settings")]
    public UIDocument uiDocument;
    public string mainContainerName = "main-container";
    
    [Header("UI Elements")]
    public List<string> buttonNames = new List<string>();
    public List<string> labelNames = new List<string>();
    public List<string> textFieldNames = new List<string>();
    
    private VisualElement rootElement;
    private Dictionary<string, Button> buttons = new Dictionary<string, Button>();
    private Dictionary<string, Label> labels = new Dictionary<string, Label>();
    private Dictionary<string, TextField> textFields = new Dictionary<string, TextField>();
    
    void Start()
    {
        InitializeUI();
    }
    
    void InitializeUI()
    {
        if (uiDocument == null)
        {
            uiDocument = GetComponent<UIDocument>();
            if (uiDocument == null)
            {
                Debug.LogError("UIDocument component not found!");
                return;
            }
        }
        
        rootElement = uiDocument.rootVisualElement;
        
        // Find main container
        var mainContainer = rootElement.Q<VisualElement>(mainContainerName);
        if (mainContainer == null)
        {
            mainContainer = rootElement;
        }
        
        // Initialize buttons
        foreach (string buttonName in buttonNames)
        {
            var button = rootElement.Q<Button>(buttonName);
            if (button != null)
            {
                buttons[buttonName] = button;
                button.clicked += () => OnButtonClicked(buttonName);
            }
        }
        
        // Initialize labels
        foreach (string labelName in labelNames)
        {
            var label = rootElement.Q<Label>(labelName);
            if (label != null)
            {
                labels[labelName] = label;
            }
        }
        
        // Initialize text fields
        foreach (string textFieldName in textFieldNames)
        {
            var textField = rootElement.Q<TextField>(textFieldName);
            if (textField != null)
            {
                textFields[textFieldName] = textField;
                textField.RegisterValueChangedCallback(evt => OnTextFieldChanged(textFieldName, evt.newValue));
            }
        }
        
        Debug.Log("UI Toolkit Controller initialized successfully!");
    }
    
    void OnButtonClicked(string buttonName)
    {
        Debug.Log($"Button clicked: {buttonName}");
        
        // Example button actions
        switch (buttonName)
        {
            case "start-button":
                SetLabelText("status-label", "Game Started!");
                break;
            case "stop-button":
                SetLabelText("status-label", "Game Stopped!");
                break;
            case "reset-button":
                ResetUI();
                break;
        }
    }
    
    void OnTextFieldChanged(string fieldName, string newValue)
    {
        Debug.Log($"Text field {fieldName} changed to: {newValue}");
    }
    
    public void SetLabelText(string labelName, string text)
    {
        if (labels.ContainsKey(labelName))
        {
            labels[labelName].text = text;
        }
    }
    
    public string GetTextFieldValue(string fieldName)
    {
        if (textFields.ContainsKey(fieldName))
        {
            return textFields[fieldName].value;
        }
        return string.Empty;
    }
    
    public void SetTextFieldValue(string fieldName, string value)
    {
        if (textFields.ContainsKey(fieldName))
        {
            textFields[fieldName].value = value;
        }
    }
    
    public void ShowElement(string elementName)
    {
        var element = rootElement.Q<VisualElement>(elementName);
        if (element != null)
        {
            element.style.display = DisplayStyle.Flex;
        }
    }
    
    public void HideElement(string elementName)
    {
        var element = rootElement.Q<VisualElement>(elementName);
        if (element != null)
        {
            element.style.display = DisplayStyle.None;
        }
    }
    
    void ResetUI()
    {
        foreach (var textField in textFields.Values)
        {
            textField.value = string.Empty;
        }
        
        foreach (var label in labels.Values)
        {
            label.text = "Ready";
        }
        
        Debug.Log("UI Reset completed!");
    }
    
    // Dynamic UI creation methods
    public Button CreateButton(string name, string text, VisualElement parent = null)
    {
        var button = new Button { text = text, name = name };
        
        if (parent == null)
            parent = rootElement;
            
        parent.Add(button);
        buttons[name] = button;
        
        button.clicked += () => OnButtonClicked(name);
        
        return button;
    }
    
    public Label CreateLabel(string name, string text, VisualElement parent = null)
    {
        var label = new Label { text = text, name = name };
        
        if (parent == null)
            parent = rootElement;
            
        parent.Add(label);
        labels[name] = label;
        
        return label;
    }
    
    public TextField CreateTextField(string name, string placeholder, VisualElement parent = null)
    {
        var textField = new TextField { name = name };
        textField.SetValueWithoutNotify(placeholder);
        
        if (parent == null)
            parent = rootElement;
            
        parent.Add(textField);
        textFields[name] = textField;
        
        textField.RegisterValueChangedCallback(evt => OnTextFieldChanged(name, evt.newValue));
        
        return textField;
    }
}