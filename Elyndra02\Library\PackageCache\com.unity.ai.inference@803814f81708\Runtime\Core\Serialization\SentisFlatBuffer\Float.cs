// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>
#define ENABLE_SPAN_T
#define UNSAFE_BYTEBUFFER
#define BYTEBUFFER_NO_BOUNDS_CHECK

namespace SentisFlatBuffer
{

using global::System;
using global::System.Collections.Generic;
using global::Unity.InferenceEngine.Google.FlatBuffers;

struct Float : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_5_26(); }
  public static Float GetRootAsFloat(ByteBuffer _bb) { return GetRootAsFloat(_bb, new Float()); }
  public static Float GetRootAsFloat(ByteBuffer _bb, Float obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, By<PERSON><PERSON><PERSON><PERSON> _bb) { __p = new Table(_i, _bb); }
  public Float __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public float FloatVal { get { int o = __p.__offset(4); return o != 0 ? __p.bb.GetFloat(o + __p.bb_pos) : (float)0.0f; } }

  public static Offset<SentisFlatBuffer.Float> CreateFloat(FlatBufferBuilder builder,
      float float_val = 0.0f) {
    builder.StartTable(1);
    Float.AddFloatVal(builder, float_val);
    return Float.EndFloat(builder);
  }

  public static void StartFloat(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddFloatVal(FlatBufferBuilder builder, float floatVal) { builder.AddFloat(0, floatVal, 0.0f); }
  public static Offset<SentisFlatBuffer.Float> EndFloat(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<SentisFlatBuffer.Float>(o);
  }
}


static class FloatVerify
{
  static public bool Verify(Unity.InferenceEngine.Google.FlatBuffers.Verifier verifier, uint tablePos)
  {
    return verifier.VerifyTableStart(tablePos)
      && verifier.VerifyField(tablePos, 4 /*FloatVal*/, 4 /*float*/, 4, false)
      && verifier.VerifyTableEnd(tablePos);
  }
}

}
