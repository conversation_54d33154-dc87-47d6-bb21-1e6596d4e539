using UnityEngine;
using UnityEditor;
using UnityEngine.Rendering;

public class DirectLightingTest
{
    [MenuItem("Debug/Direct LightingSettings Test")]
    public static void TestLightingSettingsCreation()
    {
        Debug.LogError("[DIRECT TEST] Starting LightingSettings test");
        
        try
        {
            // Try to access current LightingSettings
            var current = Lightmapping.lightingSettings;
            Debug.LogError($"[DIRECT TEST] Current LightingSettings: {(current == null ? "NULL" : "EXISTS")}");
            
            if (current == null)
            {
                Debug.LogError("[DIRECT TEST] Creating new LightingSettings");
                
                // Create new LightingSettings
                var newSettings = new LightingSettings();
                Debug.LogError("[DIRECT TEST] LightingSettings created successfully");
                
                // Try to assign it
                Lightmapping.lightingSettings = newSettings;
                Debug.LogError("[DIRECT TEST] LightingSettings assigned successfully");
                
                // Verify assignment
                var verify = Lightmapping.lightingSettings;
                Debug.LogError($"[DIRECT TEST] Verification: {(verify == null ? "FAILED" : "SUCCESS")}");
            }
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"[DIRECT TEST] Exception: {ex.Message}");
            Debug.LogError($"[DIRECT TEST] Stack trace: {ex.StackTrace}");
        }
    }
}