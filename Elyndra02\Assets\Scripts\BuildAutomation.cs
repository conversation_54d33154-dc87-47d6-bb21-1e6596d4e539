using UnityEngine;
using UnityEditor;
using System.IO;
using System.Collections.Generic;

#if UNITY_EDITOR
public class BuildAutomation : MonoBehaviour
{
    [Header("Build Settings")]
    public BuildTarget targetPlatform = BuildTarget.StandaloneWindows64;
    public string buildPath = "Builds/";
    public string productName = "MyGame";
    public bool developmentBuild = false;
    
    [Header("Build Options")]
    public bool autoRunPlayer = false;
    public bool showBuiltPlayer = true;
    public bool allowDebugging = false;
    
    [Header("Scene Management")]
    public List<string> scenesToBuild = new List<string>();
    
    void Start()
    {
        InitializeBuildSystem();
    }
    
    void InitializeBuildSystem()
    {
        // Get all scenes in build settings
        if (scenesToBuild.Count == 0)
        {
            EditorBuildSettingsScene[] scenes = EditorBuildSettings.scenes;
            foreach (var scene in scenes)
            {
                if (scene.enabled)
                {
                    scenesToBuild.Add(scene.path);
                }
            }
        }
        
        Debug.Log("Build Automation System initialized!");
        Debug.Log($"Target Platform: {targetPlatform}");
        Debug.Log($"Scenes to build: {scenesToBuild.Count}");
    }
    
    [ContextMenu("Build Game")]
    public void BuildGame()
    {
        if (Application.isPlaying)
        {
            Debug.LogWarning("Cannot build while in play mode!");
            return;
        }
        
        StartBuild();
    }
    
    void StartBuild()
    {
        Debug.Log("Starting build process...");
        
        // Prepare build settings
        BuildPlayerOptions buildOptions = new BuildPlayerOptions();
        buildOptions.scenes = scenesToBuild.ToArray();
        buildOptions.locationPathName = GetBuildPath();
        buildOptions.target = targetPlatform;
        buildOptions.options = GetBuildOptions();
        
        // Set player settings
        PlayerSettings.productName = productName;
        PlayerSettings.companyName = "MyCompany";
        
        Debug.Log($"Building to: {buildOptions.locationPathName}");
        
        // Start the build
        var report = BuildPipeline.BuildPlayer(buildOptions);
        
        // Handle build result
        HandleBuildResult(report);
    }
    
    string GetBuildPath()
    {
        string path = Path.Combine(buildPath, productName);
        
        switch (targetPlatform)
        {
            case BuildTarget.StandaloneWindows64:
                path += ".exe";
                break;
            case BuildTarget.StandaloneOSX:
                path += ".app";
                break;
            case BuildTarget.StandaloneLinux64:
                // No extension needed
                break;
            case BuildTarget.Android:
                path += ".apk";
                break;
            case BuildTarget.iOS:
                // iOS builds to a folder
                break;
        }
        
        return path;
    }
    
    BuildOptions GetBuildOptions()
    {
        BuildOptions options = BuildOptions.None;
        
        if (developmentBuild)
            options |= BuildOptions.Development;
            
        if (autoRunPlayer)
            options |= BuildOptions.AutoRunPlayer;
            
        if (showBuiltPlayer)
            options |= BuildOptions.ShowBuiltPlayer;
            
        if (allowDebugging)
            options |= BuildOptions.AllowDebugging;
            
        return options;
    }
    
    void HandleBuildResult(UnityEditor.Build.Reporting.BuildReport report)
    {
        var summary = report.summary;
        
        if (summary.result == UnityEditor.Build.Reporting.BuildResult.Succeeded)
        {
            Debug.Log($"Build succeeded! Size: {summary.totalSize} bytes");
            Debug.Log($"Build time: {summary.totalTime}");
            
            if (showBuiltPlayer)
            {
                EditorUtility.RevealInFinder(summary.outputPath);
            }
        }
        else
        {
            Debug.LogError($"Build failed with result: {summary.result}");
            
            // Log build steps for debugging
            foreach (var step in report.steps)
            {
                Debug.Log($"Build step: {step.name} - Duration: {step.duration}");
            }
        }
    }
    
    [ContextMenu("Clean Build Folder")]
    public void CleanBuildFolder()
    {
        if (Directory.Exists(buildPath))
        {
            Directory.Delete(buildPath, true);
            Debug.Log($"Cleaned build folder: {buildPath}");
        }
    }
    
    [ContextMenu("Set Development Build")]
    public void SetDevelopmentBuild()
    {
        developmentBuild = true;
        Debug.Log("Set to development build");
    }
    
    [ContextMenu("Set Release Build")]
    public void SetReleaseBuild()
    {
        developmentBuild = false;
        Debug.Log("Set to release build");
    }
    
    public void SetBuildTarget(BuildTarget target)
    {
        targetPlatform = target;
        Debug.Log($"Build target set to: {target}");
    }
    
    public void AddSceneToBuild(string scenePath)
    {
        if (!scenesToBuild.Contains(scenePath))
        {
            scenesToBuild.Add(scenePath);
            Debug.Log($"Added scene to build: {scenePath}");
        }
    }
    
    public void RemoveSceneFromBuild(string scenePath)
    {
        if (scenesToBuild.Contains(scenePath))
        {
            scenesToBuild.Remove(scenePath);
            Debug.Log($"Removed scene from build: {scenePath}");
        }
    }
}
#else
public class BuildAutomation : MonoBehaviour
{
    void Start()
    {
        Debug.Log("Build Automation is only available in Editor mode.");
    }
}
#endif