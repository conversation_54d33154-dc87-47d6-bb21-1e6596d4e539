using UnityEngine;
using System.Collections;

public class TestAIScript : MonoBeh<PERSON>our
{
    [<PERSON><PERSON>("AI Settings")]
    public float detectionRange = 10f;
    public float moveSpeed = 5f;
    public Transform target;
    
    private void Start()
    {
        Debug.Log("AI Script initialized");
    }
    
    private void Update()
    {
        if (target != null)
        {
            float distance = Vector3.Distance(transform.position, target.position);
            if (distance <= detectionRange)
            {
                Vector3 direction = (target.position - transform.position).normalized;
                transform.position += direction * moveSpeed * Time.deltaTime;
            }
        }
    }
    
    private void OnDrawGizmosSelected()
    {
        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(transform.position, detectionRange);
    }
}