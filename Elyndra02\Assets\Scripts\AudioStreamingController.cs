
using UnityEngine;
using UnityEngine.Networking;
using System.Collections;

public class AudioStreamer : MonoBehaviour
{
    public string audioUrl = "https://example.com/audio.mp3";
    public bool autoPlay = false;
    private AudioSource audioSource;

    void Start()
    {
        audioSource = GetComponent<AudioSource>();
        if (audioSource == null)
        {
            audioSource = gameObject.AddComponent<AudioSource>();
        }

        if (autoPlay)
        {
            StartCoroutine(StreamAudio());
        }
    }

    public void Play()
    {
        StartCoroutine(StreamAudio());
    }

    private IEnumerator StreamAudio()
    {
        using (UnityWebRequest www = UnityWebRequestMultimedia.GetAudioClip(audioUrl, AudioType.UNKNOWN))
        {
            yield return www.SendWebRequest();

            if (www.result == UnityWebRequest.Result.ConnectionError || www.result == UnityWebRequest.Result.ProtocolError)
            {
                Debug.LogError(www.error);
            }
            else
            {
                AudioClip clip = DownloadHandlerAudioClip.GetContent(www);
                audioSource.clip = clip;
                audioSource.Play();
            }
        }
    }
}
