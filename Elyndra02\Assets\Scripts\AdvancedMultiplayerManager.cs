using UnityEngine;
using Unity.Netcode;
using System.Collections.Generic;

public class AdvancedMultiplayerManager : NetworkBehaviour
{
    [Head<PERSON>("Network Settings")]
    public int maxPlayers = 8;
    public float tickRate = 60f;
    public bool enableRelay = true;
    
    [Header("Session Management")]
    public string sessionName = "DefaultSession";
    public bool isPrivateSession = false;
    public string sessionPassword = "";
    
    [Header("Player Management")]
    public GameObject playerPrefab;
    public Transform[] spawnPoints;
    
    private Dictionary<ulong, NetworkObject> connectedPlayers;
    private NetworkVariable<int> playerCount = new NetworkVariable<int>(0);
    
    public override void OnNetworkSpawn()
    {
        connectedPlayers = new Dictionary<ulong, NetworkObject>();
        
        if (IsServer)
        {
            Unity.Netcode.NetworkManager.Singleton.OnClientConnectedCallback += OnClientConnected;
            Unity.Netcode.NetworkManager.Singleton.OnClientDisconnectCallback += OnClientDisconnected;
        }
        
        Debug.Log("Advanced Multiplayer Manager spawned");
    }
    
    void OnClientConnected(ulong clientId)
    {
        Debug.Log($"Client {clientId} connected");
        
        if (IsServer)
        {
            SpawnPlayer(clientId);
            playerCount.Value = Unity.Netcode.NetworkManager.Singleton.ConnectedClients.Count;
        }
    }
    
    void OnClientDisconnected(ulong clientId)
    {
        Debug.Log($"Client {clientId} disconnected");
        
        if (IsServer)
        {
            if (connectedPlayers.ContainsKey(clientId))
            {
                if (connectedPlayers[clientId] != null)
                {
                    connectedPlayers[clientId].Despawn();
                }
                connectedPlayers.Remove(clientId);
            }
            
            playerCount.Value = Unity.Netcode.NetworkManager.Singleton.ConnectedClients.Count;
        }
    }
    
    void SpawnPlayer(ulong clientId)
    {
        if (playerPrefab == null)
        {
            Debug.LogError("Player prefab not assigned!");
            return;
        }
        
        Vector3 spawnPosition = GetSpawnPosition();
        Quaternion spawnRotation = Quaternion.identity;
        
        GameObject playerObj = Instantiate(playerPrefab, spawnPosition, spawnRotation);
        NetworkObject networkObj = playerObj.GetComponent<NetworkObject>();
        
        if (networkObj != null)
        {
            networkObj.SpawnAsPlayerObject(clientId);
            connectedPlayers[clientId] = networkObj;
            
            Debug.Log($"Player spawned for client {clientId} at {spawnPosition}");
        }
    }
    
    Vector3 GetSpawnPosition()
    {
        if (spawnPoints.Length > 0)
        {
            int randomIndex = Random.Range(0, spawnPoints.Length);
            return spawnPoints[randomIndex].position;
        }
        
        return Vector3.zero;
    }
    
    [ServerRpc(RequireOwnership = false)]
    public void RequestRespawnServerRpc(ulong clientId)
    {
        if (connectedPlayers.ContainsKey(clientId))
        {
            NetworkObject playerObj = connectedPlayers[clientId];
            if (playerObj != null)
            {
                Vector3 newSpawnPosition = GetSpawnPosition();
                playerObj.transform.position = newSpawnPosition;
                
                Debug.Log($"Player {clientId} respawned at {newSpawnPosition}");
            }
        }
    }
    
    [ServerRpc(RequireOwnership = false)]
    public void SendChatMessageServerRpc(string message, ulong senderId)
    {
        BroadcastChatMessageClientRpc(message, senderId);
    }
    
    [ClientRpc]
    void BroadcastChatMessageClientRpc(string message, ulong senderId)
    {
        Debug.Log($"Chat from {senderId}: {message}");
    }
    
    public void StartHost()
    {
        if (Unity.Netcode.NetworkManager.Singleton != null)
        {
            Unity.Netcode.NetworkManager.Singleton.StartHost();
            Debug.Log("Started as Host");
        }
    }
    
    public void StartServer()
    {
        if (Unity.Netcode.NetworkManager.Singleton != null)
        {
            Unity.Netcode.NetworkManager.Singleton.StartServer();
            Debug.Log("Started as Server");
        }
    }
    
    public void StartClient()
    {
        if (Unity.Netcode.NetworkManager.Singleton != null)
        {
            Unity.Netcode.NetworkManager.Singleton.StartClient();
            Debug.Log("Started as Client");
        }
    }
    
    public void Disconnect()
    {
        if (Unity.Netcode.NetworkManager.Singleton != null)
        {
            Unity.Netcode.NetworkManager.Singleton.Shutdown();
            Debug.Log("Disconnected from network");
        }
    }
    
    public int GetPlayerCount()
    {
        return playerCount.Value;
    }
    
    public bool IsSessionFull()
    {
        return GetPlayerCount() >= maxPlayers;
    }
    
    void OnGUI()
    {
        if (!IsSpawned) return;
        
        GUILayout.BeginArea(new Rect(10, 10, 200, 200));
        
        GUILayout.Label($"Players: {GetPlayerCount()}/{maxPlayers}");
        GUILayout.Label($"Session: {sessionName}");
        
        if (IsServer)
        {
            GUILayout.Label("[SERVER]");
        }
        else if (IsClient)
        {
            GUILayout.Label("[CLIENT]");
        }
        
        GUILayout.EndArea();
    }
}