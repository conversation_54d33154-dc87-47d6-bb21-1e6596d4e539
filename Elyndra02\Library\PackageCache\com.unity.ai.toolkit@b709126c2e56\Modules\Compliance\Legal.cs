using System;
using System.ComponentModel;
using UnityEngine;
#if UNITY_AI_ASSET_LABEL_FIX
using UnityEditor;
using UnityEditor.Search;
using System.Collections.Generic;
using System.Linq;
#endif

namespace Unity.AI.Toolkit.Compliance
{
    /// <summary>
    /// Legal related constants.
    /// </summary>
    [EditorBrowsable(EditorBrowsableState.Never)]
    public static class Legal
    {
        /// <summary>
        /// The label used to mark assets generated by Unity AI.
        /// </summary>
        public const string UnityAIGeneratedLabel = "UnityAI";

#if UNITY_AI_ASSET_LABEL_FIX
        [InitializeOnLoadMethod]
        static void FixDeprecatedLabelsOnLoad()
        {
            if (Application.isBatchMode)
                return;

            EditorApplication.delayCall += FixDeprecatedLabels;
        }

        static void FixDeprecatedLabels()
        {
            // Build search query for all deprecated labels with OR conditions
            var searchQueryParts = k_UnityAIGeneratedLabelsDeprecated.Select(label => $"l:\"{label}\"");
            var searchQuery = string.Join(" OR ", searchQueryParts);

            var context = new SearchContext(SearchService.GetActiveProviders().Where(p => p.id == "asset").ToList(), searchQuery);
            SearchService.Request(context, (_, items) => {
                var foundAssetPaths = new List<string>();
                foreach (var item in items)
                {
                    var assetPath = SearchUtils.GetAssetPath(item);
                    if (string.IsNullOrEmpty(assetPath))
                        continue;

                    var assetLabels = AssetDatabase.GetLabels(AssetDatabase.GUIDFromAssetPath(assetPath));
                    // Check if the asset has any of the deprecated labels
                    if (!k_UnityAIGeneratedLabelsDeprecated.Any(deprecatedLabel => assetLabels.Contains(deprecatedLabel)))
                        continue;

                    foundAssetPaths.Add(assetPath);
                }
                if (foundAssetPaths.Count == 0)
                    return;

                Debug.Log($"Found {foundAssetPaths.Count} asset(s) with deprecated Unity AI labels to update to '{UnityAIGeneratedLabel}'. Processing...");

                var fixedCount = 0;
                var changesMade = false;
                foreach (var assetPath in foundAssetPaths)
                {
                    var asset = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(assetPath);
                    if (asset == null)
                    {
                        Debug.LogWarning($"Could not load asset at path: {assetPath})");
                        continue;
                    }

                    var labels = AssetDatabase.GetLabels(asset).ToList();
                    var labelWasRemoved = false;

                    // Remove any deprecated labels
                    foreach (var deprecatedLabel in k_UnityAIGeneratedLabelsDeprecated)
                    {
                        if (labels.Remove(deprecatedLabel))
                            labelWasRemoved = true;
                    }

                    if (labelWasRemoved)
                    {
                        if (!labels.Contains(UnityAIGeneratedLabel))
                            labels.Add(UnityAIGeneratedLabel);

                        AssetDatabase.SetLabels(asset, labels.ToArray());
                        fixedCount++;
                        changesMade = true;
                    }
                }

                if (changesMade)
                {
                    AssetDatabase.SaveAssets();
                    Debug.Log($"Label fix complete. Updated deprecated Unity AI labels to '{UnityAIGeneratedLabel}' on {fixedCount} asset(s). Asset database saved.");
                }
                else
                {
                    Debug.Log($"Processed {foundAssetPaths.Count} asset(s) found with deprecated Unity AI labels, but no actual label changes were required during processing.");
                }
            }, SearchFlags.Default);
        }

        // List of deprecated labels that should be replaced with UnityAIGeneratedLabel
        static readonly string[] k_UnityAIGeneratedLabelsDeprecated = new[] { "Unity AI", "Unityai" };
#endif
    }
}
