from mcp.server.fastmcp import FastMCP, Context
from typing import Dict, Any, List, Optional
from unity_connection import get_unity_connection

def register_one_click_character_creator_tools(mcp: FastMCP):
    """Register one-click character creator tools with the MCP server."""

    @mcp.tool()
    def one_click_character_creator(
        ctx: Context,
        character_name: str,
        character_type: str = "humanoid",
        style: str = "realistic",
        gender: str = "male",
        age_range: str = "adult",
        body_type: str = "average",
        enable_ai: bool = True,
        enable_audio: bool = True,
        enable_gameplay_systems: bool = True,
        quality_level: str = "high",
        output_directory: str = "Assets/GeneratedCharacters",
        create_variations: int = 1,
        auto_optimize: bool = True
    ) -> Dict[str, Any]:
        """
        **FERRAMENTA MASTER** - Cria personagens 100% procedurais com UM CLIQUE!

        Esta ferramenta orquestra TODAS as outras para criar personagens completos:
        1. <PERSON><PERSON> todas as partes do corpo (cabeça, torso, membros)
        2. Cria texturas realistas (pele, cabelo, olhos, roupas)
        3. Gera esqueleto e rig humanoid
        4. Cria animações básicas (idle, walk, run)
        5. Configura sistemas de gameplay completos
        6. Adiciona IA e áudio
        7. Otimiza para Unity 6.2
        
        Args:
            character_name: Nome do personagem
            character_type: Tipo (humanoid, creature, robot)
            style: Estilo visual (realistic, cartoon, stylized)
            gender: Gênero (male, female, non_binary)
            age_range: Faixa etária (child, teen, adult, elderly)
            body_type: Tipo corporal (thin, average, athletic, heavy)
            enable_ai: Habilitar IA comportamental
            enable_audio: Habilitar sistema de áudio
            enable_gameplay_systems: Habilitar sistemas de gameplay
            quality_level: Qualidade (low, medium, high, ultra)
            output_directory: Diretório de saída
            create_variations: Número de variações
            auto_optimize: Otimização automática
        
        Returns:
            Status da criação e detalhes do personagem
        """
        
        try:
            connection = get_unity_connection()
            
            # FASE 1: Geração de Partes do Corpo
            print(f"[FASE 1/7] Gerando partes do corpo para {character_name}...")

            # Gerar cabeça
            head_result = connection.send_command("procedural_body_part_generation", {
                "action": "generate_head",
                "body_part_type": "head",
                "parameters": {
                    "style": style,
                    "gender": gender,
                    "age_range": age_range,
                    "body_type": body_type
                },
                "detail_level": quality_level,
                "output_path": f"{output_directory}/{character_name}/Meshes",
                "generate_uvs": True,
                "generate_normals": True,
                "generate_tangents": True,
                "enable_blend_shapes": True,
                "export_format": "prefab"
            })

            # Gerar torso
            torso_result = connection.send_command("procedural_body_part_generation", {
                "action": "generate_torso",
                "body_part_type": "torso",
                "parameters": {
                    "style": style,
                    "gender": gender,
                    "age_range": age_range,
                    "body_type": body_type
                },
                "detail_level": quality_level,
                "output_path": f"{output_directory}/{character_name}/Meshes",
                "generate_uvs": True,
                "generate_normals": True,
                "generate_tangents": True,
                "bone_weights": True,
                "export_format": "prefab"
            })

            # Gerar braços
            arms_result = connection.send_command("procedural_body_part_generation", {
                "action": "generate_arm",
                "body_part_type": "arm",
                "parameters": {
                    "style": style,
                    "gender": gender,
                    "body_type": body_type,
                    "generate_both_arms": True
                },
                "detail_level": quality_level,
                "output_path": f"{output_directory}/{character_name}/Meshes",
                "generate_uvs": True,
                "bone_weights": True,
                "export_format": "prefab"
            })

            # Gerar pernas
            legs_result = connection.send_command("procedural_body_part_generation", {
                "action": "generate_leg",
                "body_part_type": "leg",
                "parameters": {
                    "style": style,
                    "gender": gender,
                    "body_type": body_type,
                    "generate_both_legs": True
                },
                "detail_level": quality_level,
                "output_path": f"{output_directory}/{character_name}/Meshes",
                "generate_uvs": True,
                "bone_weights": True,
                "export_format": "prefab"
            })

            body_parts_results = [head_result, torso_result, arms_result, legs_result]
            failed_parts = [r for r in body_parts_results if not r.get("success", False)]

            if failed_parts:
                return {"success": False, "error": f"Falha na geração das partes do corpo: {failed_parts}"}

            # FASE 2: Geração de Texturas
            print(f"[FASE 2/7] Gerando texturas para {character_name}...")
            # Gerar textura de pele
            skin_texture_result = connection.send_command("procedural_texture_generation", {
                "action": "generate_skin",
                "texture_type": "diffuse",
                "texture_category": "skin",
                "parameters": {
                    "skin_tone": "medium_tan",
                    "age": 30,
                    "gender": gender,
                    "weathering": 0.7,
                    "battle_scars": True,
                    "muscle_definition": 0.8
                },
                "width": 1024 if quality_level == "medium" else 2048,
                "height": 1024 if quality_level == "medium" else 2048,
                "output_path": f"{output_directory}/{character_name}/Textures",
                "use_ai_generator": True,
                "generate_normal_map": True,
                "generate_roughness_map": True,
                "enable_mipmaps": True,
                "compression_quality": "high"
            })

            # Gerar textura de armadura
            armor_texture_result = connection.send_command("procedural_texture_generation", {
                "action": "generate_metal",
                "texture_type": "diffuse",
                "texture_category": "metal",
                "parameters": {
                    "metal_type": "steel_armor",
                    "color_base": "#4A5568",
                    "weathering": 0.6,
                    "battle_damage": True,
                    "engravings": True
                },
                "width": 1024 if quality_level == "medium" else 2048,
                "height": 1024 if quality_level == "medium" else 2048,
                "output_path": f"{output_directory}/{character_name}/Textures",
                "use_ai_generator": True,
                "generate_normal_map": True,
                "generate_roughness_map": True,
                "generate_metallic_map": True,
                "enable_mipmaps": True
            })

            texture_results = [skin_texture_result, armor_texture_result]
            failed_textures = [r for r in texture_results if not r.get("success", False)]

            if failed_textures:
                return {"success": False, "error": f"Falha na geração de texturas: {failed_textures}"}

            # FASE 3: Geração de Esqueleto
            print(f"[FASE 3/7] Gerando esqueleto para {character_name}...")
            skeleton_result = connection.send_command("procedural_skeleton_generation", {
                "action": "generate_humanoid" if character_type == "humanoid" else "generate_custom",
                "skeleton_type": character_type,
                "character_height": 1.85,
                "bone_proportions": {"head": 0.13, "torso": 0.3, "arms": 0.25, "legs": 0.32},
                "rig_type": "humanoid",
                "enable_facial_bones": True,
                "enable_finger_bones": True,
                "enable_toe_bones": False,
                "bone_count_optimization": "high",
                "auto_generate_ik": True,
                "create_constraint_components": True,
                "generate_bone_weights": True,
                "symmetry_mode": "bilateral",
                "joint_limits": True,
                "optimization_target": "quality",
                "output_path": f"{output_directory}/{character_name}/Rig"
            })

            if not skeleton_result.get("success", False):
                return {"success": False, "error": f"Falha na geração do esqueleto: {skeleton_result}"}

            # FASE 4: Geração de Animações
            print(f"[FASE 4/7] Gerando animações para {character_name}...")
            animations = [
                {
                    "type": "idle",
                    "duration": 4.0,
                    "motion_data": {"breathing_intensity": 0.3, "weight_shift": 0.2, "weapon_hold": "spear_ready"}
                },
                {
                    "type": "walk",
                    "duration": 2.0,
                    "motion_data": {"walk_speed": 1.2, "stride_length": 1.0, "weapon_carry": "spear_shoulder"}
                },
                {
                    "type": "run",
                    "duration": 1.5,
                    "motion_data": {"run_speed": 2.5, "stride_length": 1.3, "weapon_carry": "spear_running"}
                }
            ]

            animation_results = []

            for anim_config in animations:
                anim_result = connection.send_command("procedural_animation_generation", {
                    "action": f"generate_{anim_config['type']}",
                    "animation_type": "locomotion" if anim_config['type'] != "idle" else "idle",
                    "animation_style": style,
                    "duration": anim_config["duration"],
                    "frame_rate": 30,
                    "loop_type": "loop",
                    "target_skeleton": f"{output_directory}/{character_name}/Rig/ProceduralSkeleton.prefab",
                    "use_ai_generator": True,
                    "motion_data": anim_config["motion_data"],
                    "generate_root_motion": anim_config['type'] != "idle",
                    "optimize_curves": True,
                    "compression_level": "medium",
                    "generate_events": True,
                    "retargeting_mode": "humanoid",
                    "quality_level": quality_level,
                    "output_path": f"{output_directory}/{character_name}/Animations"
                })
                animation_results.append(anim_result)

            failed_animations = [r for r in animation_results if not r.get("success", False)]
            if failed_animations:
                print(f"Aviso: Algumas animações falharam: {failed_animations}")

            # FASE 5: Montagem do Personagem
            print(f"[FASE 5/7] Montando personagem {character_name}...")
            # Coletar caminhos das partes do corpo geradas
            body_parts_paths = []
            if head_result.get("success"):
                body_parts_paths.append(head_result.get("data", {}).get("meshPath", ""))
            if torso_result.get("success"):
                body_parts_paths.append(torso_result.get("data", {}).get("meshPath", ""))

            # Filtrar caminhos vazios
            body_parts_paths = [path for path in body_parts_paths if path]

            assembly_result = connection.send_command("assemble_character_from_parts", {
                "character_name": character_name,
                "body_parts": body_parts_paths,
                "target_directory": f"{output_directory}/{character_name}",
                "auto_rig": True,
                "generate_lods": True,
                "combine_meshes": False,
                "setup_physics": True,
                "enable_gpu_instancing": True,
                "render_pipeline": "URP",
                "enable_subsurface_scattering": quality_level in ["high", "ultra"],
                "enable_advanced_facial_rig": True,
                "enable_dynamic_hair": False,
                "enable_eye_tracking": True,
                "material_quality": quality_level,
                "enable_ai_optimization": auto_optimize,
                "enable_raytracing": quality_level == "ultra"
            })

            if not assembly_result.get("success", False):
                return {"success": False, "error": f"Falha na montagem do personagem: {assembly_result}"}

            # Aplicar texturas ao personagem montado
            character_path = assembly_result.get("data", {}).get("characterPath", f"{output_directory}/{character_name}/{character_name}.prefab")

            if skin_texture_result.get("success"):
                texture_data = skin_texture_result.get("data", {})
                texture_mappings = {
                    "_MainTex": texture_data.get("diffuseMap", ""),
                    "_BumpMap": texture_data.get("additionalMaps", [""])[0] if texture_data.get("additionalMaps") else "",
                    "_RoughnessMap": texture_data.get("additionalMaps", ["", ""])[1] if len(texture_data.get("additionalMaps", [])) > 1 else ""
                }

                # Filtrar mapeamentos vazios
                texture_mappings = {k: v for k, v in texture_mappings.items() if v}

                if texture_mappings:
                    texture_apply_result = connection.send_command("apply_generated_textures_to_character", {
                        "character_path": character_path,
                        "texture_mappings": texture_mappings,
                        "create_material_variants": True,
                        "apply_to_lods": True
                    })

            # FASE 6: Sistemas de Gameplay
            if enable_gameplay_systems:
                print(f"[FASE 6/7] Configurando sistemas de gameplay para {character_name}...")
                gameplay_result = connection.send_command("character_gameplay_system", {
                    "action": "setup_complete_character",
                    "character_path": character_path,
                    "system_type": "player",
                    "enable_health_system": True,
                    "enable_experience_system": True,
                    "enable_skill_system": True,
                    "enable_inventory": True,
                    "enable_combat_system": True,
                    "ai_behavior_type": "basic" if enable_ai else "none",
                    "audio_setup": "full" if enable_audio else "minimal",
                    "networking_support": False,
                    "save_system_integration": True
                })

                if not gameplay_result.get("success", False):
                    print(f"Aviso: Falha nos sistemas de gameplay: {gameplay_result}")

            # FASE 7: Otimização Final
            if auto_optimize:
                print(f"[FASE 7/7] Otimizando {character_name} para Unity 6.2...")
                optimization_result = connection.send_command("optimize_character_with_ai", {
                    "character_path": character_path,
                    "optimization_mode": "Balanced",
                    "optimize_meshes": True,
                    "optimize_materials": True,
                    "optimize_textures": True,
                    "generate_performance_variants": True
                })

            # Criar variações se solicitado
            variations_created = []
            if create_variations > 1:
                print(f"Criando {create_variations-1} variações adicionais...")
                for i in range(1, create_variations):
                    variation_name = f"{character_name}_Variation_{i}"
                    variation_result = connection.send_command("generate_character_variations", {
                        "base_character_path": character_path,
                        "variation_count": 1,
                        "variation_types": ["color", "accessories"],
                        "color_variations": True,
                        "accessory_variations": True,
                        "body_shape_variations": False
                    })
                    if variation_result.get("success", False):
                        variations_created.append(variation_name)

            print(f"Personagem {character_name} criado com SUCESSO!")
            
            return {
                "success": True,
                "message": f"Personagem '{character_name}' criado com sucesso!",
                "character_details": {
                    "name": character_name,
                    "type": character_type,
                    "style": style,
                    "gender": gender,
                    "age_range": age_range,
                    "body_type": body_type,
                    "quality_level": quality_level,
                    "path": f"{output_directory}/{character_name}",
                    "has_ai": enable_ai,
                    "has_audio": enable_audio,
                    "has_gameplay_systems": enable_gameplay_systems,
                    "variations_created": variations_created,
                    "optimized": auto_optimize,
                    "unity_6_2_compatible": True
                },
                "phases_completed": [
                    "Partes do corpo geradas",
                    "Texturas procedurais criadas",
                    "Esqueleto e rig configurados",
                    "Animações básicas geradas",
                    "Personagem montado",
                    "Sistemas de gameplay configurados" if enable_gameplay_systems else "Sistemas de gameplay pulados",
                    "Otimização Unity 6.2 aplicada" if auto_optimize else "Otimização pulada"
                ]
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Erro durante a criação do personagem: {str(e)}",
                "character_name": character_name
            }

    @mcp.tool()
    def batch_character_creator(
        ctx: Context,
        character_count: int,
        base_name: str = "Character",
        character_types: Optional[List[str]] = None,
        styles: Optional[List[str]] = None,
        randomize_attributes: bool = True,
        output_directory: str = "Assets/GeneratedCharacters/Batch"
    ) -> Dict[str, Any]:
        """
        **CRIAÇÃO EM LOTE** - Cria múltiplos personagens automaticamente!
        
        Args:
            character_count: Número de personagens a criar
            base_name: Nome base para os personagens
            character_types: Lista de tipos de personagem
            styles: Lista de estilos visuais
            randomize_attributes: Randomizar atributos
            output_directory: Diretório de saída
        
        Returns:
            Status da criação em lote
        """
        
        import random
        
        if character_types is None:
            character_types = ["humanoid"]
        if styles is None:
            styles = ["realistic", "cartoon", "stylized"]
        
        genders = ["male", "female", "non_binary"]
        age_ranges = ["teen", "adult", "elderly"]
        body_types = ["thin", "average", "athletic", "heavy"]
        
        created_characters = []
        failed_characters = []
        
        for i in range(character_count):
            character_name = f"{base_name}_{i+1:03d}"
            
            # Atributos aleatórios ou fixos
            if randomize_attributes:
                char_type = random.choice(character_types)
                style = random.choice(styles)
                gender = random.choice(genders)
                age_range = random.choice(age_ranges)
                body_type = random.choice(body_types)
            else:
                char_type = character_types[0]
                style = styles[0]
                gender = "male"
                age_range = "adult"
                body_type = "average"
            
            print(f"Criando personagem {i+1}/{character_count}: {character_name}")

            # Criar personagem usando a ferramenta master
            result = one_click_character_creator(
                ctx,
                character_name=character_name,
                character_type=char_type,
                style=style,
                gender=gender,
                age_range=age_range,
                body_type=body_type,
                enable_ai=True,
                enable_audio=True,
                enable_gameplay_systems=True,
                quality_level="medium",
                output_directory=output_directory,
                create_variations=1,
                auto_optimize=True
            )

            if result.get("success", False):
                created_characters.append(character_name)
                print(f"{character_name} criado com sucesso!")
            else:
                failed_characters.append({
                    "name": character_name,
                    "error": result.get("error", "Erro desconhecido")
                })
                print(f"Falha ao criar {character_name}: {result.get('error', 'Erro desconhecido')}")
        
        return {
            "success": len(created_characters) > 0,
            "message": f"Criação em lote concluída: {len(created_characters)}/{character_count} personagens criados",
            "created_characters": created_characters,
            "failed_characters": failed_characters,
            "batch_stats": {
                "total_requested": character_count,
                "successfully_created": len(created_characters),
                "failed": len(failed_characters),
                "success_rate": f"{(len(created_characters)/character_count)*100:.1f}%"
            }
        } 