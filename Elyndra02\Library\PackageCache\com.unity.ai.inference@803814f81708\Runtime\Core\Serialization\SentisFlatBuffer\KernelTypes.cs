// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>
#define ENABLE_SPAN_T
#define UNSAFE_BYTEBUFFER
#define BYTEBUFFER_NO_BOUNDS_CHECK

namespace SentisFlatBuffer
{

enum KernelTypes : byte
{
  NONE = 0,
  Null = 1,
  Int = 2,
  Float = 3,
  Bool = 4,
  Byte = 5,
  Tensor = 6,
  String = 7,
  IntList = 8,
  FloatList = 9,
  BoolList = 10,
};


}
