using UnityEngine;
using System.Collections.Generic;

public class SimplePhysicsController : MonoBehaviour
{
    [<PERSON><PERSON>("Physics Settings")]
    public float gravity = -9.81f;
    public int solverIterations = 6;
    public float bounceThreshold = 2f;
    
    [Header("Simulation")]
    public bool enableFluidSim = false;
    public float fluidDensity = 1000f;
    
    private List<Rigidbody> trackedBodies = new List<Rigidbody>();
    
    void Start()
    {
        InitializePhysics();
    }
    
    void InitializePhysics()
    {
        Physics.gravity = new Vector3(0, gravity, 0);
        Physics.defaultSolverIterations = solverIterations;
        Physics.bounceThreshold = bounceThreshold;
        
        Rigidbody[] bodies = UnityEngine.Object.FindObjectsByType<Rigidbody>(FindObjectsSortMode.None);
        trackedBodies.AddRange(bodies);
        
        Debug.Log($"Physics Controller initialized!");
        Debug.Log($"Tracking {trackedBodies.Count} rigidbodies");
        LogSettings();
    }
    
    void Update()
    {
        if (enableFluidSim)
        {
            SimulateFluid();
        }
    }
    
    void SimulateFluid()
    {
        foreach (Rigidbody rb in trackedBodies)
        {
            if (rb != null && rb.transform.position.y < 0f)
            {
                // Buoyancy
                float buoyancy = fluidDensity * Mathf.Abs(Physics.gravity.y) * 0.1f;
                rb.AddForce(Vector3.up * buoyancy);
                
                // Drag
                rb.AddForce(-rb.linearVelocity * 0.5f);
            }
        }
    }
    
    public void SetGravity(float newGravity)
    {
        gravity = newGravity;
        Physics.gravity = new Vector3(0, gravity, 0);
        Debug.Log($"Gravity: {gravity}");
    }
    
    public void SetSolverIterations(int iterations)
    {
        solverIterations = iterations;
        Physics.defaultSolverIterations = iterations;
        Debug.Log($"Solver iterations: {iterations}");
    }
    
    public void ToggleFluidSim()
    {
        enableFluidSim = !enableFluidSim;
        Debug.Log($"Fluid simulation: {enableFluidSim}");
    }
    
    public void AddExplosion(Vector3 pos, float force, float radius)
    {
        Collider[] cols = Physics.OverlapSphere(pos, radius);
        
        foreach (Collider col in cols)
        {
            Rigidbody rb = col.GetComponent<Rigidbody>();
            if (rb != null)
            {
                rb.AddExplosionForce(force, pos, radius);
            }
        }
        
        Debug.Log($"Explosion at {pos}");
    }
    
    public void ResetPhysics()
    {
        foreach (Rigidbody rb in trackedBodies)
        {
            if (rb != null)
            {
                rb.linearVelocity = Vector3.zero;
                rb.angularVelocity = Vector3.zero;
            }
        }
        
        Debug.Log("Physics reset");
    }
    
    void LogSettings()
    {
        Debug.Log($"Gravity: {Physics.gravity}");
        Debug.Log($"Solver: {Physics.defaultSolverIterations}");
        Debug.Log($"Bounce: {Physics.bounceThreshold}");
    }
}