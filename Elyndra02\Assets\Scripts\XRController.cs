using UnityEngine;
using UnityEngine.XR;
using System.Collections.Generic;

public class XRController : MonoBehaviour
{
    [<PERSON><PERSON>("XR Settings")]
    public bool enableXR = true;
    public XRNode controllerNode = XRNode.RightHand;
    
    [Header("Input Settings")]
    public float triggerThreshold = 0.1f;
    public float gripThreshold = 0.1f;
    
    [Head<PERSON>("Haptic Feedback")]
    public float hapticIntensity = 0.5f;
    public float hapticDuration = 0.1f;
    
    private InputDevice targetDevice;
    private bool deviceConnected = false;
    
    private void Start()
    {
        if (enableXR)
        {
            InitializeXR();
        }
    }
    
    private void InitializeXR()
    {
        Debug.Log("Initializing XR Controller...");
        
        // Check if XR is supported
        if (XRSettings.enabled)
        {
            Debug.Log($"XR Device: {XRSettings.loadedDeviceName}");
            Debug.Log($"XR Display: {XRSettings.eyeTextureWidth}x{XRSettings.eyeTextureHeight}");
        }
        else
        {
            Debug.LogWarning("XR is not enabled or supported");
        }
        
        TryGetDevice();
    }
    
    private void Update()
    {
        if (!enableXR) return;
        
        if (!deviceConnected)
        {
            TryGetDevice();
            return;
        }
        
        HandleInput();
        UpdatePosition();
    }
    
    private void TryGetDevice()
    {
        List<InputDevice> devices = new List<InputDevice>();
        InputDevices.GetDevicesAtXRNode(controllerNode, devices);
        
        if (devices.Count > 0)
        {
            targetDevice = devices[0];
            deviceConnected = true;
            Debug.Log($"XR Controller connected: {targetDevice.name}");
        }
    }
    
    private void HandleInput()
    {
        if (!targetDevice.isValid) return;
        
        // Trigger input
        if (targetDevice.TryGetFeatureValue(CommonUsages.trigger, out float triggerValue))
        {
            if (triggerValue > triggerThreshold)
            {
                OnTriggerPressed(triggerValue);
            }
        }
        
        // Grip input
        if (targetDevice.TryGetFeatureValue(CommonUsages.grip, out float gripValue))
        {
            if (gripValue > gripThreshold)
            {
                OnGripPressed(gripValue);
            }
        }
        
        // Primary button
        if (targetDevice.TryGetFeatureValue(CommonUsages.primaryButton, out bool primaryButton))
        {
            if (primaryButton)
            {
                OnPrimaryButtonPressed();
            }
        }
        
        // Secondary button
        if (targetDevice.TryGetFeatureValue(CommonUsages.secondaryButton, out bool secondaryButton))
        {
            if (secondaryButton)
            {
                OnSecondaryButtonPressed();
            }
        }
        
        // Thumbstick
        if (targetDevice.TryGetFeatureValue(CommonUsages.primary2DAxis, out Vector2 thumbstick))
        {
            if (thumbstick.magnitude > 0.1f)
            {
                OnThumbstickMoved(thumbstick);
            }
        }
    }
    
    private void UpdatePosition()
    {
        if (!targetDevice.isValid) return;
        
        // Update position
        if (targetDevice.TryGetFeatureValue(CommonUsages.devicePosition, out Vector3 position))
        {
            transform.localPosition = position;
        }
        
        // Update rotation
        if (targetDevice.TryGetFeatureValue(CommonUsages.deviceRotation, out Quaternion rotation))
        {
            transform.localRotation = rotation;
        }
    }
    
    private void OnTriggerPressed(float value)
    {
        Debug.Log($"Trigger pressed: {value:F2}");
        TriggerHapticFeedback();
    }
    
    private void OnGripPressed(float value)
    {
        Debug.Log($"Grip pressed: {value:F2}");
    }
    
    private void OnPrimaryButtonPressed()
    {
        Debug.Log("Primary button pressed");
        TriggerHapticFeedback();
    }
    
    private void OnSecondaryButtonPressed()
    {
        Debug.Log("Secondary button pressed");
    }
    
    private void OnThumbstickMoved(Vector2 value)
    {
        Debug.Log($"Thumbstick: {value}");
    }
    
    private void TriggerHapticFeedback()
    {
        if (targetDevice.isValid)
        {
            targetDevice.SendHapticImpulse(0, hapticIntensity, hapticDuration);
        }
    }
    
    public void SetHapticFeedback(float intensity, float duration)
    {
        hapticIntensity = Mathf.Clamp01(intensity);
        hapticDuration = Mathf.Max(0f, duration);
    }
    
    public bool IsDeviceConnected()
    {
        return deviceConnected && targetDevice.isValid;
    }
    
    public Vector3 GetControllerPosition()
    {
        if (targetDevice.TryGetFeatureValue(CommonUsages.devicePosition, out Vector3 position))
        {
            return position;
        }
        return Vector3.zero;
    }
    
    public Quaternion GetControllerRotation()
    {
        if (targetDevice.TryGetFeatureValue(CommonUsages.deviceRotation, out Quaternion rotation))
        {
            return rotation;
        }
        return Quaternion.identity;
    }
}