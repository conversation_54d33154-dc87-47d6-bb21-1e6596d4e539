using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
using UnityEditor.Build;
using UnityEditor.Build.Reporting;
#endif

public class BuildOptimizer : MonoBehaviour
{
    [Header("Build Settings")]
    public bool optimizeForPerformance = true;
    public bool stripUnusedCode = true;
    public bool compressTextures = true;
    
    private void Start()
    {
        Debug.Log("Build Optimizer initialized");
    }
    
    public void LogBuildInfo()
    {
        Debug.Log($"Platform: {Application.platform}");
        Debug.Log($"Unity Version: {Application.unityVersion}");
        Debug.Log($"Is Development Build: {Debug.isDebugBuild}");
        Debug.Log($"Data Path: {Application.dataPath}");
        Debug.Log($"Persistent Data Path: {Application.persistentDataPath}");
    }
}

#if UNITY_EDITOR
public class BuildOptimizerProcessor : IPreprocessBuildWithReport
{
    public int callbackOrder => 0;
    
    public void OnPreprocessBuild(BuildReport report)
    {
        Debug.Log("Build Optimizer: Preprocessing build...");
        
        // Find BuildOptimizer in scene
        BuildOptimizer optimizer = UnityEngine.Object.FindFirstObjectByType<BuildOptimizer>();
        if (optimizer != null)
        {
            if (optimizer.optimizeForPerformance)
            {
                Debug.Log("Build Optimizer: Applying performance optimizations");
                // Apply performance settings
                PlayerSettings.stripEngineCode = optimizer.stripUnusedCode;
            }
            
            if (optimizer.compressTextures)
            {
                Debug.Log("Build Optimizer: Texture compression enabled");
            }
        }
        
        Debug.Log($"Build Optimizer: Target platform - {report.summary.platform}");
        Debug.Log($"Build Optimizer: Build options - {report.summary.options}");
    }
}
#endif