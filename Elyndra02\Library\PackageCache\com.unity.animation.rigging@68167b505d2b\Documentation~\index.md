
# Animation Rigging

Use Animation Rigging to create and organize **animation rigs**, or sets of constraints for adding procedural motion to animated objects. Examples include:
* _Deformation rigs_ to control secondary animation of character armor, props, and accessories.
* _World Interaction rigs_ (sets of IK and Aim constraints) to make interactive adjustments for targeting or for correcting animation compression artifacts.

This documentation describes how to set up a simple Animation Rig for use in a variety of workflows. It also includes detailed information on the constraints included in the Animation Rigging package.

* The [Rigging Workflow](RiggingWorkflow.md) topic is the best place to get started. It describes the main components necessary for defining and using an animation rig. It includes suggestions on additional components and constraints that you can define for props, accessories, or other rig controls. 
* The [Animation Rigging Menu](AnimationRiggingMenu.md) topic describes utilities that help with the rigging workflow.
* The [Bidirectional Motion Transfer](BidirectionalMotionTransfer.md) topic describes an additional workflow option for transferring motion back and forth between an animation rig and a skeleton.
* The [Constraint Components](ConstraintComponents.md) topic contains detailed information on all of the built-in Constraint components.

# Technical details

## Requirements

This version of Animation Rigging is compatible with the following versions of the Unity Editor:

* 2023.2 and later

## Document revision history

|Date|Reason|
|---|---|
|Jan 21, 2020|Editing pass and restructure of package documentation|
|Nov 12, 2019|Added Bidirectional Motion Transfer workflow and updated images|
|Nov 23, 2018|First draft of package documentation|
