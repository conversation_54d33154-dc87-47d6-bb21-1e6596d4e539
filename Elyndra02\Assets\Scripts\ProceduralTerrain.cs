using UnityEngine;

public class ProceduralTerrain : MonoBehaviour
{
    [Header("Terrain Settings")]
    public int terrainWidth = 256;
    public int terrainHeight = 256;
    public float terrainScale = 20f;
    public float heightScale = 10f;
    
    [Header("Noise Settings")]
    public float noiseScale = 0.1f;
    public int octaves = 4;
    public float persistence = 0.5f;
    public float lacunarity = 2f;
    public Vector2 offset = Vector2.zero;
    
    [Header("Generation")]
    public bool generateOnStart = true;
    public bool autoUpdate = false;
    
    private Terrain terrain;
    private TerrainData terrainData;
    
    private void Start()
    {
        if (generateOnStart)
        {
            GenerateTerrain();
        }
    }
    
    public void GenerateTerrain()
    {
        Debug.Log("Generating procedural terrain...");
        
        // Create terrain if it doesn't exist
        if (terrain == null)
        {
            CreateTerrain();
        }
        
        // Generate height map
        float[,] heightMap = GenerateHeightMap();
        
        // Apply height map to terrain
        terrainData.SetHeights(0, 0, heightMap);
        
        Debug.Log($"Terrain generated: {terrainWidth}x{terrainHeight}");
    }
    
    private void CreateTerrain()
    {
        // Create terrain data
        terrainData = new TerrainData();
        terrainData.heightmapResolution = terrainWidth + 1;
        terrainData.size = new Vector3(terrainWidth * terrainScale, heightScale, terrainHeight * terrainScale);
        
        // Create terrain GameObject
        GameObject terrainObject = Terrain.CreateTerrainGameObject(terrainData);
        terrainObject.transform.parent = transform;
        terrainObject.name = "Procedural Terrain";
        
        terrain = terrainObject.GetComponent<Terrain>();
        
        Debug.Log("Terrain object created");
    }
    
    private float[,] GenerateHeightMap()
    {
        float[,] heightMap = new float[terrainWidth, terrainHeight];
        
        for (int x = 0; x < terrainWidth; x++)
        {
            for (int y = 0; y < terrainHeight; y++)
            {
                float height = GenerateHeight(x, y);
                heightMap[x, y] = height;
            }
        }
        
        return heightMap;
    }
    
    private float GenerateHeight(int x, int y)
    {
        float amplitude = 1f;
        float frequency = noiseScale;
        float height = 0f;
        
        for (int i = 0; i < octaves; i++)
        {
            float sampleX = (x + offset.x) * frequency;
            float sampleY = (y + offset.y) * frequency;
            
            float noiseValue = Mathf.PerlinNoise(sampleX, sampleY);
            height += noiseValue * amplitude;
            
            amplitude *= persistence;
            frequency *= lacunarity;
        }
        
        return Mathf.Clamp01(height);
    }
    
    public void RandomizeOffset()
    {
        offset = new Vector2(Random.Range(-1000f, 1000f), Random.Range(-1000f, 1000f));
        if (autoUpdate)
        {
            GenerateTerrain();
        }
    }
    
    public void SetNoiseParameters(float scale, int oct, float pers, float lac)
    {
        noiseScale = Mathf.Max(0.001f, scale);
        octaves = Mathf.Max(1, oct);
        persistence = Mathf.Clamp01(pers);
        lacunarity = Mathf.Max(1f, lac);
        
        if (autoUpdate)
        {
            GenerateTerrain();
        }
    }
    
    public void SetTerrainSize(int width, int height, float scale, float hScale)
    {
        terrainWidth = Mathf.Max(1, width);
        terrainHeight = Mathf.Max(1, height);
        terrainScale = Mathf.Max(0.1f, scale);
        heightScale = Mathf.Max(0.1f, hScale);
        
        // Recreate terrain with new size
        if (terrain != null)
        {
            DestroyImmediate(terrain.gameObject);
            terrain = null;
        }
        
        if (autoUpdate)
        {
            GenerateTerrain();
        }
    }
    
    public float GetHeightAtPosition(Vector3 worldPosition)
    {
        if (terrain == null) return 0f;
        
        Vector3 localPos = worldPosition - terrain.transform.position;
        Vector3 normalizedPos = new Vector3(
            localPos.x / terrainData.size.x,
            0,
            localPos.z / terrainData.size.z
        );
        
        return terrainData.GetInterpolatedHeight(normalizedPos.x, normalizedPos.z);
    }
    
    private void OnValidate()
    {
        if (terrainWidth < 1) terrainWidth = 1;
        if (terrainHeight < 1) terrainHeight = 1;
        if (terrainScale < 0.1f) terrainScale = 0.1f;
        if (heightScale < 0.1f) heightScale = 0.1f;
        if (noiseScale < 0.001f) noiseScale = 0.001f;
        if (octaves < 1) octaves = 1;
        if (lacunarity < 1f) lacunarity = 1f;
        
        persistence = Mathf.Clamp01(persistence);
    }
}