using UnityEngine;
using System.Collections.Generic;

public class AdvancedPhysicsController : MonoBehaviour
{
    [Header("Physics Settings")]
    public float gravity = -9.81f;
    public int solverIterations = 6;
    public int solverVelocityIterations = 1;
    
    [Header("Collision Detection")]
    public bool enableContinuousCollision = true;
    public float bounceThreshold = 2f;
    public float sleepThreshold = 0.005f;
    
    [Header("Fluid Simulation")]
    public bool enableFluidSimulation = false;
    public float fluidDensity = 1000f;
    public float viscosity = 0.01f;
    
    [Header("Soft Body Physics")]
    public bool enableSoftBodies = false;
    public int softBodyIterations = 4;
    
    private List<Rigidbody> managedRigidbodies;
    private List<Joint> managedJoints;
    
    void Start()
    {
        InitializePhysicsSystem();
        ConfigurePhysicsSettings();
    }
    
    void InitializePhysicsSystem()
    {
        managedRigidbodies = new List<Rigidbody>();
        managedJoints = new List<Joint>();
        
        // Find all rigidbodies in scene
        Rigidbody[] allRigidbodies = FindObjectsByType<Rigidbody>(FindObjectsSortMode.None);
        managedRigidbodies.AddRange(allRigidbodies);
        
        // Find all joints in scene
        Joint[] allJoints = FindObjectsByType<Joint>(FindObjectsSortMode.None);
        managedJoints.AddRange(allJoints);
        
        Debug.Log($"Advanced Physics initialized: {managedRigidbodies.Count} rigidbodies, {managedJoints.Count} joints");
    }
    
    void ConfigurePhysicsSettings()
    {
        // Configure global physics settings
        Physics.gravity = new Vector3(0, gravity, 0);
        Physics.defaultSolverIterations = solverIterations;
        Physics.defaultSolverVelocityIterations = solverVelocityIterations;
        
        Physics.bounceThreshold = bounceThreshold;
        Physics.sleepThreshold = sleepThreshold;
        
        // Configure collision detection
        if (enableContinuousCollision)
        {
            foreach (var rb in managedRigidbodies)
            {
                if (rb != null)
                {
                    rb.collisionDetectionMode = CollisionDetectionMode.ContinuousDynamic;
                }
            }
        }
        
        Debug.Log("Physics settings configured");
    }
    
    public void AddRigidbody(GameObject obj)
    {
        Rigidbody rb = obj.GetComponent<Rigidbody>();
        if (rb == null)
        {
            rb = obj.AddComponent<Rigidbody>();
        }
        
        if (!managedRigidbodies.Contains(rb))
        {
            managedRigidbodies.Add(rb);
            
            if (enableContinuousCollision)
            {
                rb.collisionDetectionMode = CollisionDetectionMode.ContinuousDynamic;
            }
        }
    }
    
    public void CreateSpringJoint(GameObject obj1, GameObject obj2, float spring = 50f, float damper = 5f)
    {
        SpringJoint joint = obj1.AddComponent<SpringJoint>();
        joint.connectedBody = obj2.GetComponent<Rigidbody>();
        joint.spring = spring;
        joint.damper = damper;
        
        managedJoints.Add(joint);
        Debug.Log($"Spring joint created between {obj1.name} and {obj2.name}");
    }
    
    public void ApplyExplosionForce(Vector3 position, float force, float radius)
    {
        foreach (var rb in managedRigidbodies)
        {
            if (rb != null)
            {
                rb.AddExplosionForce(force, position, radius);
            }
        }
        
        Debug.Log($"Explosion force applied at {position} with force {force} and radius {radius}");
    }
    
    public void SetTimeScale(float scale)
    {
        Time.timeScale = Mathf.Clamp(scale, 0f, 10f);
        Time.fixedDeltaTime = 0.02f * Time.timeScale;
        
        Debug.Log($"Time scale set to {scale}");
    }
    
    void FixedUpdate()
    {
        if (enableFluidSimulation)
        {
            SimulateFluidPhysics();
        }
        
        if (enableSoftBodies)
        {
            UpdateSoftBodies();
        }
    }
    
    void SimulateFluidPhysics()
    {
        // Simple fluid simulation
        foreach (var rb in managedRigidbodies)
        {
            if (rb != null && rb.transform.position.y < 0)
            {
                Vector3 buoyancy = Vector3.up * fluidDensity * Physics.gravity.magnitude * rb.mass;
                rb.AddForce(buoyancy);
                
                // Apply drag
                rb.AddForce(-rb.linearVelocity * viscosity);
            }
        }
    }
    
    void UpdateSoftBodies()
    {
        // Placeholder for soft body physics
        // In a real implementation, this would handle soft body constraints
    }
    
    void OnDrawGizmos()
    {
        if (enableFluidSimulation)
        {
            Gizmos.color = Color.blue;
            Gizmos.DrawCube(Vector3.zero, new Vector3(100, 0.1f, 100));
        }
    }
}