# Constraint Components

The Animation Rigging package includes the following predefined constraints that you can use to define your Animation rigs:

- [Blend Constraint](./constraints/BlendConstraint.md)
- [Chain IK Constraint](./constraints/ChainIKConstraint.md)
- [Damped Transform](./constraints/DampedTransform.md)
- [Multi-Aim Constraint](./constraints/MultiAimConstraint.md)
- [Multi-Parent Constraint](./constraints/MultiParentConstraint.md)
- [Multi-Position Constraint](./constraints/MultiPositionConstraint.md)
- [Multi-Referential Constraint](./constraints/MultiReferentialConstraint.md)
- [Multi-Rotation Constraint](./constraints/MultiRotationConstraint.md)
- [Override Transform](./constraints/OverrideTransform.md)
- [Twist Chain Constraint](./constraints/TwistChainConstraint.md)
- [Twist Correction](./constraints/TwistCorrection.md)
- [Two Bone IK Constraint](./constraints/TwoBoneIKConstraint.md)

The constraints in the Animation Rigging package are based on the C# Animation Jobs API.
