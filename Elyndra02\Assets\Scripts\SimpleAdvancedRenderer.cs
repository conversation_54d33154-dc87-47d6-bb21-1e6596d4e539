using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

public class SimpleAdvancedRenderer : MonoBehaviour
{
    [Header("Rendering Settings")]
    public Camera targetCamera;
    public bool enablePostProcessing = true;
    public float renderScale = 1.0f;
    
    [Header("Lighting")]
    public Light mainLight;
    public UnityEngine.ShadowQuality shadowQuality = UnityEngine.ShadowQuality.All;
    public float shadowDistance = 50f;
    
    private UniversalRenderPipelineAsset urpAsset;
    
    void Start()
    {
        InitializeRenderer();
    }
    
    void InitializeRenderer()
    {
        urpAsset = GraphicsSettings.defaultRenderPipeline as UniversalRenderPipelineAsset;
        
        if (targetCamera == null)
            targetCamera = Camera.main;
            
        if (mainLight == null)
            mainLight = FindFirstObjectByType<Light>();
            
        ConfigureSettings();
        Debug.Log("Advanced Renderer Initialized");
    }
    
    void ConfigureSettings()
    {
        if (urpAsset != null)
        {
            urpAsset.renderScale = renderScale;
        }
        
        QualitySettings.shadows = shadowQuality;
        QualitySettings.shadowDistance = shadowDistance;
        
        if (targetCamera != null)
        {
            var cameraData = targetCamera.GetComponent<UniversalAdditionalCameraData>();
            if (cameraData != null)
            {
                cameraData.renderPostProcessing = enablePostProcessing;
            }
        }
    }
    
    public void SetRenderScale(float scale)
    {
        renderScale = Mathf.Clamp(scale, 0.1f, 2f);
        if (urpAsset != null)
        {
            urpAsset.renderScale = renderScale;
        }
    }
}