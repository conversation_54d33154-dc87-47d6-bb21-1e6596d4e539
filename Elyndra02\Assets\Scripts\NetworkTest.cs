using UnityEngine;

public class NetworkTest : MonoBehaviour
{
    public string serverIP = "127.0.0.1";
    public int port = 7777;
    public bool isServer = false;
    
    void Start()
    {
        Debug.Log("Network Test Script Created");
        
        if (isServer)
        {
            Debug.Log("Starting as Server");
        }
        else
        {
            Debug.Log("Starting as Client");
        }
    }
    
    public void StartServer()
    {
        Debug.Log($"Server would start on port {port}");
    }
    
    public void ConnectToServer()
    {
        Debug.Log($"Would connect to {serverIP}:{port}");
    }
}