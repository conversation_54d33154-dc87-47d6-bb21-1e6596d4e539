using UnityEngine;
using UnityEngine.UIElements;

public class UIToolkitRuntimeManager : MonoBehaviour
{
    [Header("UI Configuration")]
    public UIDocument uiDocument;
    public string mainContainerName = "main-container";
    
    private VisualElement rootElement;
    private VisualElement mainContainer;
    
    void Start()
    {
        SetupUIToolkit();
        CreateDynamicUI();
    }
    
    void SetupUIToolkit()
    {
        if (uiDocument == null)
        {
            GameObject uiObj = new GameObject("UIDocument");
            uiDocument = uiObj.AddComponent<UIDocument>();
        }
        
        rootElement = uiDocument.rootVisualElement;
        
        // Create main container if it doesn't exist
        mainContainer = rootElement.Q<VisualElement>(mainContainerName);
        if (mainContainer == null)
        {
            mainContainer = new VisualElement();
            mainContainer.name = mainContainerName;
            mainContainer.style.width = Length.Percent(100);
            mainContainer.style.height = Length.Percent(100);
            rootElement.Add(mainContainer);
        }
        
        Debug.Log("UI Toolkit Runtime Manager initialized");
    }
    
    void CreateDynamicUI()
    {
        // Create a title label
        Label titleLabel = new Label("Advanced UI System");
        titleLabel.style.fontSize = 24;
        titleLabel.style.color = Color.white;
        titleLabel.style.unityTextAlign = TextAnchor.MiddleCenter;
        titleLabel.style.marginBottom = 20;
        mainContainer.Add(titleLabel);
        
        // Create buttons container
        VisualElement buttonContainer = new VisualElement();
        buttonContainer.style.flexDirection = FlexDirection.Row;
        buttonContainer.style.justifyContent = Justify.Center;
        buttonContainer.style.marginBottom = 20;
        
        // Create action buttons
        Button startButton = new Button(() => OnStartClicked()) { text = "Start" };
        Button stopButton = new Button(() => OnStopClicked()) { text = "Stop" };
        Button resetButton = new Button(() => OnResetClicked()) { text = "Reset" };
        
        buttonContainer.Add(startButton);
        buttonContainer.Add(stopButton);
        buttonContainer.Add(resetButton);
        mainContainer.Add(buttonContainer);
        
        // Create status display
        Label statusLabel = new Label("Status: Ready");
        statusLabel.name = "status-label";
        statusLabel.style.color = Color.green;
        statusLabel.style.unityTextAlign = TextAnchor.MiddleCenter;
        mainContainer.Add(statusLabel);
        
        Debug.Log("Dynamic UI elements created");
    }
    
    void OnStartClicked()
    {
        UpdateStatus("Running", Color.yellow);
        Debug.Log("Start button clicked");
    }
    
    void OnStopClicked()
    {
        UpdateStatus("Stopped", Color.red);
        Debug.Log("Stop button clicked");
    }
    
    void OnResetClicked()
    {
        UpdateStatus("Ready", Color.green);
        Debug.Log("Reset button clicked");
    }
    
    void UpdateStatus(string status, Color color)
    {
        Label statusLabel = rootElement.Q<Label>("status-label");
        if (statusLabel != null)
        {
            statusLabel.text = $"Status: {status}";
            statusLabel.style.color = color;
        }
    }
    
    public void AddCustomButton(string text, System.Action callback)
    {
        Button customButton = new Button(callback) { text = text };
        mainContainer.Add(customButton);
    }
    
    public void ClearUI()
    {
        mainContainer.Clear();
    }
}