using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Audio;

public class DynamicMusicController : MonoBehaviour
{
    [Header("Dynamic Music Settings")]
    public float fadeDuration = 2.0f;
    public string transitionType = "CrossFade";
    public bool autoPlayOnStart = true;
    public int maxConcurrentTracks = 4;
    
    [Header("Runtime Control")]
    public bool isPlaying = false;
    public int currentTrackIndex = 0;
    public float masterVolume = 1.0f;
    
    private AudioSource[] audioSources;
    private List<AudioClip> musicTracks = new List<AudioClip>();
    private Dictionary<string, float> trackVolumes = new Dictionary<string, float>();
    private Dictionary<string, bool> trackLoopSettings = new Dictionary<string, bool>();
    private Dictionary<string, Vector2> trackCrossfadePoints = new Dictionary<string, Vector2>();
    
    private int activeSourceIndex = 0;
    private bool isTransitioning = false;
    private string managerName;
    
    void Start()
    {
        managerName = gameObject.name;
        LoadConfiguration();
        InitializeAudioSources();
        LoadMusicTracks();
        
        if (autoPlayOnStart && musicTracks.Count > 0)
        {
            PlayTrack(0);
        }
    }
    
    void LoadConfiguration()
    {
        transitionType = PlayerPrefs.GetString($"DynamicMusic.{managerName}.TransitionType", "CrossFade");
        fadeDuration = PlayerPrefs.GetFloat($"DynamicMusic.{managerName}.FadeDuration", 2.0f);
        maxConcurrentTracks = PlayerPrefs.GetInt($"DynamicMusic.{managerName}.MaxSources", 4);
    }
    
    void InitializeAudioSources()
    {
        audioSources = GetComponents<AudioSource>();
        
        foreach (AudioSource source in audioSources)
        {
            source.volume = 0f;
            source.playOnAwake = false;
        }
    }
    
    void LoadMusicTracks()
    {
        int trackCount = PlayerPrefs.GetInt($"DynamicMusic.{managerName}.TrackCount", 0);
        
        for (int i = 0; i < trackCount; i++)
        {
            string trackName = PlayerPrefs.GetString($"DynamicMusic.{managerName}.Track{i}", "");
            if (!string.IsNullOrEmpty(trackName))
            {
                // Find the AudioClip by name (simplified approach)
                AudioClip[] allClips = Resources.FindObjectsOfTypeAll<AudioClip>();
                AudioClip foundClip = System.Array.Find(allClips, clip => clip.name == trackName);
                
                if (foundClip != null)
                {
                    musicTracks.Add(foundClip);
                    
                    // Load track-specific settings
                    float volume = PlayerPrefs.GetFloat($"DynamicMusic.{managerName}.{trackName}.Volume", 1.0f);
                    bool loop = PlayerPrefs.GetInt($"DynamicMusic.{managerName}.{trackName}.Loop", 1) == 1;
                    float fadeInPoint = PlayerPrefs.GetFloat($"DynamicMusic.{managerName}.{trackName}.FadeInPoint", 0f);
                    float fadeOutPoint = PlayerPrefs.GetFloat($"DynamicMusic.{managerName}.{trackName}.FadeOutPoint", 1f);
                    
                    trackVolumes[trackName] = volume;
                    trackLoopSettings[trackName] = loop;
                    trackCrossfadePoints[trackName] = new Vector2(fadeInPoint, fadeOutPoint);
                }
            }
        }
    }
    
    public void PlayTrack(int trackIndex)
    {
        if (trackIndex < 0 || trackIndex >= musicTracks.Count) return;
        
        if (isTransitioning) return;
        
        AudioClip targetClip = musicTracks[trackIndex];
        
        if (isPlaying && transitionType == "CrossFade")
        {
            StartCoroutine(CrossFadeToTrack(trackIndex));
        }
        else
        {
            DirectPlayTrack(trackIndex);
        }
    }
    
    void DirectPlayTrack(int trackIndex)
    {
        StopAllTracks();
        
        AudioClip clip = musicTracks[trackIndex];
        AudioSource targetSource = audioSources[activeSourceIndex];
        
        targetSource.clip = clip;
        targetSource.loop = trackLoopSettings.ContainsKey(clip.name) ? trackLoopSettings[clip.name] : true;
        
        float targetVolume = trackVolumes.ContainsKey(clip.name) ? trackVolumes[clip.name] : 1.0f;
        targetSource.volume = targetVolume * masterVolume;
        
        targetSource.Play();
        
        currentTrackIndex = trackIndex;
        isPlaying = true;
    }
    
    IEnumerator CrossFadeToTrack(int trackIndex)
    {
        isTransitioning = true;
        
        AudioClip newClip = musicTracks[trackIndex];
        AudioSource currentSource = audioSources[activeSourceIndex];
        AudioSource newSource = audioSources[(activeSourceIndex + 1) % audioSources.Length];
        
        // Setup new source
        newSource.clip = newClip;
        newSource.loop = trackLoopSettings.ContainsKey(newClip.name) ? trackLoopSettings[newClip.name] : true;
        newSource.volume = 0f;
        newSource.Play();
        
        // Get target volumes
        float currentTargetVolume = trackVolumes.ContainsKey(currentSource.clip.name) ? 
            trackVolumes[currentSource.clip.name] : 1.0f;
        float newTargetVolume = trackVolumes.ContainsKey(newClip.name) ? 
            trackVolumes[newClip.name] : 1.0f;
        
        // Crossfade
        float elapsedTime = 0f;
        float currentStartVolume = currentSource.volume;
        
        while (elapsedTime < fadeDuration)
        {
            elapsedTime += Time.deltaTime;
            float t = elapsedTime / fadeDuration;
            
            currentSource.volume = Mathf.Lerp(currentStartVolume, 0f, t);
            newSource.volume = Mathf.Lerp(0f, newTargetVolume * masterVolume, t);
            
            yield return null;
        }
        
        // Finalize transition
        currentSource.Stop();
        currentSource.volume = 0f;
        newSource.volume = newTargetVolume * masterVolume;
        
        activeSourceIndex = (activeSourceIndex + 1) % audioSources.Length;
        currentTrackIndex = trackIndex;
        isTransitioning = false;
    }
    
    public void StopAllTracks()
    {
        foreach (AudioSource source in audioSources)
        {
            source.Stop();
            source.volume = 0f;
        }
        isPlaying = false;
    }
    
    public void PauseMusic()
    {
        foreach (AudioSource source in audioSources)
        {
            if (source.isPlaying)
            {
                source.Pause();
            }
        }
    }
    
    public void ResumeMusic()
    {
        foreach (AudioSource source in audioSources)
        {
            if (source.clip != null && !source.isPlaying)
            {
                source.UnPause();
            }
        }
    }
    
    public void SetMasterVolume(float volume)
    {
        masterVolume = Mathf.Clamp01(volume);
        
        foreach (AudioSource source in audioSources)
        {
            if (source.isPlaying && source.clip != null)
            {
                float trackVolume = trackVolumes.ContainsKey(source.clip.name) ? 
                    trackVolumes[source.clip.name] : 1.0f;
                source.volume = trackVolume * masterVolume;
            }
        }
    }
    
    public void NextTrack()
    {
        int nextIndex = (currentTrackIndex + 1) % musicTracks.Count;
        PlayTrack(nextIndex);
    }
    
    public void PreviousTrack()
    {
        int prevIndex = (currentTrackIndex - 1 + musicTracks.Count) % musicTracks.Count;
        PlayTrack(prevIndex);
    }
    
    public void PlayRandomTrack()
    {
        if (musicTracks.Count > 1)
        {
            int randomIndex;
            do
            {
                randomIndex = Random.Range(0, musicTracks.Count);
            } while (randomIndex == currentTrackIndex);
            
            PlayTrack(randomIndex);
        }
    }
    
    // Public API for external control
    public int GetCurrentTrackIndex() => currentTrackIndex;
    public int GetTrackCount() => musicTracks.Count;
    public string GetCurrentTrackName() => musicTracks.Count > currentTrackIndex ? musicTracks[currentTrackIndex].name : "";
    public bool IsPlaying() => isPlaying;
    public bool IsTransitioning() => isTransitioning;
}