using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using Newtonsoft.Json;

[System.Serializable]
public class CustomizationPreset
{
    public string presetName;
    public Dictionary<string, int> customizationValues = new Dictionary<string, int>();
}

public class Kaelen_Guardian_CompleteCustomization : MonoBehaviour
{
    [Header("Character Components")]
    public SkinnedMeshRenderer[] bodyRenderers;
    public Renderer[] accessoryRenderers;
    public Animator characterAnimator;

    [Header("Customization Options")]
    [Range(0, 10)] public int armor_color = 0;
    [Range(0, 10)] public int cape_style = 0;
    [Range(0, 10)] public int spear_type = 0;
    [Range(0, 10)] public int helmet = 0;

    [Header("Presets")]
    public List<CustomizationPreset> presets = new List<CustomizationPreset>();
    public string presetsFilePath = "Assets/CharacterPresets.json";

    private Dictionary<string, Material[]> originalMaterials = new Dictionary<string, Material[]>();
    private Dictionary<string, Vector3> originalScales = new Dictionary<string, Vector3>();

    void Start()
    {
        InitializeCustomizationSystem();
        LoadPresets();
        ApplyAllCustomizations();
    }

    void InitializeCustomizationSystem()
    {
        // Auto-find components if not assigned
        if (bodyRenderers == null || bodyRenderers.Length == 0)
            bodyRenderers = GetComponentsInChildren<SkinnedMeshRenderer>();

        if (accessoryRenderers == null || accessoryRenderers.Length == 0)
            accessoryRenderers = GetComponentsInChildren<Renderer>().Where(r => r.name.ToLower().Contains("accessory")).ToArray();

        if (characterAnimator == null)
            characterAnimator = GetComponent<Animator>();

        // Store original materials and scales
        foreach (var renderer in bodyRenderers)
        {
            if (renderer != null)
            {
                originalMaterials[renderer.name] = renderer.materials;
                originalScales[renderer.name] = renderer.transform.localScale;
            }
        }
    }

    public void ApplyCustomization(string option, int value)
    {
        switch (option.ToLower())
        {
            case "armor_color":
                armor_color = value;
                Applyarmor_colorCustomization(value);
                break;
            case "cape_style":
                cape_style = value;
                Applycape_styleCustomization(value);
                break;
            case "spear_type":
                spear_type = value;
                Applyspear_typeCustomization(value);
                break;
            case "helmet":
                helmet = value;
                ApplyhelmetCustomization(value);
                break;
            default:
                Debug.LogWarning($"Unknown customization option: {option}");
                break;
        }
    }

    private void Applyarmor_colorCustomization(int value)
    {
        // Apply color variations
        Color[] colors = { Color.red, Color.blue, Color.green, Color.yellow, Color.magenta, Color.cyan, Color.white, Color.black, new Color(1f, 0.5f, 0f), new Color(0.5f, 0f, 1f) };
        Color targetColor = colors[value % colors.Length];
        foreach (var renderer in bodyRenderers)
        {
            if (renderer != null)
            {
                foreach (var material in renderer.materials)
                {
                    if (material.HasProperty("_Color")) material.color = targetColor;
                    if (material.HasProperty("_BaseColor")) material.SetColor("_BaseColor", targetColor);
                }
            }
        }
    }

    private void Applycape_styleCustomization(int value)
    {
        // Generic customization - modify blend shapes if available
        foreach (var renderer in bodyRenderers)
        {
            if (renderer != null && renderer.sharedMesh != null)
            {
                for (int i = 0; i < renderer.sharedMesh.blendShapeCount; i++)
                {
                    if (renderer.sharedMesh.GetBlendShapeName(i).ToLower().Contains("cape_style"))
                    {
                        renderer.SetBlendShapeWeight(i, value * 10f);
                    }
                }
            }
        }
    }

    private void Applyspear_typeCustomization(int value)
    {
        // Generic customization - modify blend shapes if available
        foreach (var renderer in bodyRenderers)
        {
            if (renderer != null && renderer.sharedMesh != null)
            {
                for (int i = 0; i < renderer.sharedMesh.blendShapeCount; i++)
                {
                    if (renderer.sharedMesh.GetBlendShapeName(i).ToLower().Contains("spear_type"))
                    {
                        renderer.SetBlendShapeWeight(i, value * 10f);
                    }
                }
            }
        }
    }

    private void ApplyhelmetCustomization(int value)
    {
        // Generic customization - modify blend shapes if available
        foreach (var renderer in bodyRenderers)
        {
            if (renderer != null && renderer.sharedMesh != null)
            {
                for (int i = 0; i < renderer.sharedMesh.blendShapeCount; i++)
                {
                    if (renderer.sharedMesh.GetBlendShapeName(i).ToLower().Contains("helmet"))
                    {
                        renderer.SetBlendShapeWeight(i, value * 10f);
                    }
                }
            }
        }
    }

    public void ApplyAllCustomizations()
    {
        Applyarmor_colorCustomization(armor_color);
        Applycape_styleCustomization(cape_style);
        Applyspear_typeCustomization(spear_type);
        ApplyhelmetCustomization(helmet);
    }

    public void SavePreset(string presetName)
    {
        var preset = new CustomizationPreset { presetName = presetName };
        preset.customizationValues["armor_color"] = armor_color;
        preset.customizationValues["cape_style"] = cape_style;
        preset.customizationValues["spear_type"] = spear_type;
        preset.customizationValues["helmet"] = helmet;
        presets.Add(preset);
        SavePresets();
    }

    public void LoadPreset(string presetName)
    {
        var preset = presets.FirstOrDefault(p => p.presetName == presetName);
        if (preset != null)
        {
            foreach (var kvp in preset.customizationValues)
            {
                ApplyCustomization(kvp.Key, kvp.Value);
            }
        }
    }

    private void SavePresets()
    {
        try
        {
            string json = JsonConvert.SerializeObject(presets, Formatting.Indented);
            File.WriteAllText(presetsFilePath, json);
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"Failed to save presets: {ex.Message}");
        }
    }

    private void LoadPresets()
    {
        try
        {
            if (File.Exists(presetsFilePath))
            {
                string json = File.ReadAllText(presetsFilePath);
                presets = JsonConvert.DeserializeObject<List<CustomizationPreset>>(json) ?? new List<CustomizationPreset>();
            }
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"Failed to load presets: {ex.Message}");
            presets = new List<CustomizationPreset>();
        }
    }
}
