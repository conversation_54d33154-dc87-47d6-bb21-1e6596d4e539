using UnityEngine;
using System.Collections.Generic;

public class SimpleNetworkManager : MonoBehaviour
{
    [Header("Network Settings")]
    public bool isServer = false;
    public string serverIP = "127.0.0.1";
    public int port = 7777;
    public int maxPlayers = 4;
    
    [Head<PERSON>("Player Management")]
    public GameObject playerPrefab;
    
    private Dictionary<int, GameObject> players = new Dictionary<int, GameObject>();
    private bool isConnected = false;
    private int localPlayerId = 0;
    
    void Start()
    {
        InitNetwork();
    }
    
    void InitNetwork()
    {
        Debug.Log($"Simple Network Manager initialized!");
        Debug.Log($"Mode: {(isServer ? "Server" : "Client")}");
        Debug.Log($"Address: {serverIP}:{port}");
        
        if (isServer)
        {
            StartServer();
        }
        else
        {
            ConnectToServer();
        }
    }
    
    void StartServer()
    {
        isConnected = true;
        Debug.Log($"Server started on port {port}");
        
        // Spawn local player
        SpawnPlayer(0, true);
    }
    
    void ConnectToServer()
    {
        isConnected = true;
        Debug.Log($"Connected to server at {serverIP}:{port}");
        
        // Spawn local player
        SpawnPlayer(1, true);
    }
    
    void SpawnPlayer(int playerId, bool isLocal)
    {
        Vector3 spawnPos = new Vector3(playerId * 3f, 0, 0);
        GameObject player = CreatePlayer(spawnPos, playerId);
        
        players[playerId] = player;
        
        if (isLocal)
        {
            localPlayerId = playerId;
            player.AddComponent<SimplePlayerController>();
        }
        
        Debug.Log($"Player {playerId} spawned");
    }
    
    GameObject CreatePlayer(Vector3 position, int playerId)
    {
        GameObject player;
        
        if (playerPrefab != null)
        {
            player = Instantiate(playerPrefab, position, Quaternion.identity);
        }
        else
        {
            player = GameObject.CreatePrimitive(PrimitiveType.Capsule);
            player.transform.position = position;
        }
        
        player.name = $"Player_{playerId}";
        
        // Set color
        Renderer renderer = player.GetComponent<Renderer>();
        if (renderer != null)
        {
            Color[] colors = { Color.red, Color.blue, Color.green, Color.yellow };
            renderer.material.color = colors[playerId % colors.Length];
        }
        
        return player;
    }
    
    public void SendPlayerUpdate(int playerId, Vector3 position)
    {
        Debug.Log($"Player {playerId} moved to {position}");
        // In real implementation, send over network
    }
    
    public int GetPlayerCount()
    {
        return players.Count;
    }
    
    public bool IsConnected()
    {
        return isConnected;
    }
}

public class SimplePlayerController : MonoBehaviour
{
    private SimpleNetworkManager networkManager;
    
    void Start()
    {
        networkManager = UnityEngine.Object.FindFirstObjectByType<SimpleNetworkManager>();
    }
    
    void Update()
    {
        Vector3 movement = Vector3.zero;
        
        if (Input.GetKey(KeyCode.W)) movement.z += 1;
        if (Input.GetKey(KeyCode.S)) movement.z -= 1;
        if (Input.GetKey(KeyCode.A)) movement.x -= 1;
        if (Input.GetKey(KeyCode.D)) movement.x += 1;
        
        if (movement != Vector3.zero)
        {
            transform.position += movement * 5f * Time.deltaTime;
            
            if (networkManager != null)
            {
                networkManager.SendPlayerUpdate(0, transform.position);
            }
        }
    }
}