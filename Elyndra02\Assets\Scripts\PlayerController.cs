using UnityEngine;

namespace GameCore
{
    public class PlayerController : <PERSON>o<PERSON><PERSON><PERSON><PERSON>
    {
        [Header("Movement Settings")]
        public float moveSpeed = 5f;
        public float jumpForce = 10f;
        
        [Header("Components")]
        public Rigidbody rb;
        public Transform groundCheck;
        
        private bool isGrounded;
        private Vector3 moveDirection;
        
        void Start()
        {
            if (rb == null)
                rb = GetComponent<Rigidbody>();
        }
        
        void Update()
        {
            HandleInput();
            CheckGrounded();
        }
        
        void FixedUpdate()
        {
            MovePlayer();
        }
        
        private void HandleInput()
        {
            float horizontal = Input.GetAxis("Horizontal");
            float vertical = Input.GetAxis("Vertical");
            
            moveDirection = new Vector3(horizontal, 0, vertical).normalized;
            
            if (Input.GetButtonDown("Jump") && isGrounded)
            {
                Jump();
            }
        }
        
        private void MovePlayer()
        {
            Vector3 movement = moveDirection * moveSpeed;
            movement.y = rb.linearVelocity.y;
            rb.linearVelocity = movement;
        }
        
        private void Jump()
        {
            rb.AddForce(Vector3.up * jumpForce, ForceMode.Impulse);
        }
        
        private void CheckGrounded()
        {
            isGrounded = Physics.CheckSphere(groundCheck.position, 0.1f);
        }
    }
}