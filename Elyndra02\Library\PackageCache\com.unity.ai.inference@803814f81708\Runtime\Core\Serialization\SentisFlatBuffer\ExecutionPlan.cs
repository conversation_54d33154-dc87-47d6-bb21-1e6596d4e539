// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>
#define ENABLE_SPAN_T
#define UNSAFE_BYTEBUFFER
#define BYTEBUFFER_NO_BOUNDS_CHECK

namespace SentisFlatBuffer
{

using global::System;
using global::System.Collections.Generic;
using global::Unity.InferenceEngine.Google.FlatBuffers;

struct ExecutionPlan : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_5_26(); }
  public static ExecutionPlan GetRootAsExecutionPlan(ByteBuffer _bb) { return GetRootAsExecutionPlan(_bb, new ExecutionPlan()); }
  public static ExecutionPlan GetRootAsExecutionPlan(ByteBuffer _bb, ExecutionPlan obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public ExecutionPlan __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public string Name { get { int o = __p.__offset(4); return o != 0 ? __p.__string(o + __p.bb_pos) : null; } }
#if ENABLE_SPAN_T
  public Span<byte> GetNameBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetNameBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetNameArray() { return __p.__vector_as_array<byte>(4); }
  public SentisFlatBuffer.EValue? Values(int j) { int o = __p.__offset(6); return o != 0 ? (SentisFlatBuffer.EValue?)(new SentisFlatBuffer.EValue()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ValuesLength { get { int o = __p.__offset(6); return o != 0 ? __p.__vector_len(o) : 0; } }
  public int Inputs(int j) { int o = __p.__offset(8); return o != 0 ? __p.bb.GetInt(__p.__vector(o) + j * 4) : (int)0; }
  public int InputsLength { get { int o = __p.__offset(8); return o != 0 ? __p.__vector_len(o) : 0; } }
#if ENABLE_SPAN_T
  public Span<int> GetInputsBytes() { return __p.__vector_as_span<int>(8, 4); }
#else
  public ArraySegment<byte>? GetInputsBytes() { return __p.__vector_as_arraysegment(8); }
#endif
  public int[] GetInputsArray() { return __p.__vector_as_array<int>(8); }
  public string InputsName(int j) { int o = __p.__offset(10); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int InputsNameLength { get { int o = __p.__offset(10); return o != 0 ? __p.__vector_len(o) : 0; } }
  public int Outputs(int j) { int o = __p.__offset(12); return o != 0 ? __p.bb.GetInt(__p.__vector(o) + j * 4) : (int)0; }
  public int OutputsLength { get { int o = __p.__offset(12); return o != 0 ? __p.__vector_len(o) : 0; } }
#if ENABLE_SPAN_T
  public Span<int> GetOutputsBytes() { return __p.__vector_as_span<int>(12, 4); }
#else
  public ArraySegment<byte>? GetOutputsBytes() { return __p.__vector_as_arraysegment(12); }
#endif
  public int[] GetOutputsArray() { return __p.__vector_as_array<int>(12); }
  public string OutputsName(int j) { int o = __p.__offset(14); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int OutputsNameLength { get { int o = __p.__offset(14); return o != 0 ? __p.__vector_len(o) : 0; } }
  public SentisFlatBuffer.Chain? Chains(int j) { int o = __p.__offset(16); return o != 0 ? (SentisFlatBuffer.Chain?)(new SentisFlatBuffer.Chain()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int ChainsLength { get { int o = __p.__offset(16); return o != 0 ? __p.__vector_len(o) : 0; } }
  public SentisFlatBuffer.Operator? Operators(int j) { int o = __p.__offset(18); return o != 0 ? (SentisFlatBuffer.Operator?)(new SentisFlatBuffer.Operator()).__assign(__p.__indirect(__p.__vector(o) + j * 4), __p.bb) : null; }
  public int OperatorsLength { get { int o = __p.__offset(18); return o != 0 ? __p.__vector_len(o) : 0; } }
  public SentisFlatBuffer.BackendPartitioning? BackendPartitioning { get { int o = __p.__offset(20); return o != 0 ? (SentisFlatBuffer.BackendPartitioning?)(new SentisFlatBuffer.BackendPartitioning()).__assign(__p.__indirect(o + __p.bb_pos), __p.bb) : null; } }
  public string SymbolicDimNames(int j) { int o = __p.__offset(22); return o != 0 ? __p.__string(__p.__vector(o) + j * 4) : null; }
  public int SymbolicDimNamesLength { get { int o = __p.__offset(22); return o != 0 ? __p.__vector_len(o) : 0; } }

  public static Offset<SentisFlatBuffer.ExecutionPlan> CreateExecutionPlan(FlatBufferBuilder builder,
      StringOffset nameOffset = default(StringOffset),
      VectorOffset valuesOffset = default(VectorOffset),
      VectorOffset inputsOffset = default(VectorOffset),
      VectorOffset inputs_nameOffset = default(VectorOffset),
      VectorOffset outputsOffset = default(VectorOffset),
      VectorOffset outputs_nameOffset = default(VectorOffset),
      VectorOffset chainsOffset = default(VectorOffset),
      VectorOffset operatorsOffset = default(VectorOffset),
      Offset<SentisFlatBuffer.BackendPartitioning> backend_partitioningOffset = default(Offset<SentisFlatBuffer.BackendPartitioning>),
      VectorOffset symbolic_dim_namesOffset = default(VectorOffset)) {
    builder.StartTable(10);
    ExecutionPlan.AddSymbolicDimNames(builder, symbolic_dim_namesOffset);
    ExecutionPlan.AddBackendPartitioning(builder, backend_partitioningOffset);
    ExecutionPlan.AddOperators(builder, operatorsOffset);
    ExecutionPlan.AddChains(builder, chainsOffset);
    ExecutionPlan.AddOutputsName(builder, outputs_nameOffset);
    ExecutionPlan.AddOutputs(builder, outputsOffset);
    ExecutionPlan.AddInputsName(builder, inputs_nameOffset);
    ExecutionPlan.AddInputs(builder, inputsOffset);
    ExecutionPlan.AddValues(builder, valuesOffset);
    ExecutionPlan.AddName(builder, nameOffset);
    return ExecutionPlan.EndExecutionPlan(builder);
  }

  public static void StartExecutionPlan(FlatBufferBuilder builder) { builder.StartTable(10); }
  public static void AddName(FlatBufferBuilder builder, StringOffset nameOffset) { builder.AddOffset(0, nameOffset.Value, 0); }
  public static void AddValues(FlatBufferBuilder builder, VectorOffset valuesOffset) { builder.AddOffset(1, valuesOffset.Value, 0); }
  public static VectorOffset CreateValuesVector(FlatBufferBuilder builder, Offset<SentisFlatBuffer.EValue>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, Offset<SentisFlatBuffer.EValue>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<SentisFlatBuffer.EValue>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateValuesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<SentisFlatBuffer.EValue>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartValuesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddInputs(FlatBufferBuilder builder, VectorOffset inputsOffset) { builder.AddOffset(2, inputsOffset.Value, 0); }
  public static VectorOffset CreateInputsVector(FlatBufferBuilder builder, int[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddInt(data[i]); return builder.EndVector(); }
  public static VectorOffset CreateInputsVectorBlock(FlatBufferBuilder builder, int[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateInputsVectorBlock(FlatBufferBuilder builder, ArraySegment<int> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateInputsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<int>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartInputsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddInputsName(FlatBufferBuilder builder, VectorOffset inputsNameOffset) { builder.AddOffset(3, inputsNameOffset.Value, 0); }
  public static VectorOffset CreateInputsNameVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateInputsNameVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateInputsNameVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateInputsNameVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartInputsNameVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddOutputs(FlatBufferBuilder builder, VectorOffset outputsOffset) { builder.AddOffset(4, outputsOffset.Value, 0); }
  public static VectorOffset CreateOutputsVector(FlatBufferBuilder builder, int[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddInt(data[i]); return builder.EndVector(); }
  public static VectorOffset CreateOutputsVectorBlock(FlatBufferBuilder builder, int[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateOutputsVectorBlock(FlatBufferBuilder builder, ArraySegment<int> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateOutputsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<int>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartOutputsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddOutputsName(FlatBufferBuilder builder, VectorOffset outputsNameOffset) { builder.AddOffset(5, outputsNameOffset.Value, 0); }
  public static VectorOffset CreateOutputsNameVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateOutputsNameVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateOutputsNameVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateOutputsNameVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartOutputsNameVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddChains(FlatBufferBuilder builder, VectorOffset chainsOffset) { builder.AddOffset(6, chainsOffset.Value, 0); }
  public static VectorOffset CreateChainsVector(FlatBufferBuilder builder, Offset<SentisFlatBuffer.Chain>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateChainsVectorBlock(FlatBufferBuilder builder, Offset<SentisFlatBuffer.Chain>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateChainsVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<SentisFlatBuffer.Chain>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateChainsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<SentisFlatBuffer.Chain>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartChainsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddOperators(FlatBufferBuilder builder, VectorOffset operatorsOffset) { builder.AddOffset(7, operatorsOffset.Value, 0); }
  public static VectorOffset CreateOperatorsVector(FlatBufferBuilder builder, Offset<SentisFlatBuffer.Operator>[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateOperatorsVectorBlock(FlatBufferBuilder builder, Offset<SentisFlatBuffer.Operator>[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateOperatorsVectorBlock(FlatBufferBuilder builder, ArraySegment<Offset<SentisFlatBuffer.Operator>> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateOperatorsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<Offset<SentisFlatBuffer.Operator>>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartOperatorsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static void AddBackendPartitioning(FlatBufferBuilder builder, Offset<SentisFlatBuffer.BackendPartitioning> backendPartitioningOffset) { builder.AddOffset(8, backendPartitioningOffset.Value, 0); }
  public static void AddSymbolicDimNames(FlatBufferBuilder builder, VectorOffset symbolicDimNamesOffset) { builder.AddOffset(9, symbolicDimNamesOffset.Value, 0); }
  public static VectorOffset CreateSymbolicDimNamesVector(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddOffset(data[i].Value); return builder.EndVector(); }
  public static VectorOffset CreateSymbolicDimNamesVectorBlock(FlatBufferBuilder builder, StringOffset[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateSymbolicDimNamesVectorBlock(FlatBufferBuilder builder, ArraySegment<StringOffset> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateSymbolicDimNamesVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<StringOffset>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartSymbolicDimNamesVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<SentisFlatBuffer.ExecutionPlan> EndExecutionPlan(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<SentisFlatBuffer.ExecutionPlan>(o);
  }
}


static class ExecutionPlanVerify
{
  static public bool Verify(Unity.InferenceEngine.Google.FlatBuffers.Verifier verifier, uint tablePos)
  {
    return verifier.VerifyTableStart(tablePos)
      && verifier.VerifyString(tablePos, 4 /*Name*/, false)
      && verifier.VerifyVectorOfTables(tablePos, 6 /*Values*/, SentisFlatBuffer.EValueVerify.Verify, false)
      && verifier.VerifyVectorOfData(tablePos, 8 /*Inputs*/, 4 /*int*/, false)
      && verifier.VerifyVectorOfStrings(tablePos, 10 /*InputsName*/, false)
      && verifier.VerifyVectorOfData(tablePos, 12 /*Outputs*/, 4 /*int*/, false)
      && verifier.VerifyVectorOfStrings(tablePos, 14 /*OutputsName*/, false)
      && verifier.VerifyVectorOfTables(tablePos, 16 /*Chains*/, SentisFlatBuffer.ChainVerify.Verify, false)
      && verifier.VerifyVectorOfTables(tablePos, 18 /*Operators*/, SentisFlatBuffer.OperatorVerify.Verify, false)
      && verifier.VerifyTable(tablePos, 20 /*BackendPartitioning*/, SentisFlatBuffer.BackendPartitioningVerify.Verify, false)
      && verifier.VerifyVectorOfStrings(tablePos, 22 /*SymbolicDimNames*/, false)
      && verifier.VerifyTableEnd(tablePos);
  }
}

}
