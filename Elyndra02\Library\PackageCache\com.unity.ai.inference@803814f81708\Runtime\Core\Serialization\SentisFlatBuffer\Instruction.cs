// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>
#define ENABLE_SPAN_T
#define UNSAFE_BYTEBUFFER
#define BYTEBUFFER_NO_BOUNDS_CHECK

namespace SentisFlatBuffer
{

using global::System;
using global::System.Collections.Generic;
using global::Unity.InferenceEngine.Google.FlatBuffers;

struct Instruction : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_5_26(); }
  public static Instruction GetRootAsInstruction(ByteBuffer _bb) { return GetRootAsInstruction(_bb, new Instruction()); }
  public static Instruction GetRootAsInstruction(ByteBuffer _bb, Instruction obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, By<PERSON><PERSON>uff<PERSON> _bb) { __p = new Table(_i, _bb); }
  public Instruction __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public SentisFlatBuffer.InstructionArguments InstrArgsType { get { int o = __p.__offset(4); return o != 0 ? (SentisFlatBuffer.InstructionArguments)__p.bb.Get(o + __p.bb_pos) : SentisFlatBuffer.InstructionArguments.NONE; } }
  public TTable? InstrArgs<TTable>() where TTable : struct, IFlatbufferObject { int o = __p.__offset(6); return o != 0 ? (TTable?)__p.__union<TTable>(o + __p.bb_pos) : null; }
  public SentisFlatBuffer.KernelCall InstrArgsAsKernelCall() { return InstrArgs<SentisFlatBuffer.KernelCall>().Value; }

  public static Offset<SentisFlatBuffer.Instruction> CreateInstruction(FlatBufferBuilder builder,
      SentisFlatBuffer.InstructionArguments instr_args_type = SentisFlatBuffer.InstructionArguments.NONE,
      int instr_argsOffset = 0) {
    builder.StartTable(2);
    Instruction.AddInstrArgs(builder, instr_argsOffset);
    Instruction.AddInstrArgsType(builder, instr_args_type);
    return Instruction.EndInstruction(builder);
  }

  public static void StartInstruction(FlatBufferBuilder builder) { builder.StartTable(2); }
  public static void AddInstrArgsType(FlatBufferBuilder builder, SentisFlatBuffer.InstructionArguments instrArgsType) { builder.AddByte(0, (byte)instrArgsType, 0); }
  public static void AddInstrArgs(FlatBufferBuilder builder, int instrArgsOffset) { builder.AddOffset(1, instrArgsOffset, 0); }
  public static Offset<SentisFlatBuffer.Instruction> EndInstruction(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<SentisFlatBuffer.Instruction>(o);
  }
}


static class InstructionVerify
{
  static public bool Verify(Unity.InferenceEngine.Google.FlatBuffers.Verifier verifier, uint tablePos)
  {
    return verifier.VerifyTableStart(tablePos)
      && verifier.VerifyField(tablePos, 4 /*InstrArgsType*/, 1 /*SentisFlatBuffer.InstructionArguments*/, 1, false)
      // && verifier.VerifyUnion(tablePos, 4, 6 /*InstrArgs*/, SentisFlatBuffer.InstructionArgumentsVerify.Verify, false)
      && verifier.VerifyTableEnd(tablePos);
  }
}

}
