using UnityEngine;

public class AdvancedPhysicsManager : MonoBehaviour
{
    [Head<PERSON>("Physics Settings")]
    public Vector3 gravity = new Vector3(0, -9.81f, 0);
    public float timeScale = 1.0f;
    public int solverIterations = 6;
    
    [Header("Collision Detection")]
    public bool enableCCD = true;
    public float bounceThreshold = 2.0f;
    
    void Start()
    {
        ConfigurePhysics();
    }
    
    void ConfigurePhysics()
    {
        Physics.gravity = gravity;
        Time.timeScale = timeScale;
        Physics.defaultSolverIterations = solverIterations;
        Physics.bounceThreshold = bounceThreshold;
        
        Debug.Log($"Physics configured: Gravity={gravity}, Iterations={solverIterations}");
    }
    
    public void SetGravity(Vector3 newGravity)
    {
        gravity = newGravity;
        Physics.gravity = gravity;
    }
    
    public void SetTimeScale(float scale)
    {
        timeScale = Mathf.Clamp(scale, 0.1f, 2.0f);
        Time.timeScale = timeScale;
    }
    
    public void ResetPhysics()
    {
        Physics.gravity = new Vector3(0, -9.81f, 0);
        Time.timeScale = 1.0f;
        Physics.defaultSolverIterations = 6;
    }
}