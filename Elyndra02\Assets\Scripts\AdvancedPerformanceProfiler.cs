using UnityEngine;
using UnityEngine.Profiling;
using System.Collections.Generic;

public class AdvancedPerformanceProfiler : MonoBehaviour
{
    [Header("Profiling Settings")]
    public bool enableProfiling = true;
    public float updateInterval = 1.0f;
    public bool logToConsole = true;
    
    [Header("Performance Metrics")]
    public float currentFPS;
    public float averageFPS;
    public long memoryUsage;
    public int drawCalls;
    
    private List<float> fpsHistory = new List<float>();
    private float timer;
    
    void Start()
    {
        if (enableProfiling)
        {
            Profiler.enabled = true;
            Debug.Log("Advanced Performance Profiler started");
        }
    }
    
    void Update()
    {
        if (!enableProfiling) return;
        
        timer += Time.deltaTime;
        
        if (timer >= updateInterval)
        {
            UpdateMetrics();
            timer = 0f;
        }
    }
    
    void UpdateMetrics()
    {
        // FPS Calculation
        currentFPS = 1.0f / Time.deltaTime;
        fpsHistory.Add(currentFPS);
        
        if (fpsHistory.Count > 60) // Keep last 60 samples
        {
            fpsHistory.RemoveAt(0);
        }
        
        // Calculate average FPS
        float total = 0f;
        foreach (float fps in fpsHistory)
        {
            total += fps;
        }
        averageFPS = total / fpsHistory.Count;
        
        // Memory Usage
        memoryUsage = Profiler.GetTotalAllocatedMemoryLong();
        
        // Draw Calls (approximation)
        drawCalls = EstimateDrawCalls();
        
        if (logToConsole)
        {
            LogMetrics();
        }
    }
    
    int EstimateDrawCalls()
    {
        // Simple estimation based on active renderers
        Renderer[] renderers = FindObjectsByType<Renderer>(FindObjectsSortMode.None);
        return renderers.Length;
    }
    
    void LogMetrics()
    {
        Debug.Log($"Performance Metrics - FPS: {currentFPS:F1} (Avg: {averageFPS:F1}), Memory: {memoryUsage / 1024 / 1024}MB, Draw Calls: {drawCalls}");
    }
    
    public void StartProfiling()
    {
        enableProfiling = true;
        Profiler.enabled = true;
    }
    
    public void StopProfiling()
    {
        enableProfiling = false;
        Profiler.enabled = false;
    }
    
    public void ClearHistory()
    {
        fpsHistory.Clear();
    }
    
    void OnGUI()
    {
        if (!enableProfiling) return;
        
        GUILayout.BeginArea(new Rect(10, 10, 300, 150));
        GUILayout.Label($"FPS: {currentFPS:F1} (Avg: {averageFPS:F1})");
        GUILayout.Label($"Memory: {memoryUsage / 1024 / 1024}MB");
        GUILayout.Label($"Draw Calls: {drawCalls}");
        GUILayout.EndArea();
    }
}