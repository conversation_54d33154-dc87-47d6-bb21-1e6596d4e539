using UnityEngine;
using Unity.Netcode;

public class AdvancedNetworkManager : NetworkBehaviour
{
    [Header("Network Configuration")]
    public int maxPlayers = 10;
    public float tickRate = 60f;
    public bool enableRelay = false;
    
    [Header("Session Management")]
    public string sessionName = "DefaultSession";
    public bool isPrivateSession = false;
    
    private NetworkVariable<int> connectedPlayers = new NetworkVariable<int>(0);
    
    void Start()
    {
        ConfigureNetwork();
    }
    
    void ConfigureNetwork()
    {
        if (Unity.Netcode.NetworkManager.Singleton != null)
        {
            Unity.Netcode.NetworkManager.Singleton.NetworkConfig.TickRate = (uint)tickRate;
            Debug.Log($"Network configured: MaxPlayers={maxPlayers}, TickRate={tickRate}");
        }
    }
    
    public void StartHost()
    {
        if (Unity.Netcode.NetworkManager.Singleton != null)
        {
            Unity.Netcode.NetworkManager.Singleton.StartHost();
            Debug.Log("Started as Host");
        }
    }
    
    public void StartClient()
    {
        if (Unity.Netcode.NetworkManager.Singleton != null)
        {
            Unity.Netcode.NetworkManager.Singleton.StartClient();
            Debug.Log("Started as Client");
        }
    }
    
    public void StartServer()
    {
        if (Unity.Netcode.NetworkManager.Singleton != null)
        {
            Unity.Netcode.NetworkManager.Singleton.StartServer();
            Debug.Log("Started as Server");
        }
    }
    
    public override void OnNetworkSpawn()
    {
        if (IsServer)
        {
            connectedPlayers.Value++;
            Debug.Log($"Player connected. Total: {connectedPlayers.Value}");
        }
    }
    
    public override void OnNetworkDespawn()
    {
        if (IsServer)
        {
            connectedPlayers.Value--;
            Debug.Log($"Player disconnected. Total: {connectedPlayers.Value}");
        }
    }
    
    [ServerRpc(RequireOwnership = false)]
    public void SendMessageServerRpc(string message)
    {
        Debug.Log($"Server received: {message}");
        BroadcastMessageClientRpc(message);
    }
    
    [ClientRpc]
    void BroadcastMessageClientRpc(string message)
    {
        Debug.Log($"Broadcast message: {message}");
    }
}