// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>
#define ENABLE_SPAN_T
#define UNSAFE_BYTEBUFFER
#define BYTEBUFFER_NO_BOUNDS_CHECK

namespace SentisFlatBuffer
{

using global::System;
using global::System.Collections.Generic;
using global::Unity.InferenceEngine.Google.FlatBuffers;

struct EValue : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_5_26(); }
  public static EValue GetRootAsEValue(ByteBuffer _bb) { return GetRootAsEValue(_bb, new EValue()); }
  public static EValue GetRootAsEValue(ByteBuffer _bb, EValue obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __p = new Table(_i, _bb); }
  public EValue __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public SentisFlatBuffer.KernelTypes ValType { get { int o = __p.__offset(4); return o != 0 ? (SentisFlatBuffer.KernelTypes)__p.bb.Get(o + __p.bb_pos) : SentisFlatBuffer.KernelTypes.NONE; } }
  public TTable? Val<TTable>() where TTable : struct, IFlatbufferObject { int o = __p.__offset(6); return o != 0 ? (TTable?)__p.__union<TTable>(o + __p.bb_pos) : null; }
  public SentisFlatBuffer.Null ValAsNull() { return Val<SentisFlatBuffer.Null>().Value; }
  public SentisFlatBuffer.Int ValAsInt() { return Val<SentisFlatBuffer.Int>().Value; }
  public SentisFlatBuffer.Float ValAsFloat() { return Val<SentisFlatBuffer.Float>().Value; }
  public SentisFlatBuffer.Bool ValAsBool() { return Val<SentisFlatBuffer.Bool>().Value; }
  public SentisFlatBuffer.Byte ValAsByte() { return Val<SentisFlatBuffer.Byte>().Value; }
  public SentisFlatBuffer.Tensor ValAsTensor() { return Val<SentisFlatBuffer.Tensor>().Value; }
  public SentisFlatBuffer.String ValAsString() { return Val<SentisFlatBuffer.String>().Value; }
  public SentisFlatBuffer.IntList ValAsIntList() { return Val<SentisFlatBuffer.IntList>().Value; }
  public SentisFlatBuffer.FloatList ValAsFloatList() { return Val<SentisFlatBuffer.FloatList>().Value; }
  public SentisFlatBuffer.BoolList ValAsBoolList() { return Val<SentisFlatBuffer.BoolList>().Value; }

  public static Offset<SentisFlatBuffer.EValue> CreateEValue(FlatBufferBuilder builder,
      SentisFlatBuffer.KernelTypes val_type = SentisFlatBuffer.KernelTypes.NONE,
      int valOffset = 0) {
    builder.StartTable(2);
    EValue.AddVal(builder, valOffset);
    EValue.AddValType(builder, val_type);
    return EValue.EndEValue(builder);
  }

  public static void StartEValue(FlatBufferBuilder builder) { builder.StartTable(2); }
  public static void AddValType(FlatBufferBuilder builder, SentisFlatBuffer.KernelTypes valType) { builder.AddByte(0, (byte)valType, 0); }
  public static void AddVal(FlatBufferBuilder builder, int valOffset) { builder.AddOffset(1, valOffset, 0); }
  public static Offset<SentisFlatBuffer.EValue> EndEValue(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<SentisFlatBuffer.EValue>(o);
  }
}


static class EValueVerify
{
  static public bool Verify(Unity.InferenceEngine.Google.FlatBuffers.Verifier verifier, uint tablePos)
  {
    return verifier.VerifyTableStart(tablePos)
      && verifier.VerifyField(tablePos, 4 /*ValType*/, 1 /*SentisFlatBuffer.KernelTypes*/, 1, false)
      // && verifier.VerifyUnion(tablePos, 4, 6 /*Val*/, SentisFlatBuffer.KernelTypesVerify.Verify, false)
      && verifier.VerifyTableEnd(tablePos);
  }
}

}
