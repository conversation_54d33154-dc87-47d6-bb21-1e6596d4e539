// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>
#define ENABLE_SPAN_T
#define UNSAFE_BYTEBUFFER
#define BYTEBUFFER_NO_BOUNDS_CHECK

namespace SentisFlatBuffer
{

using global::System;
using global::System.Collections.Generic;
using global::Unity.InferenceEngine.Google.FlatBuffers;

struct KernelCall : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_5_26(); }
  public static KernelCall GetRootAsKernelCall(ByteBuffer _bb) { return GetRootAsKernelCall(_bb, new KernelCall()); }
  public static KernelCall GetRootAsKernelCall(ByteBuffer _bb, KernelCall obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, Byte<PERSON><PERSON>er _bb) { __p = new Table(_i, _bb); }
  public KernelCall __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public int OpIndex { get { int o = __p.__offset(4); return o != 0 ? __p.bb.GetInt(o + __p.bb_pos) : (int)0; } }
  public int Args(int j) { int o = __p.__offset(6); return o != 0 ? __p.bb.GetInt(__p.__vector(o) + j * 4) : (int)0; }
  public int ArgsLength { get { int o = __p.__offset(6); return o != 0 ? __p.__vector_len(o) : 0; } }
#if ENABLE_SPAN_T
  public Span<int> GetArgsBytes() { return __p.__vector_as_span<int>(6, 4); }
#else
  public ArraySegment<byte>? GetArgsBytes() { return __p.__vector_as_arraysegment(6); }
#endif
  public int[] GetArgsArray() { return __p.__vector_as_array<int>(6); }

  public static Offset<SentisFlatBuffer.KernelCall> CreateKernelCall(FlatBufferBuilder builder,
      int op_index = 0,
      VectorOffset argsOffset = default(VectorOffset)) {
    builder.StartTable(2);
    KernelCall.AddArgs(builder, argsOffset);
    KernelCall.AddOpIndex(builder, op_index);
    return KernelCall.EndKernelCall(builder);
  }

  public static void StartKernelCall(FlatBufferBuilder builder) { builder.StartTable(2); }
  public static void AddOpIndex(FlatBufferBuilder builder, int opIndex) { builder.AddInt(0, opIndex, 0); }
  public static void AddArgs(FlatBufferBuilder builder, VectorOffset argsOffset) { builder.AddOffset(1, argsOffset.Value, 0); }
  public static VectorOffset CreateArgsVector(FlatBufferBuilder builder, int[] data) { builder.StartVector(4, data.Length, 4); for (int i = data.Length - 1; i >= 0; i--) builder.AddInt(data[i]); return builder.EndVector(); }
  public static VectorOffset CreateArgsVectorBlock(FlatBufferBuilder builder, int[] data) { builder.StartVector(4, data.Length, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateArgsVectorBlock(FlatBufferBuilder builder, ArraySegment<int> data) { builder.StartVector(4, data.Count, 4); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateArgsVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<int>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartArgsVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(4, numElems, 4); }
  public static Offset<SentisFlatBuffer.KernelCall> EndKernelCall(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<SentisFlatBuffer.KernelCall>(o);
  }
}


static class KernelCallVerify
{
  static public bool Verify(Unity.InferenceEngine.Google.FlatBuffers.Verifier verifier, uint tablePos)
  {
    return verifier.VerifyTableStart(tablePos)
      && verifier.VerifyField(tablePos, 4 /*OpIndex*/, 4 /*int*/, 4, false)
      && verifier.VerifyVectorOfData(tablePos, 6 /*Args*/, 4 /*int*/, false)
      && verifier.VerifyTableEnd(tablePos);
  }
}

}
