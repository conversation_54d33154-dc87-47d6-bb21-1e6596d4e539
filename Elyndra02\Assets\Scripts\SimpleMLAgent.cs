using UnityEngine;
using System.Collections.Generic;

public class SimpleMLAgent : MonoBehaviour
{
    [Header("Agent Settings")]
    public float speed = 5f;
    public float detectionRadius = 10f;
    
    [Header("Learning")]
    public float learningRate = 0.01f;
    public float explorationRate = 0.1f;
    
    private Rigidbody rb;
    private Vector3 targetPosition;
    private bool hasTarget = false;
    private float currentReward = 0f;
    
    // Simple state-action values
    private Dictionary<int, float> actionValues = new Dictionary<int, float>();
    
    void Start()
    {
        InitializeAgent();
    }
    
    void InitializeAgent()
    {
        rb = GetComponent<Rigidbody>();
        if (rb == null)
            rb = gameObject.AddComponent<Rigidbody>();
            
        rb.freezeRotation = true;
        
        // Initialize action values
        for (int i = 0; i < 4; i++)
        {
            actionValues[i] = 0f;
        }
        
        FindNewTarget();
        Debug.Log("Simple ML Agent initialized!");
    }
    
    void Update()
    {
        int action = SelectAction();
        ExecuteAction(action);
        
        float reward = CalculateReward();
        UpdateLearning(action, reward);
        
        currentReward = reward;
    }
    
    int SelectAction()
    {
        // Epsilon-greedy selection
        if (Random.Range(0f, 1f) < explorationRate)
        {
            return Random.Range(0, 4);
        }
        
        // Select best action
        int bestAction = 0;
        float bestValue = actionValues[0];
        
        for (int i = 1; i < 4; i++)
        {
            if (actionValues[i] > bestValue)
            {
                bestValue = actionValues[i];
                bestAction = i;
            }
        }
        
        return bestAction;
    }
    
    void ExecuteAction(int action)
    {
        Vector3 movement = Vector3.zero;
        
        switch (action)
        {
            case 0: movement = transform.forward; break;
            case 1: movement = -transform.forward; break;
            case 2: movement = -transform.right; break;
            case 3: movement = transform.right; break;
        }
        
        rb.AddForce(movement * speed, ForceMode.VelocityChange);
        
        if (rb.linearVelocity.magnitude > speed)
        {
            rb.linearVelocity = rb.linearVelocity.normalized * speed;
        }
    }
    
    float CalculateReward()
    {
        float reward = 0f;
        
        if (hasTarget)
        {
            float distance = Vector3.Distance(transform.position, targetPosition);
            reward = (detectionRadius - distance) / detectionRadius;
            
            if (distance < 2f)
            {
                reward += 5f;
                FindNewTarget();
            }
        }
        
        return reward;
    }
    
    void UpdateLearning(int action, float reward)
    {
        actionValues[action] += learningRate * (reward - actionValues[action]);
    }
    
    void FindNewTarget()
    {
        Vector3 randomDir = Random.insideUnitSphere * detectionRadius;
        randomDir.y = 0;
        targetPosition = transform.position + randomDir;
        hasTarget = true;
    }
    
    public void SetTarget(Vector3 pos)
    {
        targetPosition = pos;
        hasTarget = true;
    }
    
    public float GetReward()
    {
        return currentReward;
    }
    
    void OnDrawGizmosSelected()
    {
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, detectionRadius);
        
        if (hasTarget)
        {
            Gizmos.color = Color.green;
            Gizmos.DrawSphere(targetPosition, 0.5f);
        }
    }
}