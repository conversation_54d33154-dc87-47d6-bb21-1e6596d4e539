using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace AuracronCharacters
{
    /// <summary>
    /// Controlador de Animações do Kaelen
    /// Gerencia todas as animações baseadas no design document
    /// </summary>
    public class KaelenAnimationController : MonoBehaviour
    {
        [Header("=== ANIMATION SETTINGS ===")]
        public Animator animator;
        public float animationBlendSpeed = 5f;
        public float combatAnimationSpeed = 1f;
        
        [Header("=== MOVEMENT ANIMATIONS ===")]
        public string idleAnimation = "Idle";
        public string walkAnimation = "Walk";
        public string runAnimation = "Run";
        public string strafeLeftAnimation = "StrafeLeft";
        public string strafeRightAnimation = "StrafeRight";
        
        [Header("=== COMBAT ANIMATIONS ===")]
        public string basicAttackAnimation = "BasicAttack";
        public string piercingChargeAnimation = "InvestidaPerfurante";
        public string etherealBarrierAnimation = "BarreiraEterea";
        public string huntersMarkAnimation = "MarcaDoCacador";
        public string convergenceAnimation = "ConvergenciaDasLaminas";
        
        [Header("=== SPECIAL ANIMATIONS ===")]
        public string deathAnimation = "Death";
        public string victoryAnimation = "Victory";
        public string tauntAnimation = "Taunt";
        public string recallAnimation = "Recall";
        
        [Header("=== ANIMATION PARAMETERS ===")]
        public string speedParameter = "Speed";
        public string isMovingParameter = "IsMoving";
        public string isInCombatParameter = "IsInCombat";
        public string isDeadParameter = "IsDead";
        public string velocityXParameter = "VelocityX";
        public string velocityZParameter = "VelocityZ";
        
        // Referencias dos componentes
        private KaelenController kaelenController;
        private KaelenStats kaelenStats;
        private CharacterController characterController;
        
        // Estados de animação
        private bool isPlayingAbilityAnimation = false;
        private bool isPlayingSpecialAnimation = false;
        private float lastAnimationTime = 0f;
        
        // Cache de hashes para performance
        private int speedHash;
        private int isMovingHash;
        private int isInCombatHash;
        private int isDeadHash;
        private int velocityXHash;
        private int velocityZHash;
        
        // Ability animation hashes
        private int basicAttackHash;
        private int piercingChargeHash;
        private int etherealBarrierHash;
        private int huntersMarkHash;
        private int convergenceHash;
        private int deathHash;
        private int victoryHash;
        private int tauntHash;
        private int recallHash;
        
        void Start()
        {
            InitializeComponents();
            CacheAnimationHashes();
            SetupAnimationEvents();
        }
        
        void Update()
        {
            UpdateMovementAnimations();
            UpdateCombatState();
        }
        
        private void InitializeComponents()
        {
            if (animator == null)
                animator = GetComponent<Animator>();
                
            kaelenController = GetComponent<KaelenController>();
            kaelenStats = GetComponent<KaelenStats>();
            characterController = GetComponent<CharacterController>();
            
            if (animator == null)
            {
                Debug.LogError("Animator não encontrado no Kaelen!");
            }
        }
        
        private void CacheAnimationHashes()
        {
            // Parameters
            speedHash = Animator.StringToHash(speedParameter);
            isMovingHash = Animator.StringToHash(isMovingParameter);
            isInCombatHash = Animator.StringToHash(isInCombatParameter);
            isDeadHash = Animator.StringToHash(isDeadParameter);
            velocityXHash = Animator.StringToHash(velocityXParameter);
            velocityZHash = Animator.StringToHash(velocityZParameter);
            
            // Triggers
            basicAttackHash = Animator.StringToHash(basicAttackAnimation);
            piercingChargeHash = Animator.StringToHash(piercingChargeAnimation);
            etherealBarrierHash = Animator.StringToHash(etherealBarrierAnimation);
            huntersMarkHash = Animator.StringToHash(huntersMarkAnimation);
            convergenceHash = Animator.StringToHash(convergenceAnimation);
            deathHash = Animator.StringToHash(deathAnimation);
            victoryHash = Animator.StringToHash(victoryAnimation);
            tauntHash = Animator.StringToHash(tauntAnimation);
            recallHash = Animator.StringToHash(recallAnimation);
        }
        
        private void SetupAnimationEvents()
        {
            // Subscribe to stats events
            if (kaelenStats != null)
            {
                kaelenStats.OnDeath.AddListener(PlayDeathAnimation);
            }
        }
        
        #region MOVEMENT ANIMATIONS
        private void UpdateMovementAnimations()
        {
            if (animator == null || kaelenStats.IsDead) return;
            
            // Get movement data
            Vector3 velocity = characterController.velocity;
            float speed = new Vector3(velocity.x, 0, velocity.z).magnitude;
            bool isMoving = speed > 0.1f;
            
            // Convert world velocity to local velocity for blend tree
            Vector3 localVelocity = transform.InverseTransformDirection(velocity);
            
            // Update animator parameters
            animator.SetFloat(speedHash, speed, 0.1f, Time.deltaTime);
            animator.SetBool(isMovingHash, isMoving);
            animator.SetFloat(velocityXHash, localVelocity.x, 0.1f, Time.deltaTime);
            animator.SetFloat(velocityZHash, localVelocity.z, 0.1f, Time.deltaTime);
        }
        
        public void SetMovementSpeed(float speedMultiplier)
        {
            if (animator != null)
            {
                animator.speed = speedMultiplier;
            }
        }
        #endregion
        
        #region COMBAT ANIMATIONS
        private void UpdateCombatState()
        {
            if (animator == null) return;
            
            // Determine if in combat (simplified logic)
            bool inCombat = Time.time - lastAnimationTime < 5f; // 5 seconds after last ability
            animator.SetBool(isInCombatHash, inCombat);
        }
        
        public void PlayBasicAttack()
        {
            if (CanPlayAnimation())
            {
                animator.SetTrigger(basicAttackHash);
                StartAbilityAnimation("Basic Attack", 1f);
            }
        }
        
        public void PlayPiercingCharge()
        {
            if (CanPlayAnimation())
            {
                animator.SetTrigger(piercingChargeHash);
                StartAbilityAnimation("Investida Perfurante", 1.5f);
            }
        }
        
        public void PlayEtherealBarrier()
        {
            if (CanPlayAnimation())
            {
                animator.SetTrigger(etherealBarrierHash);
                StartAbilityAnimation("Barreira Etérea", 2f);
            }
        }
        
        public void PlayHuntersMark()
        {
            if (CanPlayAnimation())
            {
                animator.SetTrigger(huntersMarkHash);
                StartAbilityAnimation("Marca do Caçador", 1.2f);
            }
        }
        
        public void PlayConvergenceOfBlades()
        {
            if (CanPlayAnimation())
            {
                animator.SetTrigger(convergenceHash);
                StartAbilityAnimation("Convergência das Lâminas", 4f);
            }
        }
        #endregion
        
        #region SPECIAL ANIMATIONS
        public void PlayDeathAnimation()
        {
            if (animator != null)
            {
                animator.SetBool(isDeadHash, true);
                animator.SetTrigger(deathHash);
                isPlayingSpecialAnimation = true;
                Debug.Log("Animação de morte iniciada");
            }
        }
        
        public void PlayVictoryAnimation()
        {
            if (CanPlaySpecialAnimation())
            {
                animator.SetTrigger(victoryHash);
                StartSpecialAnimation("Victory", 3f);
            }
        }
        
        public void PlayTauntAnimation()
        {
            if (CanPlaySpecialAnimation())
            {
                animator.SetTrigger(tauntHash);
                StartSpecialAnimation("Taunt", 2.5f);
            }
        }
        
        public void PlayRecallAnimation()
        {
            if (CanPlaySpecialAnimation())
            {
                animator.SetTrigger(recallHash);
                StartSpecialAnimation("Recall", 8f); // Recall é longo
            }
        }
        
        public void InterruptRecall()
        {
            if (isPlayingSpecialAnimation)
            {
                StopSpecialAnimation();
                Debug.Log("Recall interrompido!");
            }
        }
        #endregion
        
        #region ANIMATION STATE MANAGEMENT
        private bool CanPlayAnimation()
        {
            return animator != null && !kaelenStats.IsDead && !isPlayingAbilityAnimation;
        }
        
        private bool CanPlaySpecialAnimation()
        {
            return animator != null && !kaelenStats.IsDead && !isPlayingSpecialAnimation;
        }
        
        private void StartAbilityAnimation(string abilityName, float duration)
        {
            isPlayingAbilityAnimation = true;
            lastAnimationTime = Time.time;
            
            Debug.Log($"Iniciando animação: {abilityName}");
            
            // Auto-stop after duration
            StartCoroutine(StopAbilityAnimationAfterDelay(duration));
        }
        
        private void StartSpecialAnimation(string animationName, float duration)
        {
            isPlayingSpecialAnimation = true;
            
            Debug.Log($"Iniciando animação especial: {animationName}");
            
            // Auto-stop after duration
            StartCoroutine(StopSpecialAnimationAfterDelay(duration));
        }
        
        private IEnumerator StopAbilityAnimationAfterDelay(float delay)
        {
            yield return new WaitForSeconds(delay);
            isPlayingAbilityAnimation = false;
        }
        
        private IEnumerator StopSpecialAnimationAfterDelay(float delay)
        {
            yield return new WaitForSeconds(delay);
            isPlayingSpecialAnimation = false;
        }
        
        private void StopSpecialAnimation()
        {
            isPlayingSpecialAnimation = false;
            StopAllCoroutines();
        }
        
        public void ForceStopAllAnimations()
        {
            isPlayingAbilityAnimation = false;
            isPlayingSpecialAnimation = false;
            StopAllCoroutines();
        }
        #endregion
        
        #region ANIMATION EVENTS
        // Estes métodos são chamados pelos Animation Events no Unity
        
        public void OnBasicAttackHit()
        {
            // Chamado no frame exato do impacto do ataque básico
            Debug.Log("Basic Attack Hit Event");
            
            if (kaelenController != null)
            {
                // Trigger damage application
                kaelenController.ApplyBasicAttackDamage();
            }
        }
        
        public void OnPiercingChargeStart()
        {
            Debug.Log("Piercing Charge Start Event");
            // Pode ser usado para efeitos visuais ou sonoros
        }
        
        public void OnPiercingChargeHit()
        {
            Debug.Log("Piercing Charge Hit Event");
            // Aplicar dano da investida
        }
        
        public void OnEtherealBarrierCast()
        {
            Debug.Log("Ethereal Barrier Cast Event");
            // Ativar a barreira
        }
        
        public void OnHuntersMarkThrow()
        {
            Debug.Log("Hunter's Mark Throw Event");
            // Lançar o projétil da marca
        }
        
        public void OnConvergenceStart()
        {
            Debug.Log("Convergence Start Event");
            // Início da ultimate
        }
        
        public void OnConvergenceBladeStrike()
        {
            Debug.Log("Convergence Blade Strike Event");
            // Cada impacto de lâmina
        }
        
        public void OnAnimationComplete()
        {
            Debug.Log("Animation Complete Event");
            isPlayingAbilityAnimation = false;
        }
        
        public void OnFootstep()
        {
            // Evento para sons de passos
            Debug.Log("Footstep Event");
            
            // Aqui você pode tocar sons de passos baseados no terreno
            PlayFootstepSound();
        }
        
        private void PlayFootstepSound()
        {
            // Implementar sistema de som de passos
            // Pode variar baseado no tipo de terreno
        }
        #endregion
        
        #region UTILITY METHODS
        public bool IsPlayingAbilityAnimation() => isPlayingAbilityAnimation;
        public bool IsPlayingSpecialAnimation() => isPlayingSpecialAnimation;
        public bool IsPlayingAnyAnimation() => isPlayingAbilityAnimation || isPlayingSpecialAnimation;
        
        public void SetAnimationSpeed(float speed)
        {
            if (animator != null)
            {
                animator.speed = speed;
            }
        }
        
        public void PauseAnimations()
        {
            if (animator != null)
            {
                animator.speed = 0f;
            }
        }
        
        public void ResumeAnimations()
        {
            if (animator != null)
            {
                animator.speed = 1f;
            }
        }
        
        public AnimatorStateInfo GetCurrentAnimationState()
        {
            if (animator != null)
            {
                return animator.GetCurrentAnimatorStateInfo(0);
            }
            return new AnimatorStateInfo();
        }
        
        public float GetCurrentAnimationProgress()
        {
            if (animator != null)
            {
                return animator.GetCurrentAnimatorStateInfo(0).normalizedTime;
            }
            return 0f;
        }
        
        public void CrossFadeToAnimation(string animationName, float transitionDuration = 0.2f)
        {
            if (animator != null)
            {
                animator.CrossFade(animationName, transitionDuration);
            }
        }
        #endregion
        
        #region DEBUG
        void OnDrawGizmosSelected()
        {
            // Desenhar informações de debug no Scene View
            if (Application.isPlaying && animator != null)
            {
                // Mostrar estado atual da animação
                var currentState = animator.GetCurrentAnimatorStateInfo(0);
                
                Gizmos.color = Color.yellow;
                Gizmos.DrawWireSphere(transform.position + Vector3.up * 3f, 0.5f);
                
                // Adicionar texto de debug se necessário
            }
        }
        #endregion
    }
}