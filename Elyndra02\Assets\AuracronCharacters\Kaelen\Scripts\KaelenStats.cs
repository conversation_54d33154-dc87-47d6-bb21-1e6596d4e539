using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System;

namespace AuracronCharacters
{
    /// <summary>
    /// Sistema de Stats do Kaelen baseado no design document
    /// Inclui stats base, crescimento por nível, buffs/debuffs temporários
    /// </summary>
    [System.Serializable]
    public class CharacterStats
    {
        [Header("=== STATS BASE ===")]
        public float baseHealth = 580f;
        public float baseMana = 350f;
        public float baseAttackDamage = 68f;
        public float baseAttackSpeed = 0.625f;
        public float baseArmor = 38f;
        public float baseMagicResist = 32f;
        public float baseMoveSpeed = 340f;
        public float baseRange = 175f; // Melee range
        
        [Header("=== CRESCIMENTO POR NÍVEL ===")]
        public float healthPerLevel = 85f;
        public float manaPerLevel = 45f;
        public float attackDamagePerLevel = 3.2f;
        public float attackSpeedPerLevel = 0.03f;
        public float armorPerLevel = 3.8f;
        public float magicResistPerLevel = 1.25f;
        
        [Header("=== STATS ATUAIS ===")]
        [SerializeField] private float currentHealth;
        [SerializeField] private float currentMana;
        [SerializeField] private float currentAttackDamage;
        [SerializeField] private float currentAttackSpeed;
        [SerializeField] private float currentArmor;
        [SerializeField] private float currentMagicResist;
        [SerializeField] private float currentMoveSpeed;
        [SerializeField] private float currentRange;
        
        // Properties para acesso controlado
        public float CurrentHealth { get => currentHealth; set => currentHealth = Mathf.Max(0, value); }
        public float CurrentMana { get => currentMana; set => currentMana = Mathf.Max(0, value); }
        public float CurrentAttackDamage { get => currentAttackDamage; set => currentAttackDamage = Mathf.Max(0, value); }
        public float CurrentAttackSpeed { get => currentAttackSpeed; set => currentAttackSpeed = Mathf.Max(0.1f, value); }
        public float CurrentArmor { get => currentArmor; set => currentArmor = Mathf.Max(0, value); }
        public float CurrentMagicResist { get => currentMagicResist; set => currentMagicResist = Mathf.Max(0, value); }
        public float CurrentMoveSpeed { get => currentMoveSpeed; set => currentMoveSpeed = Mathf.Max(50f, value); }
        public float CurrentRange { get => currentRange; set => currentRange = Mathf.Max(100f, value); }
        
        public float MaxHealth { get; private set; }
        public float MaxMana { get; private set; }
        
        public void CalculateStatsForLevel(int level)
        {
            level = Mathf.Clamp(level, 1, 18);
            
            MaxHealth = baseHealth + (healthPerLevel * (level - 1));
            MaxMana = baseMana + (manaPerLevel * (level - 1));
            
            currentHealth = MaxHealth;
            currentMana = MaxMana;
            currentAttackDamage = baseAttackDamage + (attackDamagePerLevel * (level - 1));
            currentAttackSpeed = baseAttackSpeed + (attackSpeedPerLevel * (level - 1));
            currentArmor = baseArmor + (armorPerLevel * (level - 1));
            currentMagicResist = baseMagicResist + (magicResistPerLevel * (level - 1));
            currentMoveSpeed = baseMoveSpeed;
            currentRange = baseRange;
        }
    }
    
    [System.Serializable]
    public class BuffDebuff
    {
        public string name;
        public BuffType type;
        public StatType affectedStat;
        public float value;
        public float duration;
        public bool isPercentage;
        public float timeRemaining;
        
        public BuffDebuff(string buffName, BuffType buffType, StatType stat, float buffValue, float buffDuration, bool percentage = false)
        {
            name = buffName;
            type = buffType;
            affectedStat = stat;
            value = buffValue;
            duration = buffDuration;
            isPercentage = percentage;
            timeRemaining = buffDuration;
        }
    }
    
    public enum BuffType
    {
        Buff,
        Debuff
    }
    
    public enum StatType
    {
        Health,
        Mana,
        AttackDamage,
        AttackSpeed,
        Armor,
        MagicResist,
        MoveSpeed,
        Range
    }
    
    public class KaelenStats : MonoBehaviour
    {
        [Header("=== CONFIGURAÇÕES ===")]
        public CharacterStats baseStats = new CharacterStats();
        public int currentLevel = 1;
        
        [Header("=== REGENERAÇÃO ===")]
        public float healthRegenPerSecond = 1.5f;
        public float manaRegenPerSecond = 2.8f;
        
        [Header("=== EVENTOS ===")]
        public UnityEngine.Events.UnityEvent OnHealthChanged;
        public UnityEngine.Events.UnityEvent OnManaChanged;
        public UnityEngine.Events.UnityEvent OnDeath;
        public UnityEngine.Events.UnityEvent OnLevelUp;
        
        // Sistema de buffs/debuffs
        private List<BuffDebuff> activeBuffs = new List<BuffDebuff>();
        private Dictionary<StatType, float> statModifiers = new Dictionary<StatType, float>();
        
        // Cache dos stats finais
        private CharacterStats finalStats = new CharacterStats();
        
        // Flags de estado
        public bool IsDead { get; private set; } = false;
        public bool IsInvulnerable { get; set; } = false;
        
        void Start()
        {
            InitializeStats();
            StartCoroutine(RegenerationCoroutine());
        }
        
        void Update()
        {
            UpdateBuffs();
        }
        
        private void InitializeStats()
        {
            baseStats.CalculateStatsForLevel(currentLevel);
            RecalculateStats();
        }
        
        #region LEVEL SYSTEM
        public void LevelUp()
        {
            if (currentLevel < 18)
            {
                currentLevel++;
                float oldMaxHealth = baseStats.MaxHealth;
                float oldMaxMana = baseStats.MaxMana;
                
                baseStats.CalculateStatsForLevel(currentLevel);
                
                // Curar proporcionalmente ao aumento de vida/mana
                float healthIncrease = baseStats.MaxHealth - oldMaxHealth;
                float manaIncrease = baseStats.MaxMana - oldMaxMana;
                
                baseStats.CurrentHealth += healthIncrease;
                baseStats.CurrentMana += manaIncrease;
                
                RecalculateStats();
                OnLevelUp?.Invoke();
                
                Debug.Log($"Kaelen subiu para o nível {currentLevel}!");
            }
        }
        
        public void SetLevel(int level)
        {
            currentLevel = Mathf.Clamp(level, 1, 18);
            InitializeStats();
        }
        #endregion
        
        #region DAMAGE SYSTEM
        public void TakeDamage(float damage, DamageType damageType = DamageType.Physical)
        {
            if (IsDead || IsInvulnerable) return;
            
            float finalDamage = CalculateDamageReduction(damage, damageType);
            
            baseStats.CurrentHealth -= finalDamage;
            
            Debug.Log($"Kaelen recebeu {finalDamage} de dano ({damageType}). Vida restante: {baseStats.CurrentHealth}/{baseStats.MaxHealth}");
            
            OnHealthChanged?.Invoke();
            
            if (baseStats.CurrentHealth <= 0 && !IsDead)
            {
                Die();
            }
        }
        
        private float CalculateDamageReduction(float damage, DamageType damageType)
        {
            float resistance = damageType == DamageType.Physical ? finalStats.CurrentArmor : finalStats.CurrentMagicResist;
            float damageReduction = resistance / (resistance + 100f);
            return damage * (1f - damageReduction);
        }
        
        public void Heal(float amount)
        {
            if (IsDead) return;
            
            baseStats.CurrentHealth = Mathf.Min(baseStats.CurrentHealth + amount, baseStats.MaxHealth);
            OnHealthChanged?.Invoke();
            
            Debug.Log($"Kaelen foi curado em {amount}. Vida atual: {baseStats.CurrentHealth}/{baseStats.MaxHealth}");
        }
        
        public void RestoreMana(float amount)
        {
            if (IsDead) return;
            
            baseStats.CurrentMana = Mathf.Min(baseStats.CurrentMana + amount, baseStats.MaxMana);
            OnManaChanged?.Invoke();
        }
        
        public bool ConsumeMana(float amount)
        {
            if (baseStats.CurrentMana >= amount)
            {
                baseStats.CurrentMana -= amount;
                OnManaChanged?.Invoke();
                return true;
            }
            return false;
        }
        #endregion
        
        #region BUFF/DEBUFF SYSTEM
        public void AddBuff(string name, StatType stat, float value, float duration, bool isPercentage = false)
        {
            BuffDebuff newBuff = new BuffDebuff(name, BuffType.Buff, stat, value, duration, isPercentage);
            activeBuffs.Add(newBuff);
            RecalculateStats();
            
            Debug.Log($"Buff '{name}' aplicado: {stat} {(isPercentage ? "+" + (value * 100) + "%" : "+" + value)} por {duration}s");
        }
        
        public void AddDebuff(string name, StatType stat, float value, float duration, bool isPercentage = false)
        {
            BuffDebuff newDebuff = new BuffDebuff(name, BuffType.Debuff, stat, value, duration, isPercentage);
            activeBuffs.Add(newDebuff);
            RecalculateStats();
            
            Debug.Log($"Debuff '{name}' aplicado: {stat} {(isPercentage ? "-" + (value * 100) + "%" : "-" + value)} por {duration}s");
        }
        
        public void RemoveBuff(string name)
        {
            activeBuffs.RemoveAll(buff => buff.name == name);
            RecalculateStats();
        }
        
        public bool HasBuff(string name)
        {
            return activeBuffs.Exists(buff => buff.name == name);
        }
        
        private void UpdateBuffs()
        {
            bool needsRecalculation = false;
            
            for (int i = activeBuffs.Count - 1; i >= 0; i--)
            {
                activeBuffs[i].timeRemaining -= Time.deltaTime;
                
                if (activeBuffs[i].timeRemaining <= 0)
                {
                    Debug.Log($"Buff/Debuff '{activeBuffs[i].name}' expirou");
                    activeBuffs.RemoveAt(i);
                    needsRecalculation = true;
                }
            }
            
            if (needsRecalculation)
            {
                RecalculateStats();
            }
        }
        
        private void RecalculateStats()
        {
            // Reset modifiers
            statModifiers.Clear();
            
            // Apply all active buffs/debuffs
            foreach (var buff in activeBuffs)
            {
                if (!statModifiers.ContainsKey(buff.affectedStat))
                    statModifiers[buff.affectedStat] = 0f;
                
                float modifier = buff.value;
                if (buff.type == BuffType.Debuff)
                    modifier = -modifier;
                
                if (buff.isPercentage)
                {
                    // Percentage modifiers are multiplicative
                    statModifiers[buff.affectedStat] += modifier;
                }
                else
                {
                    // Flat modifiers are additive
                    statModifiers[buff.affectedStat] += modifier;
                }
            }
            
            // Calculate final stats
            finalStats = new CharacterStats();
            finalStats.CurrentHealth = baseStats.CurrentHealth;
            finalStats.CurrentMana = baseStats.CurrentMana;
            finalStats.CurrentAttackDamage = ApplyModifier(baseStats.CurrentAttackDamage, StatType.AttackDamage);
            finalStats.CurrentAttackSpeed = ApplyModifier(baseStats.CurrentAttackSpeed, StatType.AttackSpeed);
            finalStats.CurrentArmor = ApplyModifier(baseStats.CurrentArmor, StatType.Armor);
            finalStats.CurrentMagicResist = ApplyModifier(baseStats.CurrentMagicResist, StatType.MagicResist);
            finalStats.CurrentMoveSpeed = ApplyModifier(baseStats.CurrentMoveSpeed, StatType.MoveSpeed);
            finalStats.CurrentRange = ApplyModifier(baseStats.CurrentRange, StatType.Range);
        }
        
        private float ApplyModifier(float baseValue, StatType statType)
        {
            if (!statModifiers.ContainsKey(statType))
                return baseValue;
            
            float modifier = statModifiers[statType];
            
            // Apply percentage modifiers multiplicatively
            // Apply flat modifiers additively
            return baseValue * (1f + modifier) + modifier;
        }
        #endregion
        
        #region REGENERATION
        private IEnumerator RegenerationCoroutine()
        {
            while (!IsDead)
            {
                yield return new WaitForSeconds(1f);
                
                if (!IsDead)
                {
                    // Health regeneration
                    if (baseStats.CurrentHealth < baseStats.MaxHealth)
                    {
                        Heal(healthRegenPerSecond);
                    }
                    
                    // Mana regeneration
                    if (baseStats.CurrentMana < baseStats.MaxMana)
                    {
                        RestoreMana(manaRegenPerSecond);
                    }
                }
            }
        }
        #endregion
        
        #region DEATH SYSTEM
        private void Die()
        {
            IsDead = true;
            Debug.Log("Kaelen morreu!");
            OnDeath?.Invoke();
            
            // Parar regeneração
            StopAllCoroutines();
            
            // Limpar todos os buffs
            activeBuffs.Clear();
            
            // Trigger death animation
            Animator animator = GetComponent<Animator>();
            if (animator != null)
            {
                animator.SetTrigger("Death");
            }
        }
        
        public void Revive(float healthPercentage = 1f)
        {
            if (!IsDead) return;
            
            IsDead = false;
            baseStats.CurrentHealth = baseStats.MaxHealth * healthPercentage;
            baseStats.CurrentMana = baseStats.MaxMana;
            
            // Restart regeneration
            StartCoroutine(RegenerationCoroutine());
            
            OnHealthChanged?.Invoke();
            OnManaChanged?.Invoke();
            
            Debug.Log($"Kaelen foi revivido com {healthPercentage * 100}% de vida!");
        }
        #endregion
        
        #region GETTERS
        public CharacterStats GetFinalStats() => finalStats;
        public CharacterStats GetBaseStats() => baseStats;
        public List<BuffDebuff> GetActiveBuffs() => new List<BuffDebuff>(activeBuffs);
        
        public float GetHealthPercentage() => baseStats.CurrentHealth / baseStats.MaxHealth;
        public float GetManaPercentage() => baseStats.CurrentMana / baseStats.MaxMana;
        
        public bool IsFullHealth() => baseStats.CurrentHealth >= baseStats.MaxHealth;
        public bool IsFullMana() => baseStats.CurrentMana >= baseStats.MaxMana;
        #endregion
    }
    
    public enum DamageType
    {
        Physical,
        Magical,
        True // Ignora resistências
    }
}