using UnityEngine;
using UnityEngine.VFX;

public class VFXParticleManager : MonoBehaviour
{
    [Header("Particle Effects")]
    public ParticleSystem[] particleSystems;
    public VisualEffect[] visualEffects;
    
    [Header("Global Settings")]
    public float globalIntensity = 1.0f;
    public bool playOnStart = true;
    
    void Start()
    {
        InitializeEffects();
        
        if (playOnStart)
        {
            PlayAllEffects();
        }
    }
    
    void InitializeEffects()
    {
        // Find all particle systems if not assigned
        if (particleSystems == null || particleSystems.Length == 0)
        {
            particleSystems = GetComponentsInChildren<ParticleSystem>();
        }
        
        // Find all visual effects if not assigned
        if (visualEffects == null || visualEffects.Length == 0)
        {
            visualEffects = GetComponentsInChildren<VisualEffect>();
        }
        
        Debug.Log($"VFX Manager initialized with {particleSystems.Length} particle systems and {visualEffects.Length} visual effects");
    }
    
    public void PlayAllEffects()
    {
        foreach (var ps in particleSystems)
        {
            if (ps != null) ps.Play();
        }
        
        foreach (var vfx in visualEffects)
        {
            if (vfx != null) vfx.Play();
        }
        
        Debug.Log("All VFX effects started");
    }
    
    public void StopAllEffects()
    {
        foreach (var ps in particleSystems)
        {
            if (ps != null) ps.Stop();
        }
        
        foreach (var vfx in visualEffects)
        {
            if (vfx != null) vfx.Stop();
        }
        
        Debug.Log("All VFX effects stopped");
    }
    
    public void SetGlobalIntensity(float intensity)
    {
        globalIntensity = Mathf.Clamp01(intensity);
        
        foreach (var ps in particleSystems)
        {
            if (ps != null)
            {
                var emission = ps.emission;
                emission.rateOverTimeMultiplier = globalIntensity;
            }
        }
        
        Debug.Log($"Global VFX intensity set to {globalIntensity}");
    }
}