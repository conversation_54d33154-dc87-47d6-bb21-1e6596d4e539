using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace AuracronCharacters
{
    /// <summary>
    /// Sistema de Habilidades do Kaelen
    /// Q: Investida Perfurante
    /// W: Barreira Etérea
    /// E: Marca do Caçador
    /// R: Convergência das Lâminas
    /// </summary>
    public class KaelenAbilities : MonoBehaviour
    {
        [Header("=== ABILITY SETTINGS ===")]
        public LayerMask enemyLayer = -1;
        public LayerMask allyLayer = -1;
        
        [Header("=== Q - INVESTIDA PERFURANTE ===")]
        public float dashDistance = 8f;
        public float dashSpeed = 20f;
        public float dashDamage = 80f;
        public GameObject dashTrailEffect;
        
        [Header("=== W - BARREIRA ETÉREA ===")]
        public float barrierRadius = 5f;
        public float barrierDuration = 3f;
        public float speedBuffMultiplier = 1.4f;
        public float enemySlowMultiplier = 0.5f;
        public GameObject barrierEffect;
        
        [Header("=== E - MARCA DO CAÇADOR ===")]
        public float markDuration = 8f;
        public float projectileSpeed = 15f;
        public float markDamage = 100f;
        public GameObject markProjectile;
        public GameObject markEffect;
        
        [Header("=== R - CONVERGÊNCIA DAS LÂMINAS ===")]
        public float ultimateRadius = 8f;
        public int bladeCount = 7;
        public float ultimateDuration = 3f;
        public float bladeDamage = 150f;
        public GameObject bladeEffect;
        public GameObject ultimateAreaEffect;
        
        private KaelenController kaelenController;
        private CharacterController characterController;
        private Animator animator;
        
        // Ability states
        private bool isDashing = false;
        private bool barrierActive = false;
        private bool ultimateActive = false;
        private Transform markedTarget;
        private Coroutine barrierCoroutine;
        
        void Start()
        {
            kaelenController = GetComponent<KaelenController>();
            characterController = GetComponent<CharacterController>();
            animator = GetComponent<Animator>();
        }
        
        #region Q - INVESTIDA PERFURANTE
        public void ExecuteInvestidaPerfurante()
        {
            if (isDashing) return;
            
            StartCoroutine(InvestidaPerfuranteCoroutine());
        }
        
        private IEnumerator InvestidaPerfuranteCoroutine()
        {
            isDashing = true;
            
            // Ativar animação
            if (animator != null)
                animator.SetTrigger("InvestidaPerfurante");
            
            // Criar efeito visual do dash
            GameObject trailEffect = null;
            if (dashTrailEffect != null)
            {
                trailEffect = Instantiate(dashTrailEffect, transform.position, transform.rotation);
                trailEffect.transform.SetParent(transform);
            }
            
            // Calcular direção e distância do dash
            Vector3 dashDirection = transform.forward;
            Vector3 startPos = transform.position;
            Vector3 endPos = startPos + dashDirection * dashDistance;
            
            // Verificar se o caminho está livre
            RaycastHit hit;
            if (Physics.Raycast(startPos, dashDirection, out hit, dashDistance))
            {
                endPos = hit.point - dashDirection * 0.5f; // Parar antes do obstáculo
            }
            
            float dashTime = Vector3.Distance(startPos, endPos) / dashSpeed;
            float elapsedTime = 0f;
            
            List<Transform> hitTargets = new List<Transform>();
            
            // Executar o dash
            while (elapsedTime < dashTime)
            {
                float t = elapsedTime / dashTime;
                Vector3 newPos = Vector3.Lerp(startPos, endPos, t);
                
                // Verificar colisões durante o dash
                Collider[] hits = Physics.OverlapSphere(newPos, 1f, enemyLayer);
                foreach (var hitCollider in hits)
                {
                    if (!hitTargets.Contains(hitCollider.transform))
                    {
                        hitTargets.Add(hitCollider.transform);
                        ApplyDashDamage(hitCollider.transform);
                        
                        // Máximo 3 inimigos em linha reta
                        if (hitTargets.Count >= 3)
                            break;
                    }
                }
                
                // Mover o personagem
                Vector3 movement = newPos - transform.position;
                characterController.Move(movement);
                
                elapsedTime += Time.deltaTime;
                yield return null;
            }
            
            // Limpar efeito visual
            if (trailEffect != null)
                Destroy(trailEffect, 2f);
                
            isDashing = false;
        }
        
        private void ApplyDashDamage(Transform target)
        {
            // Aplicar dano
            Debug.Log($"Investida Perfurante atingiu {target.name}! Dano: {dashDamage}");
            
            // Aplicar knockback
            Rigidbody targetRb = target.GetComponent<Rigidbody>();
            if (targetRb != null)
            {
                Vector3 knockbackDirection = (target.position - transform.position).normalized;
                targetRb.AddForce(knockbackDirection * 10f, ForceMode.Impulse);
            }
            
            // Criar efeito de impacto
            CreateImpactEffect(target.position);
        }
        #endregion
        
        #region W - BARREIRA ETÉREA
        public void ExecuteBarreiraEterea()
        {
            if (barrierActive) return;
            
            if (barrierCoroutine != null)
                StopCoroutine(barrierCoroutine);
                
            barrierCoroutine = StartCoroutine(BarreiraEtereaCoroutine());
        }
        
        private IEnumerator BarreiraEtereaCoroutine()
        {
            barrierActive = true;
            
            // Ativar animação
            if (animator != null)
                animator.SetTrigger("BarreiraEterea");
            
            // Criar efeito visual da barreira
            GameObject barrier = null;
            if (barrierEffect != null)
            {
                barrier = Instantiate(barrierEffect, transform.position, Quaternion.identity);
                barrier.transform.localScale = Vector3.one * barrierRadius * 2f;
            }
            
            float elapsedTime = 0f;
            
            while (elapsedTime < barrierDuration)
            {
                // Verificar entidades na área da barreira
                Collider[] hits = Physics.OverlapSphere(transform.position, barrierRadius);
                
                foreach (var hit in hits)
                {
                    if (IsAlly(hit.gameObject))
                    {
                        ApplySpeedBuff(hit.transform);
                    }
                    else if (IsEnemy(hit.gameObject))
                    {
                        ApplyBarrierDebuff(hit.transform);
                    }
                }
                
                elapsedTime += Time.deltaTime;
                yield return new WaitForSeconds(0.1f);
            }
            
            // Limpar efeito visual
            if (barrier != null)
                Destroy(barrier);
                
            barrierActive = false;
        }
        
        private void ApplySpeedBuff(Transform ally)
        {
            // Implementar buff de velocidade
            Debug.Log($"Aliado {ally.name} recebeu buff de velocidade!");
            
            // Aqui você pode implementar um sistema de buffs mais complexo
            CharacterController allyController = ally.GetComponent<CharacterController>();
            if (allyController != null)
            {
                // Aplicar buff temporário
            }
        }
        
        private void ApplyBarrierDebuff(Transform enemy)
        {
            Debug.Log($"Inimigo {enemy.name} recebeu slow e dano contínuo!");
            
            // Aplicar dano contínuo
            // Aplicar slow
        }
        #endregion
        
        #region E - MARCA DO CAÇADOR
        public void ExecuteMarcaDoCacador(Vector3 targetDirection)
        {
            StartCoroutine(MarcaDoCacadorCoroutine(targetDirection));
        }
        
        private IEnumerator MarcaDoCacadorCoroutine(Vector3 direction)
        {
            // Ativar animação
            if (animator != null)
                animator.SetTrigger("MarcaDoCacador");
            
            // Criar projétil
            if (markProjectile != null)
            {
                GameObject projectile = Instantiate(markProjectile, transform.position + Vector3.up, Quaternion.LookRotation(direction));
                
                // Mover projétil
                Rigidbody projectileRb = projectile.GetComponent<Rigidbody>();
                if (projectileRb != null)
                {
                    projectileRb.linearVelocity = direction.normalized * projectileSpeed;
                }
                
                // Configurar detecção de colisão do projétil
                MarkProjectile markScript = projectile.GetComponent<MarkProjectile>();
                if (markScript == null)
                {
                    markScript = projectile.AddComponent<MarkProjectile>();
                }
                markScript.Initialize(this, markDamage);
                
                // Destruir projétil após tempo limite
                Destroy(projectile, 5f);
            }
            
            yield return null;
        }
        
        public void MarkTarget(Transform target)
        {
            markedTarget = target;
            
            // Criar efeito visual no alvo marcado
            if (markEffect != null && target != null)
            {
                GameObject mark = Instantiate(markEffect, target.position, Quaternion.identity);
                mark.transform.SetParent(target);
                
                // Destruir marca após duração
                Destroy(mark, markDuration);
            }
            
            // Iniciar timer da marca
            StartCoroutine(MarkDurationCoroutine());
            
            Debug.Log($"Alvo {target.name} foi marcado!");
        }
        
        private IEnumerator MarkDurationCoroutine()
        {
            yield return new WaitForSeconds(markDuration);
            markedTarget = null;
        }
        
        public void TeleportToMarkedTarget()
        {
            if (markedTarget != null)
            {
                // Calcular posição de teleporte
                Vector3 teleportPos = markedTarget.position - markedTarget.forward * 2f;
                
                // Teleportar
                transform.position = teleportPos;
                
                // Aplicar dano
                Debug.Log($"Teleporte para {markedTarget.name}! Dano: {markDamage}");
                
                // Criar efeito de teleporte
                CreateTeleportEffect(teleportPos);
                
                // Reset da marca se o alvo morrer
                // Implementar lógica de reset de cooldown
            }
        }
        #endregion
        
        #region R - CONVERGÊNCIA DAS LÂMINAS
        public void ExecuteConvergenciaDasLaminas()
        {
            if (ultimateActive) return;
            
            StartCoroutine(ConvergenciaDasLaminasCoroutine());
        }
        
        private IEnumerator ConvergenciaDasLaminasCoroutine()
        {
            ultimateActive = true;
            
            // Ativar animação
            if (animator != null)
                animator.SetTrigger("ConvergenciaDasLaminas");
            
            // Kaelen flutua
            Vector3 originalPos = transform.position;
            Vector3 floatPos = originalPos + Vector3.up * 1f;
            
            // Subir gradualmente
            yield return StartCoroutine(MoveToPosition(originalPos, floatPos, 1.5f));
            
            // Criar efeito da área ultimate
            GameObject areaEffect = null;
            if (ultimateAreaEffect != null)
            {
                areaEffect = Instantiate(ultimateAreaEffect, transform.position, Quaternion.identity);
                areaEffect.transform.localScale = Vector3.one * ultimateRadius * 2f;
            }
            
            // Chuva de lâminas
            yield return StartCoroutine(BladeRainCoroutine());
            
            // Descer de volta
            yield return StartCoroutine(MoveToPosition(floatPos, originalPos, 0.5f));
            
            // Limpar efeitos
            if (areaEffect != null)
                Destroy(areaEffect);
                
            ultimateActive = false;
        }
        
        private IEnumerator MoveToPosition(Vector3 from, Vector3 to, float duration)
        {
            float elapsedTime = 0f;
            
            while (elapsedTime < duration)
            {
                float t = elapsedTime / duration;
                transform.position = Vector3.Lerp(from, to, t);
                elapsedTime += Time.deltaTime;
                yield return null;
            }
            
            transform.position = to;
        }
        
        private IEnumerator BladeRainCoroutine()
        {
            float interval = ultimateDuration / bladeCount;
            
            for (int i = 0; i < bladeCount; i++)
            {
                // Posição aleatória na área
                Vector3 bladePos = GetRandomPositionInArea();
                
                // Criar lâmina
                CreateBlade(bladePos);
                
                yield return new WaitForSeconds(interval);
            }
        }
        
        private Vector3 GetRandomPositionInArea()
        {
            Vector2 randomCircle = Random.insideUnitCircle * ultimateRadius;
            return transform.position + new Vector3(randomCircle.x, 0, randomCircle.y);
        }
        
        private void CreateBlade(Vector3 position)
        {
            // Criar efeito visual da lâmina
            if (bladeEffect != null)
            {
                GameObject blade = Instantiate(bladeEffect, position + Vector3.up * 10f, Quaternion.identity);
                
                // Animar lâmina caindo
                StartCoroutine(AnimateBladeFall(blade, position));
            }
            
            // Aplicar dano após delay
            StartCoroutine(ApplyBladeDelayedDamage(position, 1f));
        }
        
        private IEnumerator AnimateBladeFall(GameObject blade, Vector3 targetPos)
        {
            Vector3 startPos = blade.transform.position;
            float fallTime = 1f;
            float elapsedTime = 0f;
            
            while (elapsedTime < fallTime)
            {
                float t = elapsedTime / fallTime;
                blade.transform.position = Vector3.Lerp(startPos, targetPos, t);
                elapsedTime += Time.deltaTime;
                yield return null;
            }
            
            // Criar efeito de impacto
            CreateImpactEffect(targetPos);
            
            // Destruir lâmina
            Destroy(blade, 2f);
        }
        
        private IEnumerator ApplyBladeDelayedDamage(Vector3 position, float delay)
        {
            yield return new WaitForSeconds(delay);
            
            // Verificar inimigos na área
            Collider[] hits = Physics.OverlapSphere(position, 2f, enemyLayer);
            
            foreach (var hit in hits)
            {
                float damage = bladeDamage;
                
                // Dano dobrado no centro
                float distanceFromCenter = Vector3.Distance(position, transform.position);
                if (distanceFromCenter < 2f)
                {
                    damage *= 2f;
                }
                
                Debug.Log($"Lâmina atingiu {hit.name}! Dano: {damage}");
                
                // Aplicar slow crescente
                ApplySlowEffect(hit.transform);
            }
        }
        #endregion
        
        #region UTILITY METHODS
        private bool IsAlly(GameObject obj)
        {
            return ((1 << obj.layer) & allyLayer) != 0;
        }
        
        private bool IsEnemy(GameObject obj)
        {
            return ((1 << obj.layer) & enemyLayer) != 0;
        }
        
        private void CreateImpactEffect(Vector3 position)
        {
            // Implementar efeito de impacto
            Debug.Log($"Efeito de impacto criado em {position}");
        }
        
        private void CreateTeleportEffect(Vector3 position)
        {
            // Implementar efeito de teleporte
            Debug.Log($"Efeito de teleporte criado em {position}");
        }
        
        private void ApplySlowEffect(Transform target)
        {
            // Implementar efeito de slow
            Debug.Log($"Slow aplicado em {target.name}");
        }
        
        public bool IsDashing() => isDashing;
        public bool IsBarrierActive() => barrierActive;
        public bool IsUltimateActive() => ultimateActive;
        public Transform GetMarkedTarget() => markedTarget;
        #endregion
    }
    
    // Classe auxiliar para o projétil da marca
    public class MarkProjectile : MonoBehaviour
    {
        private KaelenAbilities kaelenAbilities;
        private float damage;
        
        public void Initialize(KaelenAbilities abilities, float dmg)
        {
            kaelenAbilities = abilities;
            damage = dmg;
        }
        
        void OnTriggerEnter(Collider other)
        {
            if (other.CompareTag("Enemy"))
            {
                // Marcar o alvo
                kaelenAbilities.MarkTarget(other.transform);
                
                // Destruir projétil
                Destroy(gameObject);
            }
        }
    }
}