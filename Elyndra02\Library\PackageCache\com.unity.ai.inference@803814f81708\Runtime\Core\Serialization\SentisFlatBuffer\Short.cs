// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>
#define ENABLE_SPAN_T
#define UNSAFE_BYTEBUFFER
#define BYTEBUFFER_NO_BOUNDS_CHECK

namespace SentisFlatBuffer
{

using global::System;
using global::System.Collections.Generic;
using global::Unity.InferenceEngine.Google.FlatBuffers;

struct Short : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_5_26(); }
  public static Short GetRootAsShort(ByteBuffer _bb) { return GetRootAsShort(_bb, new Short()); }
  public static Short GetRootAsShort(ByteBuffer _bb, Short obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, Byte<PERSON>uffer _bb) { __p = new Table(_i, _bb); }
  public Short __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public short ShortVal { get { int o = __p.__offset(4); return o != 0 ? __p.bb.GetShort(o + __p.bb_pos) : (short)0; } }

  public static Offset<SentisFlatBuffer.Short> CreateShort(FlatBufferBuilder builder,
      short short_val = 0) {
    builder.StartTable(1);
    Short.AddShortVal(builder, short_val);
    return Short.EndShort(builder);
  }

  public static void StartShort(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddShortVal(FlatBufferBuilder builder, short shortVal) { builder.AddShort(0, shortVal, 0); }
  public static Offset<SentisFlatBuffer.Short> EndShort(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<SentisFlatBuffer.Short>(o);
  }
}


static class ShortVerify
{
  static public bool Verify(Unity.InferenceEngine.Google.FlatBuffers.Verifier verifier, uint tablePos)
  {
    return verifier.VerifyTableStart(tablePos)
      && verifier.VerifyField(tablePos, 4 /*ShortVal*/, 2 /*short*/, 2, false)
      && verifier.VerifyTableEnd(tablePos);
  }
}

}
