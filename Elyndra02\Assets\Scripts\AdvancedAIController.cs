using UnityEngine;
using UnityEngine.AI;
using System.Collections;
using System.Collections.Generic;

public class AdvancedAIController : MonoBeh<PERSON><PERSON>
{
    [Header("AI Configuration")]
    public float detectionRadius = 10f;
    public float attackRange = 2f;
    public float patrolSpeed = 2f;
    public float chaseSpeed = 5f;
    
    [Header("AI States")]
    public AIState currentState = AIState.Patrol;
    
    [Header("Patrol Points")]
    public Transform[] patrolPoints;
    private int currentPatrolIndex = 0;
    
    [Header("Target")]
    public Transform target;
    
    private NavMeshAgent agent;
    private Animator animator;
    private float lastStateChange;
    
    public enum AIState
    {
        Patrol,
        Chase,
        Attack,
        Search,
        Return
    }
    
    void Start()
    {
        agent = GetComponent<NavMeshAgent>();
        animator = GetComponent<Animator>();
        
        if (agent == null)
        {
            agent = gameObject.AddComponent<NavMeshAgent>();
        }
        
        SetState(AIState.Patrol);
    }
    
    void Update()
    {
        switch (currentState)
        {
            case AIState.Patrol:
                PatrolBehavior();
                break;
            case AIState.Chase:
                ChaseBehavior();
                break;
            case AIState.Attack:
                AttackBehavior();
                break;
            case AIState.Search:
                SearchBehavior();
                break;
            case AIState.Return:
                ReturnBehavior();
                break;
        }
        
        CheckForTarget();
    }
    
    void PatrolBehavior()
    {
        if (patrolPoints.Length == 0) return;
        
        agent.speed = patrolSpeed;
        
        if (!agent.pathPending && agent.remainingDistance < 0.5f)
        {
            currentPatrolIndex = (currentPatrolIndex + 1) % patrolPoints.Length;
            agent.SetDestination(patrolPoints[currentPatrolIndex].position);
        }
    }
    
    void ChaseBehavior()
    {
        if (target == null)
        {
            SetState(AIState.Search);
            return;
        }
        
        agent.speed = chaseSpeed;
        agent.SetDestination(target.position);
        
        float distanceToTarget = Vector3.Distance(transform.position, target.position);
        
        if (distanceToTarget <= attackRange)
        {
            SetState(AIState.Attack);
        }
        else if (distanceToTarget > detectionRadius * 1.5f)
        {
            SetState(AIState.Search);
        }
    }
    
    void AttackBehavior()
    {
        if (target == null)
        {
            SetState(AIState.Search);
            return;
        }
        
        agent.SetDestination(transform.position);
        transform.LookAt(target);
        
        if (animator != null)
        {
            animator.SetTrigger("Attack");
        }
        
        float distanceToTarget = Vector3.Distance(transform.position, target.position);
        
        if (distanceToTarget > attackRange)
        {
            SetState(AIState.Chase);
        }
    }
    
    void SearchBehavior()
    {
        agent.speed = patrolSpeed;
        
        if (Time.time - lastStateChange > 5f)
        {
            SetState(AIState.Return);
        }
    }
    
    void ReturnBehavior()
    {
        if (patrolPoints.Length == 0)
        {
            SetState(AIState.Patrol);
            return;
        }
        
        agent.speed = patrolSpeed;
        agent.SetDestination(patrolPoints[currentPatrolIndex].position);
        
        if (!agent.pathPending && agent.remainingDistance < 0.5f)
        {
            SetState(AIState.Patrol);
        }
    }
    
    void CheckForTarget()
    {
        if (currentState == AIState.Attack || currentState == AIState.Chase) return;
        
        Collider[] colliders = Physics.OverlapSphere(transform.position, detectionRadius);
        
        foreach (Collider col in colliders)
        {
            if (col.CompareTag("Player"))
            {
                target = col.transform;
                SetState(AIState.Chase);
                break;
            }
        }
    }
    
    void SetState(AIState newState)
    {
        currentState = newState;
        lastStateChange = Time.time;
        
        if (animator != null)
        {
            animator.SetInteger("State", (int)newState);
        }
    }
    
    void OnDrawGizmosSelected()
    {
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, detectionRadius);
        
        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(transform.position, attackRange);
        
        if (patrolPoints != null)
        {
            Gizmos.color = Color.blue;
            for (int i = 0; i < patrolPoints.Length; i++)
            {
                if (patrolPoints[i] != null)
                {
                    Gizmos.DrawWireSphere(patrolPoints[i].position, 0.5f);
                    
                    if (i < patrolPoints.Length - 1 && patrolPoints[i + 1] != null)
                    {
                        Gizmos.DrawLine(patrolPoints[i].position, patrolPoints[i + 1].position);
                    }
                }
            }
        }
    }
}