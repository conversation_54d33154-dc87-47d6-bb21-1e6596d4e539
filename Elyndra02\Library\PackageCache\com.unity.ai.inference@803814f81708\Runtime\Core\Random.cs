using System;
using System.Runtime.InteropServices;

namespace Unity.InferenceEngine
{
    /// <summary>
    /// Helper struct class for converting between ints, uints and floats in bytes without allocation
    /// </summary>
    [StructLayout(LayoutKind.Explicit)]
    struct Seed
    {
        [FieldOffset(0)]
        public int intSeed;
        [FieldOffset(0)]
        public uint uintSeed;
    }

    /// <summary>
    /// Represents a pseudo-random number generator used by Inference Engine.
    /// </summary>
    [UnityEngine.Scripting.APIUpdating.MovedFrom("Unity.Sentis")]
    public class Random
    {
        /// <summary>
        /// Static global System.Random used for random values when no seed provided
        /// </summary>
        static System.Random s_Random = new System.Random();

        /// <summary>
        /// Sets the global Inference Engine random state for random values without an explicit seed.
        /// </summary>
        /// <param name="seed">The seed to set the state to</param>
        public static void SetSeed(int seed)
        {
            s_Random = new System.Random(seed);
        }

        // Local System.Random used for random values when seed is provided
        System.Random m_Random;

        // Returns either local or global System.Random corresponding to given seed or not
        System.Random SystemRandom => m_Random ?? s_Random;

        internal Random() { }

        internal Random(int seed)
        {
            m_Random = new System.Random(new Seed { intSeed = seed }.intSeed);
        }

        // Returns float with random bytes to be used as seed for Random Op
        internal int NextSeed()
        {
            return new Seed { intSeed = SystemRandom.Next(int.MinValue, int.MaxValue) }.intSeed;
        }

        // Returns uint with random bytes to be used as seed inside Op and be passed to a job or as a seed for Mathematics.Random
        internal static uint GetSeed(int? seed)
        {
            return seed.HasValue ? new Seed { intSeed = seed.Value }.uintSeed : new Seed { intSeed = s_Random.Next(int.MinValue, int.MaxValue) }.uintSeed;
        }
    }
}
