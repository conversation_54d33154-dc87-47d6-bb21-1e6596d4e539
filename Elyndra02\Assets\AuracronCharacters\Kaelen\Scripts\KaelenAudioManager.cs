using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace AuracronCharacters
{
    /// <summary>
    /// Sistema de Áudio do Kaelen
    /// Gerencia todos os sons baseados no design document
    /// </summary>
    [System.Serializable]
    public class AudioClipGroup
    {
        public string name;
        public AudioClip[] clips;
        [Range(0f, 1f)] public float volume = 1f;
        [Range(0.1f, 3f)] public float pitch = 1f;
        [Range(0f, 1f)] public float pitchVariation = 0.1f;
        public bool loop = false;
        
        public AudioClip GetRandomClip()
        {
            if (clips == null || clips.Length == 0) return null;
            return clips[Random.Range(0, clips.Length)];
        }
    }
    
    public class KaelenAudioManager : MonoBehaviour
    {
        [Header("=== AUDIO SOURCES ===")]
        public AudioSource voiceAudioSource;
        public AudioSource sfxAudioSource;
        public AudioSource footstepsAudioSource;
        public AudioSource ambientAudioSource;
        public AudioSource musicAudioSource;
        
        [Header("=== MOVEMENT SOUNDS ===")]
        public AudioClipGroup footstepsGrass;
        public AudioClipGroup footstepsStone;
        public AudioClipGroup footstepsMetal;
        public AudioClipGroup footstepsWater;
        public AudioClipGroup armorClinks;
        public AudioClipGroup spearDrag;
        
        [Header("=== ABILITY SOUNDS ===")]
        public AudioClipGroup basicAttackSounds;
        public AudioClipGroup piercingChargeSounds;
        public AudioClipGroup etherealBarrierSounds;
        public AudioClipGroup huntersMarkSounds;
        public AudioClipGroup convergenceSounds;
        
        [Header("=== COMBAT SOUNDS ===")]
        public AudioClipGroup spearHitFlesh;
        public AudioClipGroup spearHitArmor;
        public AudioClipGroup spearHitShield;
        public AudioClipGroup spearBlock;
        public AudioClipGroup takeDamageSounds;
        public AudioClipGroup deathSounds;
        
        [Header("=== VOICE LINES ===")]
        public AudioClipGroup battleCries;
        public AudioClipGroup painSounds;
        public AudioClipGroup victorySounds;
        public AudioClipGroup tauntSounds;
        public AudioClipGroup abilityCalls;
        
        [Header("=== EMOTIONAL SOUNDS ===")]
        public AudioClipGroup determinedSounds;
        public AudioClipGroup angrySounds;
        public AudioClipGroup focusedSounds;
        public AudioClipGroup triumphantSounds;
        
        [Header("=== THEME MUSIC ===")]
        public AudioClip kaelenTheme;
        public AudioClip combatTheme;
        public AudioClip victoryTheme;
        
        [Header("=== AUDIO SETTINGS ===")]
        [Range(0f, 1f)] public float masterVolume = 1f;
        [Range(0f, 1f)] public float sfxVolume = 0.8f;
        [Range(0f, 1f)] public float voiceVolume = 0.9f;
        [Range(0f, 1f)] public float musicVolume = 0.6f;
        public float fadeInDuration = 1f;
        public float fadeOutDuration = 1f;
        
        // Audio state management
        private Dictionary<string, Coroutine> activeAudioCoroutines = new Dictionary<string, Coroutine>();
        private TerrainType currentTerrain = TerrainType.Grass;
        private bool isInCombat = false;
        private float lastFootstepTime = 0f;
        private float footstepInterval = 0.5f;
        
        // Component references
        private KaelenController kaelenController;
        private KaelenStats kaelenStats;
        private KaelenAnimationController animationController;
        
        void Start()
        {
            InitializeComponents();
            SetupAudioSources();
            SubscribeToEvents();
        }
        
        void Update()
        {
            UpdateFootsteps();
            UpdateCombatState();
        }
        
        private void InitializeComponents()
        {
            kaelenController = GetComponent<KaelenController>();
            kaelenStats = GetComponent<KaelenStats>();
            animationController = GetComponent<KaelenAnimationController>();
            
            // Create audio sources if they don't exist
            if (voiceAudioSource == null)
                voiceAudioSource = gameObject.AddComponent<AudioSource>();
            if (sfxAudioSource == null)
                sfxAudioSource = gameObject.AddComponent<AudioSource>();
            if (footstepsAudioSource == null)
                footstepsAudioSource = gameObject.AddComponent<AudioSource>();
            if (ambientAudioSource == null)
                ambientAudioSource = gameObject.AddComponent<AudioSource>();
            if (musicAudioSource == null)
                musicAudioSource = gameObject.AddComponent<AudioSource>();
        }
        
        private void SetupAudioSources()
        {
            // Configure voice audio source
            voiceAudioSource.volume = voiceVolume * masterVolume;
            voiceAudioSource.spatialBlend = 1f; // 3D sound
            voiceAudioSource.rolloffMode = AudioRolloffMode.Logarithmic;
            voiceAudioSource.maxDistance = 20f;
            
            // Configure SFX audio source
            sfxAudioSource.volume = sfxVolume * masterVolume;
            sfxAudioSource.spatialBlend = 1f;
            sfxAudioSource.rolloffMode = AudioRolloffMode.Logarithmic;
            sfxAudioSource.maxDistance = 15f;
            
            // Configure footsteps audio source
            footstepsAudioSource.volume = sfxVolume * masterVolume * 0.7f;
            footstepsAudioSource.spatialBlend = 1f;
            footstepsAudioSource.rolloffMode = AudioRolloffMode.Linear;
            footstepsAudioSource.maxDistance = 10f;
            
            // Configure music audio source
            musicAudioSource.volume = musicVolume * masterVolume;
            musicAudioSource.spatialBlend = 0f; // 2D sound
            musicAudioSource.loop = true;
        }
        
        private void SubscribeToEvents()
        {
            if (kaelenStats != null)
            {
                kaelenStats.OnDeath.AddListener(PlayDeathSound);
                kaelenStats.OnHealthChanged.AddListener(OnHealthChanged);
            }
        }
        
        #region MOVEMENT SOUNDS
        private void UpdateFootsteps()
        {
            if (kaelenStats.IsDead) return;
            
            // Check if character is moving
            CharacterController controller = GetComponent<CharacterController>();
            if (controller != null && controller.velocity.magnitude > 0.1f)
            {
                if (Time.time - lastFootstepTime > footstepInterval)
                {
                    PlayFootstepSound();
                    lastFootstepTime = Time.time;
                }
            }
        }
        
        public void PlayFootstepSound()
        {
            AudioClipGroup footstepGroup = GetFootstepGroupForTerrain(currentTerrain);
            PlayAudioClipGroup(footstepGroup, footstepsAudioSource);
            
            // Occasionally play armor clink
            if (Random.value < 0.3f)
            {
                StartCoroutine(PlayDelayedAudioClipGroup(armorClinks, sfxAudioSource, 0.1f));
            }
        }
        
        private AudioClipGroup GetFootstepGroupForTerrain(TerrainType terrain)
        {
            switch (terrain)
            {
                case TerrainType.Grass: return footstepsGrass;
                case TerrainType.Stone: return footstepsStone;
                case TerrainType.Metal: return footstepsMetal;
                case TerrainType.Water: return footstepsWater;
                default: return footstepsGrass;
            }
        }
        
        public void SetTerrain(TerrainType terrain)
        {
            currentTerrain = terrain;
            
            // Adjust footstep interval based on terrain
            switch (terrain)
            {
                case TerrainType.Water:
                    footstepInterval = 0.6f; // Slower in water
                    break;
                case TerrainType.Metal:
                    footstepInterval = 0.4f; // Faster on hard surfaces
                    break;
                default:
                    footstepInterval = 0.5f;
                    break;
            }
        }
        
        public void PlaySpearDragSound()
        {
            PlayAudioClipGroup(spearDrag, sfxAudioSource);
        }
        #endregion
        
        #region ABILITY SOUNDS
        public void PlayBasicAttackSound()
        {
            PlayAudioClipGroup(basicAttackSounds, sfxAudioSource);
            
            // Random battle cry
            if (Random.value < 0.2f)
            {
                StartCoroutine(PlayDelayedAudioClipGroup(battleCries, voiceAudioSource, 0.2f));
            }
        }
        
        public void PlayPiercingChargeSound()
        {
            PlayAudioClipGroup(piercingChargeSounds, sfxAudioSource);
            
            // Ability call
            PlayAbilityCall("Investida Perfurante!");
        }
        
        public void PlayEtherealBarrierSound()
        {
            PlayAudioClipGroup(etherealBarrierSounds, sfxAudioSource);
            
            // Ability call
            PlayAbilityCall("Barreira Etérea!");
        }
        
        public void PlayHuntersMarkSound()
        {
            PlayAudioClipGroup(huntersMarkSounds, sfxAudioSource);
            
            // Ability call
            PlayAbilityCall("Marca do Caçador!");
        }
        
        public void PlayConvergenceSound()
        {
            PlayAudioClipGroup(convergenceSounds, sfxAudioSource);
            
            // Ultimate call
            PlayAbilityCall("Convergência das Lâminas!");
        }
        
        private void PlayAbilityCall(string abilityName)
        {
            // Play specific ability voice line
            PlayAudioClipGroup(abilityCalls, voiceAudioSource);
            Debug.Log($"Kaelen grita: {abilityName}");
        }
        #endregion
        
        #region COMBAT SOUNDS
        public void PlayHitSound(HitType hitType)
        {
            AudioClipGroup hitGroup = null;
            
            switch (hitType)
            {
                case HitType.Flesh:
                    hitGroup = spearHitFlesh;
                    break;
                case HitType.Armor:
                    hitGroup = spearHitArmor;
                    break;
                case HitType.Shield:
                    hitGroup = spearHitShield;
                    break;
            }
            
            if (hitGroup != null)
            {
                PlayAudioClipGroup(hitGroup, sfxAudioSource);
            }
        }
        
        public void PlayBlockSound()
        {
            PlayAudioClipGroup(spearBlock, sfxAudioSource);
        }
        
        public void PlayTakeDamageSound()
        {
            PlayAudioClipGroup(takeDamageSounds, voiceAudioSource);
            PlayAudioClipGroup(painSounds, voiceAudioSource);
        }
        
        public void PlayDeathSound()
        {
            PlayAudioClipGroup(deathSounds, voiceAudioSource);
            
            // Stop all other sounds
            StopAllSounds();
        }
        #endregion
        
        #region EMOTIONAL SOUNDS
        public void PlayEmotionalSound(EmotionType emotion)
        {
            AudioClipGroup emotionGroup = null;
            
            switch (emotion)
            {
                case EmotionType.Determined:
                    emotionGroup = determinedSounds;
                    break;
                case EmotionType.Angry:
                    emotionGroup = angrySounds;
                    break;
                case EmotionType.Focused:
                    emotionGroup = focusedSounds;
                    break;
                case EmotionType.Triumphant:
                    emotionGroup = triumphantSounds;
                    break;
            }
            
            if (emotionGroup != null)
            {
                PlayAudioClipGroup(emotionGroup, voiceAudioSource);
            }
        }
        
        public void PlayVictorySound()
        {
            PlayAudioClipGroup(victorySounds, voiceAudioSource);
            PlayEmotionalSound(EmotionType.Triumphant);
        }
        
        public void PlayTauntSound()
        {
            PlayAudioClipGroup(tauntSounds, voiceAudioSource);
        }
        #endregion
        
        #region MUSIC SYSTEM
        public void PlayThemeMusic()
        {
            if (kaelenTheme != null)
            {
                FadeToMusic(kaelenTheme);
            }
        }
        
        public void PlayCombatMusic()
        {
            if (combatTheme != null && !isInCombat)
            {
                FadeToMusic(combatTheme);
                isInCombat = true;
            }
        }
        
        public void PlayVictoryMusic()
        {
            if (victoryTheme != null)
            {
                FadeToMusic(victoryTheme);
            }
        }
        
        public void StopMusic()
        {
            StartCoroutine(FadeOutMusic());
        }
        
        private void FadeToMusic(AudioClip newClip)
        {
            StartCoroutine(FadeToMusicCoroutine(newClip));
        }
        
        private IEnumerator FadeToMusicCoroutine(AudioClip newClip)
        {
            // Fade out current music
            yield return StartCoroutine(FadeOutMusic());
            
            // Set new clip and fade in
            musicAudioSource.clip = newClip;
            musicAudioSource.Play();
            yield return StartCoroutine(FadeInMusic());
        }
        
        private IEnumerator FadeOutMusic()
        {
            float startVolume = musicAudioSource.volume;
            float elapsedTime = 0f;
            
            while (elapsedTime < fadeOutDuration)
            {
                elapsedTime += Time.deltaTime;
                musicAudioSource.volume = Mathf.Lerp(startVolume, 0f, elapsedTime / fadeOutDuration);
                yield return null;
            }
            
            musicAudioSource.Stop();
        }
        
        private IEnumerator FadeInMusic()
        {
            float targetVolume = musicVolume * masterVolume;
            float elapsedTime = 0f;
            
            musicAudioSource.volume = 0f;
            
            while (elapsedTime < fadeInDuration)
            {
                elapsedTime += Time.deltaTime;
                musicAudioSource.volume = Mathf.Lerp(0f, targetVolume, elapsedTime / fadeInDuration);
                yield return null;
            }
        }
        #endregion
        
        #region UTILITY METHODS
        private void PlayAudioClipGroup(AudioClipGroup clipGroup, AudioSource audioSource)
        {
            if (clipGroup == null || audioSource == null) return;
            
            AudioClip clip = clipGroup.GetRandomClip();
            if (clip == null) return;
            
            audioSource.clip = clip;
            audioSource.volume = clipGroup.volume * GetVolumeForSource(audioSource) * masterVolume;
            audioSource.pitch = clipGroup.pitch + Random.Range(-clipGroup.pitchVariation, clipGroup.pitchVariation);
            audioSource.loop = clipGroup.loop;
            audioSource.Play();
        }
        
        private IEnumerator PlayDelayedAudioClipGroup(AudioClipGroup clipGroup, AudioSource audioSource, float delay)
        {
            yield return new WaitForSeconds(delay);
            PlayAudioClipGroup(clipGroup, audioSource);
        }
        
        private float GetVolumeForSource(AudioSource source)
        {
            if (source == voiceAudioSource) return voiceVolume;
            if (source == musicAudioSource) return musicVolume;
            return sfxVolume;
        }
        
        private void UpdateCombatState()
        {
            // Simple combat detection - can be improved
            bool wasInCombat = isInCombat;
            
            // Exit combat after some time without abilities
            if (isInCombat && animationController != null && !animationController.IsPlayingAbilityAnimation())
            {
                // Add timer logic here
                isInCombat = false;
            }
            
            // Transition music
            if (wasInCombat && !isInCombat)
            {
                PlayThemeMusic();
            }
        }
        
        private void OnHealthChanged()
        {
            if (kaelenStats != null)
            {
                float healthPercentage = kaelenStats.GetHealthPercentage();
                
                // Play pain sounds when health is low
                if (healthPercentage < 0.3f && Random.value < 0.1f)
                {
                    PlayAudioClipGroup(painSounds, voiceAudioSource);
                }
            }
        }
        
        public void StopAllSounds()
        {
            voiceAudioSource.Stop();
            sfxAudioSource.Stop();
            footstepsAudioSource.Stop();
            ambientAudioSource.Stop();
        }
        
        public void SetMasterVolume(float volume)
        {
            masterVolume = Mathf.Clamp01(volume);
            UpdateAllVolumes();
        }
        
        public void SetSFXVolume(float volume)
        {
            sfxVolume = Mathf.Clamp01(volume);
            UpdateAllVolumes();
        }
        
        public void SetVoiceVolume(float volume)
        {
            voiceVolume = Mathf.Clamp01(volume);
            UpdateAllVolumes();
        }
        
        public void SetMusicVolume(float volume)
        {
            musicVolume = Mathf.Clamp01(volume);
            UpdateAllVolumes();
        }
        
        private void UpdateAllVolumes()
        {
            voiceAudioSource.volume = voiceVolume * masterVolume;
            sfxAudioSource.volume = sfxVolume * masterVolume;
            footstepsAudioSource.volume = sfxVolume * masterVolume * 0.7f;
            musicAudioSource.volume = musicVolume * masterVolume;
        }
        #endregion
    }
    
    public enum TerrainType
    {
        Grass,
        Stone,
        Metal,
        Water,
        Sand,
        Wood
    }
    
    public enum HitType
    {
        Flesh,
        Armor,
        Shield
    }
    
    public enum EmotionType
    {
        Determined,
        Angry,
        Focused,
        Triumphant
    }
}