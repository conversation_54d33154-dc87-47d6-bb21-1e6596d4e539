using UnityEngine;
using UnityEditor;
using UnityEngine.Rendering;

public class LightingTestComponent : MonoBehaviour
{
    void Start()
    {
        Debug.LogError("[AUTO TEST] LightingTestComponent started");
        TestLightingSettings();
    }
    
    public void TestLightingSettings()
    {
        Debug.LogError("[AUTO TEST] Starting LightingSettings test");
        
        try
        {
            // Try to access current LightingSettings
            var current = Lightmapping.lightingSettings;
            Debug.LogError($"[AUTO TEST] Current LightingSettings: {(current == null ? "NULL" : "EXISTS")}");
            
            if (current == null)
            {
                Debug.LogError("[AUTO TEST] Creating new LightingSettings");
                
                // Create new LightingSettings
                var newSettings = new LightingSettings();
                Debug.LogError("[AUTO TEST] LightingSettings created successfully");
                
                // Try to assign it
                Lightmapping.lightingSettings = newSettings;
                Debug.LogError("[AUTO TEST] LightingSettings assigned successfully");
                
                // Verify assignment
                var verify = Lightmapping.lightingSettings;
                Debug.LogError($"[AUTO TEST] Verification: {(verify == null ? "FAILED" : "SUCCESS")}");
            }
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"[AUTO TEST] Exception: {ex.Message}");
            Debug.LogError($"[AUTO TEST] Stack trace: {ex.StackTrace}");
        }
    }
}