# Regras para Unity MCP no Trae IDE

**Data de Criação:** 30/12/2024  
**Última Atualização:** 29/06/2025  
**Versão:** 2.0  
**Compatibilidade:** Unity 6.2 + Trae IDE

## 📋 Visão Geral

Este documento define as regras para determinar quando usar **criação manual de arquivos** versus **servidor MCP Unity** no Trae IDE. Seguir estas regras garante eficiência e evita erros de implementação.

## ⚠️ ALERTAS CRÍTICOS - UNITY 6.2 (2025)

### 🚫 PACOTES E TECNOLOGIAS DEPRECATED
- **❌ Sentis:** DEPRECATED - Use **Inference Engine** obrigatoriamente
- **❌ Unity Lobby:** DEPRECATED - Migrado para **Multiplayer Package**
- **❌ Unity Relay:** DEPRECATED - Migrado para **Multiplayer Package**
- **❌ Netcode for GameObjects (antigo):** Use versão integrada no Multiplayer Package

### 🔄 REGRA DE ATUALIZAÇÃO OBRIGATÓRIA
- **📅 Data Atual:** 29 de Junho de 2025
- **🔍 SEMPRE verificar:** Pacotes, classes e funções atualizadas
- **🚫 NUNCA usar:** APIs deprecated ou pacotes obsoletos
- **🔄 RENOVAR CONTEXTO:** Quando tokens baixos ou contexto expirando
- **📖 RECONHECER CÓDIGO:** Analisar código existente antes de implementar

---

## 🔧 REGRA PRINCIPAL: Criação Manual vs MCP

### ✅ CRIAR MANUALMENTE (Sem Unity aberto)

**Arquivos que podem ser criados diretamente nas pastas:**

#### Scripts C#
- ✅ `*.cs` - Scripts MonoBehaviour, ScriptableObject, classes utilitárias
- ✅ `*.asmdef` - Assembly Definition files
- ✅ Estruturas de pastas para organização

#### Arquivos de Configuração
- ✅ `*.json` - Arquivos de configuração, dados de jogo
- ✅ `*.xml` - Configurações, metadados
- ✅ `*.txt` - Documentação, logs, dados simples
- ✅ `*.md` - Documentação Markdown
- ✅ `*.yaml`/`*.yml` - Configurações YAML

#### Assets Simples
- ✅ `*.shader` - Shaders HLSL/CG
- ✅ `*.compute` - Compute Shaders
- ✅ `*.cginc` - Include files para shaders

#### Outros
- ✅ Arquivos de build/CI/CD
- ✅ Configurações de projeto externas
- ✅ Scripts de automação

### 🎮 USAR MCP SERVER (Unity 6.2 deve estar aberto)

**Assets que requerem Unity Editor:**

#### Assets Unity Nativos
- 🎮 `*.asset` - ScriptableObjects, configurações Unity
- 🎮 `*.prefab` - Prefabs e variantes
- 🎮 `*.unity` - Cenas Unity
- 🎮 `*.mat` - Materiais
- 🎮 `*.physicMaterial` - Materiais de física
- 🎮 `*.mixer` - Audio Mixers
- 🎮 `*.controller` - Animator Controllers
- 🎮 `*.mask` - Avatar Masks
- 🎮 `*.anim` - Animation Clips
- 🎮 `*.playable` - Timeline assets

#### Operações Runtime
- 🎮 Configuração de GameObjects na cena
- 🎮 Modificação de componentes em runtime
- 🎮 Baking de iluminação
- 🎮 Geração de NavMesh
- 🎮 Configuração de sistemas complexos (Audio, Physics, etc.)

---

## 🎯 REGRA DE SELEÇÃO DE FERRAMENTAS MCP

### 📚 OBRIGATÓRIO: Consultar Documentação

**SEMPRE que usar MCP Server:**

1. **🔍 LEIA PRIMEIRO:** `c:\ultimo\ferramentas-mcp.md`
2. **❓ EM DÚVIDA:** Consulte a documentação antes de escolher
3. **🚫 NÃO ADIVINHE:** Nunca escolha ferramenta sem certeza
4. **🔄 VERIFICAR ATUALIZAÇÃO:** Confirmar se pacotes/APIs não estão deprecated
5. **🧠 USAR INFERENCE ENGINE:** Obrigatório para IA (Sentis deprecated)
6. **📦 MULTIPLAYER PACKAGE:** Para Lobby, Relay e Netcode

### 🗂️ Categorias de Ferramentas MCP

#### 🎵 Audio System (16 ferramentas)
```
- create_audio_mixer → Audio Mixers
- setup_spatial_audio → Áudio 3D
- create_reverb_zones → Zonas de reverb
- setup_audio_occlusion → Oclusão de som
- create_dynamic_music → Música adaptativa
- setup_audio_filters → Filtros de áudio
- create_audio_snapshots → Snapshots de mixer
- setup_voice_chat → Chat de voz
- create_audio_triggers → Gatilhos de áudio
- setup_audio_streaming → Streaming de áudio
- create_procedural_audio → Áudio procedural
- setup_audio_compression → Compressão de áudio
- create_audio_effects_chain → Cadeia de efeitos
- setup_binaural_audio → Áudio binaural
- create_audio_visualization → Visualização de áudio
- optimize_audio_performance → Otimização de áudio
```

#### ⚡ Physics System (18 ferramentas)
```
- create_rigidbody_system → Física de corpos rígidos
- setup_joint_systems → Juntas físicas
- create_cloth_simulation → Simulação de tecidos
- setup_fluid_simulation → Simulação de fluidos
- create_destruction_system → Sistema de destruição
- setup_vehicle_physics → Física de veículos
- create_rope_physics → Física de cordas
- setup_wind_system → Sistema de vento
- create_gravity_zones → Zonas de gravidade
- setup_collision_layers → Camadas de colisão
- create_trigger_systems → Sistemas de gatilhos
- setup_physics_materials → Materiais físicos
- create_particle_physics → Física de partículas
- setup_soft_body_physics → Física de corpos moles
- create_magnetic_fields → Campos magnéticos
- setup_buoyancy_system → Sistema de flutuabilidade
- create_physics_constraints → Restrições físicas
- optimize_physics_performance → Otimização de física
```

#### 💡 Lighting & Rendering (20 ferramentas)
```
- setup_global_illumination → GI baking
- create_light_probes → Light Probes
- setup_reflection_probes → Reflection Probes
- create_lightmaps → Lightmaps
- setup_realtime_gi → GI em tempo real
- create_volumetric_lighting → Iluminação volumétrica
- setup_shadow_cascades → Cascatas de sombra
- create_light_cookies → Light cookies
- setup_hdr_pipeline → Pipeline HDR
- create_post_processing → Pós-processamento
- setup_bloom_effects → Efeitos de bloom
- create_color_grading → Correção de cor
- setup_depth_of_field → Profundidade de campo
- create_motion_blur → Desfoque de movimento
- setup_screen_space_reflections → SSR
- create_ambient_occlusion → Oclusão de ambiente
- setup_fog_effects → Efeitos de neblina
- create_caustics → Efeitos de cáustica
- setup_light_shafts → Raios de luz
- optimize_rendering_performance → Otimização de renderização
```

#### 🌍 Terrain & Environment (14 ferramentas)
```
- create_terrain → Criação de terreno
- paint_terrain_textures → Pintura de texturas
- create_terrain_trees → Árvores no terreno
- setup_terrain_grass → Grama no terreno
- create_terrain_details → Detalhes do terreno
- setup_terrain_collision → Colisão do terreno
- create_water_system → Sistema de água
- setup_weather_system → Sistema de clima
- create_day_night_cycle → Ciclo dia/noite
- setup_environment_zones → Zonas ambientais
- create_biome_system → Sistema de biomas
- setup_procedural_caves → Cavernas procedurais
- create_landmark_generation → Geração de marcos
- optimize_terrain_performance → Otimização de terreno
```

#### 🎮 Multiplayer & Networking (16 ferramentas - Usar pacote Multiplayer)
```
- configure_multiplayer_netcode → Netcode setup
- setup_unity_gaming_services → Unity Gaming Services
- start_multiplayer_host → Iniciar servidor
- connect_to_multiplayer_server → Conectar cliente
- join_multiplayer_lobby → Entrar em lobby
- get_multiplayer_status → Status da conexão
- get_connected_players → Jogadores conectados
- sync_player_data → Sincronizar dados
- send_multiplayer_message → Mensagens RPC
- setup_voice_chat → Chat de voz
- configure_anti_cheat → Anti-cheat
- setup_network_culling → Network culling
- configure_lag_compensation → Compensação de lag
- setup_dedicated_servers → Servidores dedicados
- monitor_network_performance → Performance de rede
- optimize_bandwidth_usage → Otimização de banda
```

#### 🤖 AI Runtime Operations (8 ferramentas - Usar Inference Engine)
```
- setup_navmesh_system → NavMesh baking
- setup_pathfinding → Pathfinding
- setup_crowd_simulation → Simulação de multidões
- setup_machine_learning → ML-Agents
- setup_adaptive_difficulty → Dificuldade adaptativa
- create_ai_perception → Percepção de IA
- setup_ai_communication → Comunicação de IA
- setup_ai_optimization → Otimização de IA
```

#### 🎬 Visual Effects & Particles (14 ferramentas)
```
- create_particle_systems → Sistemas de partículas
- setup_vfx_graph → VFX Graph
- create_fire_effects → Efeitos de fogo
- setup_water_effects → Efeitos de água
- create_explosion_effects → Efeitos de explosão
- setup_magic_effects → Efeitos mágicos
- create_weather_effects → Efeitos de clima
- setup_environmental_effects → Efeitos ambientais
- create_ui_effects → Efeitos de UI
- setup_screen_effects → Efeitos de tela
- create_trail_effects → Efeitos de rastro
- setup_distortion_effects → Efeitos de distorção
- create_hologram_effects → Efeitos de holograma
- optimize_vfx_performance → Otimização de VFX
```

#### 📊 Performance & Profiling (8 ferramentas)
```
- analyze_performance_profile → Análise de performance
- monitor_runtime_performance → Monitoramento runtime
- optimize_memory_usage → Otimização de memória
- optimize_rendering_pipeline → Pipeline de renderização
- optimize_assets → Otimização de assets
- optimize_build_size → Tamanho do build
- setup_performance_budgets → Orçamentos de performance
- configure_adaptive_quality → Qualidade adaptativa
```

#### 🎭 Animation Runtime (7 ferramentas)
```
- setup_ik_systems → Sistemas IK
- create_facial_animation → Animação facial
- setup_motion_matching → Motion matching
- setup_timeline_sequences → Sequências Timeline
- create_cinemachine_setup → Setup Cinemachine
- create_ragdoll_physics → Física ragdoll
- setup_motion_capture → Captura de movimento
```

#### 🥽 XR Runtime Operations (18 ferramentas)
```
- setup_xr_toolkit → XR Toolkit
- create_vr_player_rig → Rig de jogador VR
- setup_hand_tracking → Rastreamento de mãos
- create_ar_foundation_setup → AR Foundation
- setup_eye_tracking → Rastreamento ocular
- create_spatial_anchors → Âncoras espaciais
- setup_passthrough_ar → Passthrough AR
- create_mixed_reality_setup → Setup MR
- setup_haptic_feedback → Feedback háptico
- create_xr_ui_system → Sistema UI XR
- setup_room_scale_tracking → Rastreamento de sala
- create_teleportation_system → Sistema de teletransporte
- setup_comfort_settings → Configurações de conforto
- create_xr_input_system → Sistema de entrada XR
- setup_cross_platform_xr → XR multiplataforma
- create_ar_occlusion → Oclusão AR
- setup_xr_performance_optimization → Otimização XR
- create_xr_analytics → Analytics XR
```

#### 🔧 Asset Processing Runtime (6 ferramentas)
```
- generate_particle_system → Gerar sistema de partículas
- create_texture_atlas → Atlas de texturas
- create_mesh_colliders → Mesh Colliders
- generate_lod_meshes → Malhas LOD
- create_uv_mappings → Mapeamento UV
- optimize_asset_pipeline → Pipeline de assets
```

#### 📁 Project & Code Operations (5 ferramentas)
```
- setup_project_dependencies → Dependências do projeto
- configure_build_pipeline → Pipeline de build
- validate_project_integrity → Integridade do projeto
- optimize_existing_code → Otimizar código
- refactor_code_structure → Refatorar estrutura
```

#### 🧪 Unity 6.2 & Testing (3 ferramentas)
```
- setup_unity_ai → Unity AI
- run_unity_test_suite → Test Runner
- configure_cloud_testing → Testes na nuvem
```

#### 🎨 AI-Powered Asset Generation (10 ferramentas)
```
- generate_ai_texture → Texturas com IA
- generate_ai_sprite → Sprites com IA
- generate_ai_material → Materiais PBR com IA
- generate_ai_animation → Animações com IA
- generate_ai_sound → Sons com IA
- configure_ai_generator_styles → Estilos de IA
- create_texture_variations_ai → Variações de textura
- setup_ai_asset_pipeline → Pipeline de assets IA
- generate_material_from_texture_ai → Material de textura IA
- refine_ai_generated_assets → Refinamento de assets IA
```

#### 🏗️ Procedural Generation (9 ferramentas)
```
- generate_procedural_mesh → Malhas procedurais
- create_procedural_terrain_system → Terreno procedural
- setup_procedural_vegetation → Vegetação procedural
- generate_mesh_from_heightmap → Malha de heightmap
- create_procedural_building_generator → Gerador de edifícios
- setup_geometry_scripting_runtime → Scripting de geometria
- generate_l_system_structures → Estruturas L-System
- create_procedural_road_network → Rede de estradas
- optimize_procedural_geometry → Otimização procedural
```

#### 🎭 Procedural Character Assembly (8 ferramentas)
```
- assemble_character_from_parts → Montagem de personagem
- apply_generated_textures_to_character → Texturas em personagem
- setup_procedural_rigging → Rigging procedural
- create_character_animation_controller → Controller de animação
- generate_character_variations → Variações de personagem
- setup_character_lods_procedural → LODs procedurais
- create_character_customization_system → Sistema de customização
- export_assembled_character_prefab → Exportar prefab
```

#### 🎮 Dynamic Gameplay Systems (9 ferramentas)
```
- create_generative_state_machine → State Machine generativa
- attach_behaviour_script_to_state → Scripts de comportamento
- setup_procedural_event_system → Sistema de eventos
- generate_gameplay_rules_engine → Motor de regras
- create_dynamic_skill_tree → Árvore de habilidades
- setup_buff_debuff_system → Sistema de buffs/debuffs
- generate_ai_behaviour_tree → Behaviour Tree IA
- configure_game_state_manager → Gerenciador de estado
- create_objective_tracker_system → Rastreador de objetivos
```

#### 📖 Procedural Narrative & Quest Generation (9 ferramentas)
```
- generate_quest_state_machine → State Machine de quest
- create_quest_objective_generator → Gerador de objetivos
- setup_sentis_dialogue_generator → Gerador de diálogos
- generate_branching_dialogue_tree → Árvore de diálogo
- create_narrative_event_chain → Cadeia de eventos
- setup_dynamic_lore_system → Sistema de lore
- generate_npc_backstory → História de NPCs
- create_quest_reward_generator → Gerador de recompensas
- link_quests_to_gameplay_events → Vincular quests a eventos
```

#### 💰 Generative Game Economy & Balancing (8 ferramentas)
```
- generate_item_database_procedural → Banco de dados de itens
- create_dynamic_shop_inventory → Inventário de loja
- setup_procedural_loot_tables → Tabelas de loot
- simulate_supply_and_demand → Oferta e demanda
- balance_skill_costs_and_effects → Balanceamento de habilidades
- generate_enemy_stat_curves → Curvas de stats
- create_economy_event_simulator → Simulador econômico
- analyze_gameplay_data_for_balancing → Análise para balanceamento
```

#### 🌍 Dynamic World Simulation (7 ferramentas)
```
- create_faction_simulation_system → Simulação de facções
- setup_dynamic_event_spawner → Spawner de eventos
- simulate_npc_daily_routines → Rotinas de NPCs
- create_simulated_ecosystem → Ecossistema simulado
- setup_world_state_change_system → Mudanças de estado
- generate_dynamic_points_of_interest → Pontos de interesse
- create_weather_and_calendar_simulation → Clima e calendário
```

#### 🎬 Automated Cinematic Generation (7 ferramentas)
```
- generate_timeline_from_events → Timeline de eventos
- setup_cinemachine_procedural_camera → Câmeras procedurais
- create_dynamic_shot_list → Lista de planos
- sequence_generated_animations_on_timeline → Sequenciar animações
- add_generated_audio_to_timeline → Áudio na Timeline
- trigger_cinematic_from_gameplay → Disparar cinemática
- control_post_processing_on_timeline → Pós-processamento na Timeline
```

#### 🤖 Unity AI Assistant Integration (8 ferramentas)
```
- setup_ai_assistant → Configurar AI Assistant
- generate_code_with_assistant → Gerar código
- troubleshoot_with_assistant → Resolver problemas
- automate_tasks_with_assistant → Automatizar tarefas
- scene_editing_with_assistant → Edição de cena
- asset_management_with_assistant → Gerenciamento de assets
- project_analysis_with_assistant → Análise de projeto
- assistant_workflow_automation → Automação de workflow
```

#### 🧠 Sentis Neural Network Runtime (8 ferramentas)
```
- setup_sentis_frame_slicing → Frame-slicing Sentis
- create_sentis_quantization_system → Quantização Sentis
- setup_sentis_backend_selection → Seleção de backend
- configure_sentis_tensor_operations → Operações de tensor
- setup_sentis_memory_optimization → Otimização de memória
- create_sentis_model_visualization → Visualização de modelo
- setup_sentis_async_inference → Inferência assíncrona
- configure_sentis_onnx_import → Importação ONNX
```

#### 🔧 Advanced Systems (Mais de 50 ferramentas adicionais)
```
- WebGPU Runtime Features (5 ferramentas)
- Advanced Rendering Features (12 ferramentas)
- Ray Tracing Operations (6 ferramentas)
- Advanced AI Runtime (8 ferramentas)
- Unity 6.2 Platform Extensions (6 ferramentas)
- Mixed Reality Enhanced (4 ferramentas)
- UI Toolkit Runtime (8 ferramentas)
- Project Auditor Runtime (8 ferramentas)
- 2D Toolkit Runtime (5 ferramentas)
- SRP Core Extensions (5 ferramentas)
- Input System Runtime (7 ferramentas)
- Navigation & Pathfinding Enhanced (6 ferramentas)
- Water System Runtime (8 ferramentas)
- Variable Rate Shading Runtime (6 ferramentas)
- Deferred+ Rendering System (6 ferramentas)
- Graphics State Collection (6 ferramentas)
- Bicubic Lightmap System (5 ferramentas)
- VFX & Shader Graph Runtime Control (8 ferramentas)
- Advanced Editor & Build Tools (7 ferramentas)
- Android Runtime Controls (6 ferramentas)
- Generative 2D World Building (7 ferramentas)
- Unity Behavior AI Generation (7 ferramentas)
- Unity Cloud AI Integration (6 ferramentas)
```

---

## 🚨 REGRAS DE EMERGÊNCIA

### ❌ O que NUNCA fazer:
- ❌ Usar MCP para criar scripts C# simples
- ❌ Usar criação manual para assets Unity (.prefab, .mat, etc.)
- ❌ Escolher ferramenta MCP sem consultar documentação
- ❌ Misturar abordagens no mesmo workflow
- ❌ **USAR SENTIS** (deprecated - use Inference Engine)
- ❌ **USAR LOBBY/RELAY SEPARADOS** (migrados para Multiplayer Package)
- ❌ **IMPLEMENTAR CÓDIGO DEPRECATED** sem verificar atualizações
- ❌ **CONTINUAR COM CONTEXTO EXPIRADO** (renovar obrigatoriamente)

### ✅ O que SEMPRE fazer:
- ✅ Verificar se Unity está aberto antes de usar MCP
- ✅ Consultar `ferramentas-mcp.md` quando em dúvida
- ✅ Usar criação manual para arquivos de texto/código
- ✅ Usar MCP para operações que requerem Unity Editor
- ✅ **USAR INFERENCE ENGINE** para IA (nunca Sentis)
- ✅ **USAR MULTIPLAYER PACKAGE** para networking
- ✅ **VERIFICAR CÓDIGO EXISTENTE** antes de implementar
- ✅ **RENOVAR CONTEXTO** quando tokens baixos
- ✅ **PADRONIZAR COM CÓDIGO EXISTENTE** no projeto

---

## 🔄 Fluxo de Decisão

```mermaid
graph TD
    A[Preciso criar/modificar algo] --> B{É arquivo de código/texto?}
    B -->|Sim| C[Criar Manualmente]
    B -->|Não| D{É asset Unity ou operação runtime?}
    D -->|Sim| E{Unity 6.2 está aberto?}
    E -->|Sim| F[Consultar ferramentas-mcp.md]
    E -->|Não| G[Abrir Unity primeiro]
    F --> H[Usar MCP Server]
    D -->|Não| I[Avaliar caso específico]
```

---

## 📝 Exemplos Práticos

### ✅ Cenário 1: Criar script de movimento
**Situação:** Preciso de um script PlayerController.cs  
**Decisão:** Criação Manual  
**Motivo:** Script C# pode ser criado diretamente

### 🎮 Cenário 2: Configurar sistema de áudio
**Situação:** Preciso configurar Audio Mixer com grupos  
**Decisão:** MCP Server (`create_audio_mixer`)  
**Motivo:** Audio Mixer é asset Unity nativo

### ✅ Cenário 3: Criar shader personalizado
**Situação:** Preciso de um shader HLSL  
**Decisão:** Criação Manual  
**Motivo:** Arquivo .shader pode ser criado como texto

### 🎮 Cenário 4: Setup de iluminação
**Situação:** Preciso fazer baking de GI  
**Decisão:** MCP Server (`setup_global_illumination`)  
**Motivo:** Operação que requer Unity Editor

---

## 🎯 Resumo das Regras (Unity 6.2 - 2025)

1. **📁 Arquivos de código/texto** → Criação Manual
2. **🎮 Assets Unity/Operações Runtime** → MCP Server
3. **📚 Sempre consultar** `ferramentas-mcp.md` antes de usar MCP
4. **🔍 Em dúvida?** → Leia a documentação
5. **⚡ Unity deve estar aberto** para usar MCP Server
6. **🧠 IA = Inference Engine** (Sentis deprecated)
7. **🌐 Multiplayer = Multiplayer Package** (Lobby/Relay deprecated)
8. **🔄 Renovar contexto** quando necessário
9. **📖 Analisar código existente** antes de implementar
10. **⚠️ Verificar deprecated** sempre

---

**💡 Lembre-se:** Estas regras garantem eficiência e evitam erros. Quando seguidas corretamente, resultam em 100% de sucesso na implementação!

**📞 Suporte:** Para dúvidas, consulte sempre `c:\ultimo\ferramentas-mcp.md`