using UnityEngine;
using System.Collections.Generic;

public class SimpleTwoDToolkit : MonoBehaviour
{
    [<PERSON><PERSON>("2D Settings")]
    public Camera mainCamera;
    public LayerMask interactionLayers = -1;
    
    [<PERSON><PERSON>("Sprite Management")]
    public List<SpriteRenderer> sprites = new List<SpriteRenderer>();
    
    void Start()
    {
        if (mainCamera == null)
            mainCamera = Camera.main;
            
        Debug.Log("Simple 2D Toolkit initialized!");
    }
    
    void Update()
    {
        HandleInput();
    }
    
    void HandleInput()
    {
        if (Input.GetMouseButtonDown(0))
        {
            Vector3 mousePos = Input.mousePosition;
            Vector3 worldPos = mainCamera.ScreenToWorldPoint(mousePos);
            worldPos.z = 0f;
            
            RaycastHit2D hit = Physics2D.Raycast(worldPos, Vector2.zero);
            if (hit.collider != null)
            {
                Debug.Log($"Clicked: {hit.collider.name}");
            }
        }
    }
    
    public void SetSpriteAlpha(SpriteRenderer sprite, float alpha)
    {
        if (sprite != null)
        {
            Color color = sprite.color;
            color.a = alpha;
            sprite.color = color;
        }
    }
    
    public void ScaleSprite(SpriteRenderer sprite, Vector3 scale)
    {
        if (sprite != null)
        {
            sprite.transform.localScale = scale;
        }
    }
}