using UnityEngine;
using UnityEngine.XR;
using UnityEngine.XR.Management;

public class AdvancedXRManager : MonoBehaviour
{
    [Header("XR Configuration")]
    public bool enableXR = true;
    public bool enableHandTracking = false;
    public bool enableEyeTracking = false;
    
    [Header("Performance Settings")]
    public int targetFrameRate = 90;
    public bool enableFoveatedRendering = false;
    
    private bool xrInitialized = false;
    
    void Start()
    {
        InitializeXR();
    }
    
    void InitializeXR()
    {
        if (!enableXR)
        {
            Debug.Log("XR disabled in settings");
            return;
        }
        
        // Check if XR is supported
        if (XRSettings.enabled)
        {
            xrInitialized = true;
            ConfigureXRSettings();
            Debug.Log("XR initialized successfully");
        }
        else
        {
            Debug.LogWarning("XR not available or not enabled");
        }
    }
    
    void ConfigureXRSettings()
    {
        // Set target frame rate
        Application.targetFrameRate = targetFrameRate;
        
        // Configure rendering settings
        if (enableFoveatedRendering)
        {
            // Enable foveated rendering if supported
            Debug.Log("Foveated rendering enabled");
        }
        
        // Configure tracking features
        ConfigureTracking();
    }
    
    void ConfigureTracking()
    {
        if (enableHandTracking)
        {
            Debug.Log("Hand tracking configuration enabled");
        }
        
        if (enableEyeTracking)
        {
            Debug.Log("Eye tracking configuration enabled");
        }
    }
    
    void Update()
    {
        if (!xrInitialized) return;
        
        // Update XR input and tracking
        UpdateXRInput();
    }
    
    void UpdateXRInput()
    {
        // Get head pose
        if (InputDevices.GetDeviceAtXRNode(XRNode.Head).TryGetFeatureValue(CommonUsages.devicePosition, out Vector3 headPosition))
        {
            // Process head position
        }
        
        if (InputDevices.GetDeviceAtXRNode(XRNode.Head).TryGetFeatureValue(CommonUsages.deviceRotation, out Quaternion headRotation))
        {
            // Process head rotation
        }
        
        // Get controller input
        UpdateControllerInput(XRNode.LeftHand);
        UpdateControllerInput(XRNode.RightHand);
    }
    
    void UpdateControllerInput(XRNode hand)
    {
        InputDevice device = InputDevices.GetDeviceAtXRNode(hand);
        
        if (device.TryGetFeatureValue(CommonUsages.triggerButton, out bool triggerPressed))
        {
            if (triggerPressed)
            {
                Debug.Log($"{hand} trigger pressed");
            }
        }
        
        if (device.TryGetFeatureValue(CommonUsages.gripButton, out bool gripPressed))
        {
            if (gripPressed)
            {
                Debug.Log($"{hand} grip pressed");
            }
        }
    }
    
    public void ToggleXR()
    {
        enableXR = !enableXR;
        
        if (enableXR)
        {
            InitializeXR();
        }
        else
        {
            DisableXR();
        }
    }
    
    void DisableXR()
    {
        xrInitialized = false;
        Debug.Log("XR disabled");
    }
    
    public void RecenterTracking()
    {
        if (xrInitialized)
        {
            var xrInputSubsystem = XRGeneralSettings.Instance?.Manager?.activeLoader?.GetLoadedSubsystem<UnityEngine.XR.XRInputSubsystem>();
            if (xrInputSubsystem != null)
            {
                xrInputSubsystem.TryRecenter();
                Debug.Log("XR tracking recentered");
            }
        }
    }
}