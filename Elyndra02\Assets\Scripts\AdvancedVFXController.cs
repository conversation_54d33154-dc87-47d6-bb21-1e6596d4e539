using UnityEngine;
using UnityEngine.VFX;
using UnityEngine.Rendering;
using System.Collections.Generic;

public class AdvancedVFXController : MonoBehaviour
{
    [Header("VFX Settings")]
    public VisualEffect visualEffect;
    public new ParticleSystem particleSystem;
    
    [Header("Shader Effects")]
    public Material[] effectMaterials;
    public Renderer targetRenderer;
    
    [<PERSON><PERSON>("Post Processing")]
    public Volume postProcessVolume;
    public bool enableDynamicEffects = true;
    
    [Header("Performance")]
    public int maxParticles = 1000;
    public float cullingDistance = 50f;
    
    private Dictionary<string, VisualEffect> vfxInstances;
    private Camera mainCamera;
    
    void Start()
    {
        InitializeVFX();
    }
    
    void InitializeVFX()
    {
        vfxInstances = new Dictionary<string, VisualEffect>();
        mainCamera = Camera.main;
        
        if (visualEffect != null)
        {
            RegisterVFX("main", visualEffect);
        }
        
        SetupPerformanceSettings();
        
        Debug.Log("Advanced VFX Controller initialized");
    }
    
    void SetupPerformanceSettings()
    {
        if (particleSystem != null)
        {
            var main = particleSystem.main;
            main.maxParticles = maxParticles;
            
            var emission = particleSystem.emission;
            emission.enabled = true;
        }
    }
    
    public void RegisterVFX(string name, VisualEffect vfx)
    {
        vfxInstances[name] = vfx;
        Debug.Log($"VFX '{name}' registered");
    }
    
    public void PlayVFX(string name)
    {
        if (vfxInstances.ContainsKey(name))
        {
            vfxInstances[name].Play();
            Debug.Log($"VFX '{name}' playing");
        }
    }
    
    public void StopVFX(string name)
    {
        if (vfxInstances.ContainsKey(name))
        {
            vfxInstances[name].Stop();
            Debug.Log($"VFX '{name}' stopped");
        }
    }
    
    public void SetVFXProperty(string vfxName, string propertyName, float value)
    {
        if (vfxInstances.ContainsKey(vfxName))
        {
            vfxInstances[vfxName].SetFloat(propertyName, value);
        }
    }
    
    public void CreateExplosionEffect(Vector3 position)
    {
        if (particleSystem != null)
        {
            particleSystem.transform.position = position;
            particleSystem.Play();
            Debug.Log($"Explosion effect at {position}");
        }
    }
    
    public void ApplyShaderEffect(int materialIndex)
    {
        if (targetRenderer != null && materialIndex < effectMaterials.Length)
        {
            targetRenderer.material = effectMaterials[materialIndex];
            Debug.Log($"Shader effect {materialIndex} applied");
        }
    }
    
    void Update()
    {
        if (enableDynamicEffects)
        {
            UpdateDynamicEffects();
        }
        
        PerformCulling();
    }
    
    void UpdateDynamicEffects()
    {
        float time = Time.time;
        
        foreach (var vfx in vfxInstances.Values)
        {
            if (vfx != null)
            {
                vfx.SetFloat("Time", time);
            }
        }
    }
    
    void PerformCulling()
    {
        if (mainCamera == null) return;
        
        foreach (var vfx in vfxInstances.Values)
        {
            if (vfx != null)
            {
                float distance = Vector3.Distance(mainCamera.transform.position, vfx.transform.position);
                bool shouldCull = distance > cullingDistance;
                
                if (shouldCull && vfx.isActiveAndEnabled)
                {
                    vfx.Stop();
                }
                else if (!shouldCull && !vfx.isActiveAndEnabled)
                {
                    vfx.Play();
                }
            }
        }
    }
}