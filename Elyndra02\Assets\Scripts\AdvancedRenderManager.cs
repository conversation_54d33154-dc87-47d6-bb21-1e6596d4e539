using UnityEngine;
using UnityEngine.Rendering;

public class AdvancedRenderManager : MonoBehaviour
{
    [Header("Advanced Rendering")]
    public bool enablePostProcessing = true;
    public bool enableShadows = true;
    public ShadowQuality shadowQuality = ShadowQuality.All;
    
    [Header("Performance")]
    public int targetFrameRate = 60;
    public bool enableVSync = false;
    
    private Camera renderCamera;
    
    void Start()
    {
        renderCamera = Camera.main ?? FindFirstObjectByType<Camera>();
        ConfigureAdvancedRendering();
    }
    
    void ConfigureAdvancedRendering()
    {
        Application.targetFrameRate = targetFrameRate;
        QualitySettings.vSyncCount = enableVSync ? 1 : 0;
        QualitySettings.shadows = shadowQuality;
        
        if (renderCamera != null)
        {
            renderCamera.renderingPath = RenderingPath.DeferredShading;
        }
        
        Debug.Log("Advanced rendering configured successfully");
    }
    
    public void SetShadowQuality(int quality)
    {
        shadowQuality = (ShadowQuality)quality;
        QualitySettings.shadows = shadowQuality;
    }
}