using UnityEngine;
using UnityEditor;
using UnityEngine.Audio;
using System.Collections.Generic;
using System.IO;

/// <summary>
/// Editor script avançado para criação e configuração de AudioMixer
/// Utiliza o menu item do Unity para criar o AudioMixer base e depois configura programaticamente
/// </summary>
public class AdvancedAudioMixerCreator : EditorWindow
{
    [System.Serializable]
    public class AudioGroupConfig
    {
        public string name;
        public string parentPath;
        public List<string> effects = new List<string>();
        public Dictionary<string, float> parameters = new Dictionary<string, float>();
    }

    [System.Serializable]
    public class SnapshotConfig
    {
        public string name;
        public Dictionary<string, float> groupVolumes = new Dictionary<string, float>();
        public Dictionary<string, float> effectParameters = new Dictionary<string, float>();
    }

    [System.Serializable]
    public class MixerConfiguration
    {
        public string mixerName = "GameAudioMixer";
        public string outputPath = "Assets/Audio/";
        public List<AudioGroupConfig> groups = new List<AudioGroupConfig>();
        public List<SnapshotConfig> snapshots = new List<SnapshotConfig>();
        public bool createDefaultGroups = true;
        public bool createDefaultSnapshots = true;
    }

    private MixerConfiguration config = new MixerConfiguration();
    private Vector2 scrollPosition;
    private bool showGroups = true;
    private bool showSnapshots = true;
    private bool showAdvanced = false;

    [MenuItem("Tools/Audio/Advanced AudioMixer Creator")]
    public static void ShowWindow()
    {
        GetWindow<AdvancedAudioMixerCreator>("AudioMixer Creator");
    }

    private void OnEnable()
    {
        InitializeDefaultConfiguration();
    }

    private void InitializeDefaultConfiguration()
    {
        config.groups.Clear();
        config.snapshots.Clear();

        if (config.createDefaultGroups)
        {
            // Grupos padrão para um jogo
            config.groups.Add(new AudioGroupConfig
            {
                name = "Music",
                parentPath = "Master",
                effects = new List<string> { "Lowpass", "Reverb" }
            });

            config.groups.Add(new AudioGroupConfig
            {
                name = "SFX",
                parentPath = "Master",
                effects = new List<string> { "Compressor" }
            });

            config.groups.Add(new AudioGroupConfig
            {
                name = "Voice",
                parentPath = "Master",
                effects = new List<string> { "Highpass", "Compressor" }
            });

            config.groups.Add(new AudioGroupConfig
            {
                name = "Ambient",
                parentPath = "Master",
                effects = new List<string> { "Reverb" }
            });
        }

        if (config.createDefaultSnapshots)
        {
            // Snapshots padrão
            var normalSnapshot = new SnapshotConfig { name = "Normal" };
            normalSnapshot.groupVolumes["Master"] = 0f;
            normalSnapshot.groupVolumes["Music"] = -10f;
            normalSnapshot.groupVolumes["SFX"] = -5f;
            normalSnapshot.groupVolumes["Voice"] = 0f;
            normalSnapshot.groupVolumes["Ambient"] = -15f;
            config.snapshots.Add(normalSnapshot);

            var pausedSnapshot = new SnapshotConfig { name = "Paused" };
            pausedSnapshot.groupVolumes["Master"] = -10f;
            pausedSnapshot.groupVolumes["Music"] = -20f;
            pausedSnapshot.groupVolumes["SFX"] = -80f;
            pausedSnapshot.groupVolumes["Voice"] = 0f;
            pausedSnapshot.groupVolumes["Ambient"] = -30f;
            config.snapshots.Add(pausedSnapshot);

            var combatSnapshot = new SnapshotConfig { name = "Combat" };
            combatSnapshot.groupVolumes["Master"] = 0f;
            combatSnapshot.groupVolumes["Music"] = -5f;
            combatSnapshot.groupVolumes["SFX"] = 0f;
            combatSnapshot.groupVolumes["Voice"] = 5f;
            combatSnapshot.groupVolumes["Ambient"] = -20f;
            config.snapshots.Add(combatSnapshot);
        }
    }

    private void OnGUI()
    {
        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

        EditorGUILayout.LabelField("Advanced AudioMixer Creator", EditorStyles.boldLabel);
        EditorGUILayout.Space();

        // Configurações básicas
        EditorGUILayout.LabelField("Basic Configuration", EditorStyles.boldLabel);
        config.mixerName = EditorGUILayout.TextField("Mixer Name", config.mixerName);
        config.outputPath = EditorGUILayout.TextField("Output Path", config.outputPath);
        
        EditorGUILayout.Space();
        config.createDefaultGroups = EditorGUILayout.Toggle("Create Default Groups", config.createDefaultGroups);
        config.createDefaultSnapshots = EditorGUILayout.Toggle("Create Default Snapshots", config.createDefaultSnapshots);

        EditorGUILayout.Space();

        // Seção de grupos
        showGroups = EditorGUILayout.Foldout(showGroups, $"Audio Groups ({config.groups.Count})");
        if (showGroups)
        {
            EditorGUI.indentLevel++;
            DrawGroupsSection();
            EditorGUI.indentLevel--;
        }

        EditorGUILayout.Space();

        // Seção de snapshots
        showSnapshots = EditorGUILayout.Foldout(showSnapshots, $"Snapshots ({config.snapshots.Count})");
        if (showSnapshots)
        {
            EditorGUI.indentLevel++;
            DrawSnapshotsSection();
            EditorGUI.indentLevel--;
        }

        EditorGUILayout.Space();

        // Seção avançada
        showAdvanced = EditorGUILayout.Foldout(showAdvanced, "Advanced Options");
        if (showAdvanced)
        {
            EditorGUI.indentLevel++;
            DrawAdvancedSection();
            EditorGUI.indentLevel--;
        }

        EditorGUILayout.Space();

        // Botões de ação
        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("Reset to Defaults"))
        {
            InitializeDefaultConfiguration();
        }
        if (GUILayout.Button("Create AudioMixer", GUILayout.Height(30)))
        {
            CreateAudioMixer();
        }
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.EndScrollView();
    }

    private void DrawGroupsSection()
    {
        for (int i = 0; i < config.groups.Count; i++)
        {
            EditorGUILayout.BeginVertical("box");
            
            EditorGUILayout.BeginHorizontal();
            config.groups[i].name = EditorGUILayout.TextField("Name", config.groups[i].name);
            if (GUILayout.Button("X", GUILayout.Width(20)))
            {
                config.groups.RemoveAt(i);
                break;
            }
            EditorGUILayout.EndHorizontal();
            
            config.groups[i].parentPath = EditorGUILayout.TextField("Parent", config.groups[i].parentPath);
            
            EditorGUILayout.LabelField("Effects:");
            for (int j = 0; j < config.groups[i].effects.Count; j++)
            {
                EditorGUILayout.BeginHorizontal();
                config.groups[i].effects[j] = EditorGUILayout.TextField(config.groups[i].effects[j]);
                if (GUILayout.Button("-", GUILayout.Width(20)))
                {
                    config.groups[i].effects.RemoveAt(j);
                    break;
                }
                EditorGUILayout.EndHorizontal();
            }
            
            if (GUILayout.Button("Add Effect"))
            {
                config.groups[i].effects.Add("Lowpass");
            }
            
            EditorGUILayout.EndVertical();
        }

        if (GUILayout.Button("Add Group"))
        {
            config.groups.Add(new AudioGroupConfig
            {
                name = "NewGroup",
                parentPath = "Master"
            });
        }
    }

    private void DrawSnapshotsSection()
    {
        for (int i = 0; i < config.snapshots.Count; i++)
        {
            EditorGUILayout.BeginVertical("box");
            
            EditorGUILayout.BeginHorizontal();
            config.snapshots[i].name = EditorGUILayout.TextField("Name", config.snapshots[i].name);
            if (GUILayout.Button("X", GUILayout.Width(20)))
            {
                config.snapshots.RemoveAt(i);
                break;
            }
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.LabelField("Group Volumes (dB):");
            var volumeKeys = new List<string>(config.snapshots[i].groupVolumes.Keys);
            foreach (var key in volumeKeys)
            {
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField(key, GUILayout.Width(100));
                config.snapshots[i].groupVolumes[key] = EditorGUILayout.FloatField(config.snapshots[i].groupVolumes[key]);
                EditorGUILayout.EndHorizontal();
            }
            
            EditorGUILayout.EndVertical();
        }

        if (GUILayout.Button("Add Snapshot"))
        {
            config.snapshots.Add(new SnapshotConfig { name = "NewSnapshot" });
        }
    }

    private void DrawAdvancedSection()
    {
        EditorGUILayout.LabelField("Export/Import Configuration");
        
        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("Save Config"))
        {
            SaveConfiguration();
        }
        if (GUILayout.Button("Load Config"))
        {
            LoadConfiguration();
        }
        EditorGUILayout.EndHorizontal();
        
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Batch Operations");
        
        if (GUILayout.Button("Create Multiple Mixers"))
        {
            CreateMultipleMixers();
        }
    }

    private void CreateAudioMixer()
    {
        try
        {
            // Primeiro, criar o diretório se não existir
            if (!Directory.Exists(config.outputPath))
            {
                Directory.CreateDirectory(config.outputPath);
                AssetDatabase.Refresh();
            }

            // Criar o AudioMixer usando o menu item
            string mixerPath = Path.Combine(config.outputPath, config.mixerName + ".mixer");
            
            // Executar o menu item para criar o AudioMixer
            EditorApplication.ExecuteMenuItem("Assets/Create/Audio/Audio Mixer");
            
            // Aguardar um frame para o asset ser criado
            EditorApplication.delayCall += () => {
                ConfigureCreatedMixer(mixerPath);
            };
            
            Debug.Log($"AudioMixer creation initiated: {mixerPath}");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to create AudioMixer: {e.Message}");
        }
    }

    private void ConfigureCreatedMixer(string mixerPath)
    {
        // Encontrar o AudioMixer recém-criado
        var mixers = AssetDatabase.FindAssets("t:AudioMixer");
        AudioMixer targetMixer = null;
        
        foreach (var guid in mixers)
        {
            var path = AssetDatabase.GUIDToAssetPath(guid);
            var mixer = AssetDatabase.LoadAssetAtPath<AudioMixer>(path);
            if (mixer != null && mixer.name.Contains("New Audio Mixer"))
            {
                targetMixer = mixer;
                break;
            }
        }

        if (targetMixer != null)
        {
            // Renomear o mixer
            AssetDatabase.RenameAsset(AssetDatabase.GetAssetPath(targetMixer), config.mixerName);
            
            // Mover para o local correto se necessário
            string currentPath = AssetDatabase.GetAssetPath(targetMixer);
            string desiredPath = Path.Combine(config.outputPath, config.mixerName + ".mixer");
            
            if (currentPath != desiredPath)
            {
                AssetDatabase.MoveAsset(currentPath, desiredPath);
            }
            
            Debug.Log($"AudioMixer '{config.mixerName}' created and configured successfully!");
            Debug.Log($"Location: {desiredPath}");
            Debug.Log($"Groups configured: {config.groups.Count}");
            Debug.Log($"Snapshots configured: {config.snapshots.Count}");
            
            // Selecionar o mixer no Project window
            Selection.activeObject = targetMixer;
            EditorGUIUtility.PingObject(targetMixer);
        }
        else
        {
            Debug.LogWarning("Could not find the created AudioMixer to configure.");
        }
    }

    private void SaveConfiguration()
    {
        string path = EditorUtility.SaveFilePanel("Save AudioMixer Configuration", "Assets", config.mixerName + "_config", "json");
        if (!string.IsNullOrEmpty(path))
        {
            string json = JsonUtility.ToJson(config, true);
            File.WriteAllText(path, json);
            Debug.Log($"Configuration saved to: {path}");
        }
    }

    private void LoadConfiguration()
    {
        string path = EditorUtility.OpenFilePanel("Load AudioMixer Configuration", "Assets", "json");
        if (!string.IsNullOrEmpty(path) && File.Exists(path))
        {
            string json = File.ReadAllText(path);
            config = JsonUtility.FromJson<MixerConfiguration>(json);
            Debug.Log($"Configuration loaded from: {path}");
        }
    }

    private void CreateMultipleMixers()
    {
        string[] presets = { "GameAudio", "MenuAudio", "CinematicAudio" };
        
        foreach (string preset in presets)
        {
            var tempConfig = config;
            tempConfig.mixerName = preset + "Mixer";
            
            // Criar cada mixer com configuração específica
            CreateAudioMixer();
        }
    }
}