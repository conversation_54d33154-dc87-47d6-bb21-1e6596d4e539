using System;
using Unity.AI.Toolkit.Accounts.Services;
using UnityEditor;
using UnityEngine;
using UnityEngine.UIElements;
using Unity.AI.Toolkit.Accounts.Services.Core;

namespace Unity.AI.Toolkit.Accounts.Components
{
    [UxmlElement]
    partial class AIDropdown : VisualElement
    {
        PointsBeta m_PointsBeta;

        public AIDropdown()
        {
            var tree = AssetDatabase.LoadAssetAtPath<VisualTreeAsset>("Packages/com.unity.ai.toolkit/Modules/Accounts/Components/AIDropdown/AIDropdown.uxml");
            tree.CloneTree(this);

            m_PointsBeta = this.Q<PointsBeta>("points");

            var menuExtensionsGeneral = this.Q<VisualElement>("menu-extensions-general");
            Extensions.OnExtendGeneral(menuExtensionsGeneral);

            var manageAccountSeparator = this.Q<VisualElement>("manage-account-separator");
            var menuExtensions = this.Q<VisualElement>("menu-extensions");

            if (DropdownExtension.onExtend.Count > 0)
                manageAccountSeparator.RemoveFromClassList("hidden");

            Extensions.OnExtend(menuExtensions);

            RegisterCallback<AttachToPanelEvent>(_ =>
            {
                Account.settings.OnChange += Refresh;
                Account.network.OnChange += Refresh;
                Refresh();
            });
            RegisterCallback<DetachFromPanelEvent>(_ =>
            {
                Account.settings.OnChange -= Refresh;
                Account.network.OnChange -= Refresh;
            });
        }

        void Refresh()
        {
            m_PointsBeta.style.display = ShouldHidePoints ? DisplayStyle.None : DisplayStyle.Flex;

            Extensions.OnShow(this);
        }

        static bool ShouldHidePoints =>
            !Account.network.IsAvailable ||
            (!Account.settings.AiAssistantEnabled && !Account.settings.AiGeneratorsEnabled);
    }
}
