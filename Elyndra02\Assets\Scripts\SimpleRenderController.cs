using UnityEngine;
using UnityEngine.Rendering;

public class SimpleRenderController : MonoBehaviour
{
    [Header("Rendering Settings")]
    public Camera targetCamera;
    public Light mainLight;
    
    [Header("Quality")]
    public int targetFrameRate = 60;
    public bool enableVSync = true;
    public ShadowQuality shadowQuality = ShadowQuality.All;
    
    [Header("Lighting")]
    public float lightIntensity = 1f;
    public Color lightColor = Color.white;
    
    void Start()
    {
        InitializeRenderer();
    }
    
    void InitializeRenderer()
    {
        if (targetCamera == null)
            targetCamera = Camera.main;
            
        if (mainLight == null)
            mainLight = UnityEngine.Object.FindFirstObjectByType<Light>();
            
        ApplySettings();
        
        Debug.Log("Simple Render Controller initialized!");
        LogRenderInfo();
    }
    
    void ApplySettings()
    {
        Application.targetFrameRate = targetFrameRate;
        QualitySettings.vSyncCount = enableVSync ? 1 : 0;
        QualitySettings.shadows = shadowQuality;
        
        if (targetCamera != null)
        {
            targetCamera.allowHDR = true;
        }
        
        if (mainLight != null)
        {
            mainLight.intensity = lightIntensity;
            mainLight.color = lightColor;
        }
    }
    
    public void SetQuality(int level)
    {
        QualitySettings.SetQualityLevel(level);
        Debug.Log($"Quality set to: {level}");
    }
    
    public void SetShadows(ShadowQuality quality)
    {
        shadowQuality = quality;
        QualitySettings.shadows = quality;
        Debug.Log($"Shadows: {quality}");
    }
    
    public void SetFrameRate(int fps)
    {
        targetFrameRate = fps;
        Application.targetFrameRate = fps;
        Debug.Log($"FPS target: {fps}");
    }
    
    public void ToggleVSync()
    {
        enableVSync = !enableVSync;
        QualitySettings.vSyncCount = enableVSync ? 1 : 0;
        Debug.Log($"VSync: {enableVSync}");
    }
    
    public void SetLightIntensity(float intensity)
    {
        lightIntensity = intensity;
        if (mainLight != null)
            mainLight.intensity = intensity;
    }
    
    public void SetLightColor(Color color)
    {
        lightColor = color;
        if (mainLight != null)
            mainLight.color = color;
    }
    
    void LogRenderInfo()
    {
        Debug.Log($"Graphics: {SystemInfo.graphicsDeviceName}");
        Debug.Log($"Memory: {SystemInfo.graphicsMemorySize}MB");
        Debug.Log($"Quality: {QualitySettings.GetQualityLevel()}");
    }
}