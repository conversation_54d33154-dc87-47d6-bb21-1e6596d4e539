using UnityEngine;
using System.Collections;

namespace AuracronCharacters
{
    /// <summary>
    /// Controlador principal do Kaelen Guardian
    /// Baseado no design document KAELEN_REDESIGN_EPIC.md
    /// Integra todos os sistemas: Stats, Abilities, Animation, Audio
    /// </summary>
    public class KaelenController : MonoBehaviour
    {
        [Header("=== CORE COMPONENTS ===")]
        public CharacterController characterController;
        public Animator animator;
        public AudioSource audioSource;
        
        [Header("=== KAELEN SYSTEMS ===")]
        public KaelenStats kaelenStats;
        public KaelenAbilities kaelenAbilities;
        public KaelenAnimationController animationController;
        public KaelenAudioManager audioManager;
        
        [Header("=== ABILITIES DATA ===")]
        public AbilityData passiveAbility;
        public AbilityData qAbility; // Investida Perfurante
        public AbilityData wAbility; // Barreira Etérea
        public AbilityData eAbility; // Marca do Caçador
        public AbilityData rAbility; // Convergência das Lâminas
        
        [Header("=== MOVEMENT SETTINGS ===")]
        public float moveSpeed = 340f;
        public float jumpHeight = 2f;
        public float gravity = -9.81f;
        public float rotationSpeed = 10f;
        
        [Header("=== INPUT SETTINGS ===")]
        public KeyCode qKey = KeyCode.Q;
        public KeyCode wKey = KeyCode.W;
        public KeyCode eKey = KeyCode.E;
        public KeyCode rKey = KeyCode.R;
        public KeyCode basicAttackKey = KeyCode.Mouse0;
        
        // Movement variables
        private Vector3 velocity;
        private Vector3 lastMovementDirection;
        private bool isGrounded;
        private bool isMoving;
        
        // Combat variables
        private Transform currentTarget;
        private float lastAttackTime;
        
        // State flags
        public bool IsControllable { get; set; } = true;
        public bool IsInCombat { get; private set; } = false;
        
        void Start()
        {
            InitializeComponents();
            InitializeAbilities();
            SubscribeToEvents();
        }
        
        void Update()
        {
            if (!IsControllable || kaelenStats.IsDead) return;
            
            HandleMovement();
            HandleAbilities();
            HandleBasicAttack();
            UpdateCombatState();
        }
        
        private void InitializeComponents()
        {
            // Get core components
            if (characterController == null)
                characterController = GetComponent<CharacterController>();
            if (animator == null)
                animator = GetComponent<Animator>();
            if (audioSource == null)
                audioSource = GetComponent<AudioSource>();
            
            // Get Kaelen systems
            if (kaelenStats == null)
                kaelenStats = GetComponent<KaelenStats>();
            if (kaelenAbilities == null)
                kaelenAbilities = GetComponent<KaelenAbilities>();
            if (animationController == null)
                animationController = GetComponent<KaelenAnimationController>();
            if (audioManager == null)
                audioManager = GetComponent<KaelenAudioManager>();
            
            // Validate critical components
            if (kaelenStats == null)
            {
                Debug.LogError("KaelenStats component not found!");
            }
        }
        
        private void InitializeAbilities()
        {
            // Initialize ability data based on design document
            if (qAbility == null) qAbility = new AbilityData();
            qAbility.name = "Investida Perfurante";
            qAbility.description = "Dash que atravessa inimigos";
            qAbility.damage = 80f;
            qAbility.manaCost = 60f;
            qAbility.cooldown = 12f;
            qAbility.animationTrigger = "InvestidaPerfurante";
            
            if (wAbility == null) wAbility = new AbilityData();
            wAbility.name = "Barreira Etérea";
            wAbility.description = "Cria barreira que buffa aliados e debuffa inimigos";
            wAbility.damage = 0f;
            wAbility.manaCost = 80f;
            wAbility.cooldown = 18f;
            wAbility.animationTrigger = "BarreiraEterea";
            
            if (eAbility == null) eAbility = new AbilityData();
            eAbility.name = "Marca do Caçador";
            eAbility.description = "Marca inimigo para teleporte";
            eAbility.damage = 100f;
            eAbility.manaCost = 70f;
            eAbility.cooldown = 14f;
            eAbility.animationTrigger = "MarcaDoCacador";
            
            if (rAbility == null) rAbility = new AbilityData();
            rAbility.name = "Convergência das Lâminas";
            rAbility.description = "Ultimate - chuva de lâminas";
            rAbility.damage = 150f;
            rAbility.manaCost = 120f;
            rAbility.cooldown = 80f;
            rAbility.animationTrigger = "ConvergenciaDasLaminas";
        }
        
        private void SubscribeToEvents()
        {
            if (kaelenStats != null)
            {
                kaelenStats.OnDeath.AddListener(OnDeath);
                kaelenStats.OnLevelUp.AddListener(OnLevelUp);
            }
        }
        
        #region MOVEMENT SYSTEM
        private void HandleMovement()
        {
            // Don't move during ability animations
            if (animationController != null && animationController.IsPlayingAbilityAnimation())
                return;
            
            // Get input
            float horizontal = Input.GetAxis("Horizontal");
            float vertical = Input.GetAxis("Vertical");
            
            Vector3 direction = new Vector3(horizontal, 0f, vertical).normalized;
            isMoving = direction.magnitude >= 0.1f;
            
            if (isMoving)
            {
                // Apply movement speed from stats
                float currentMoveSpeed = kaelenStats.GetFinalStats().CurrentMoveSpeed;
                Vector3 movement = direction * currentMoveSpeed * Time.deltaTime;
                characterController.Move(movement);
                
                // Rotate towards movement direction
                Quaternion targetRotation = Quaternion.LookRotation(direction);
                transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, rotationSpeed * Time.deltaTime);
                
                lastMovementDirection = direction;
            }
            
            // Handle jumping
            if (Input.GetButtonDown("Jump") && isGrounded)
            {
                velocity.y = Mathf.Sqrt(jumpHeight * -2f * gravity);
            }
            
            // Apply gravity
            velocity.y += gravity * Time.deltaTime;
            characterController.Move(velocity * Time.deltaTime);
            
            // Ground check
            isGrounded = characterController.isGrounded;
            if (isGrounded && velocity.y < 0)
            {
                velocity.y = -2f;
            }
        }
        #endregion
        
        #region ABILITY SYSTEM
        private void HandleAbilities()
        {
            // Q - Investida Perfurante
            if (Input.GetKeyDown(qKey))
            {
                UseQAbility();
            }
            
            // W - Barreira Etérea
            if (Input.GetKeyDown(wKey))
            {
                UseWAbility();
            }
            
            // E - Marca do Caçador
            if (Input.GetKeyDown(eKey))
            {
                UseEAbility();
            }
            
            // R - Convergência das Lâminas
            if (Input.GetKeyDown(rKey))
            {
                UseRAbility();
            }
        }
        
        public void UseQAbility()
        {
            if (CanUseAbility(qAbility))
            {
                ConsumeAbility(qAbility);
                
                // Execute ability
                if (kaelenAbilities != null)
                    kaelenAbilities.ExecuteInvestidaPerfurante();
                
                // Play animation
                if (animationController != null)
                    animationController.PlayPiercingCharge();
                
                // Play audio
                if (audioManager != null)
                    audioManager.PlayPiercingChargeSound();
            }
        }
        
        public void UseWAbility()
        {
            if (CanUseAbility(wAbility))
            {
                ConsumeAbility(wAbility);
                
                // Execute ability
                if (kaelenAbilities != null)
                    kaelenAbilities.ExecuteBarreiraEterea();
                
                // Play animation
                if (animationController != null)
                    animationController.PlayEtherealBarrier();
                
                // Play audio
                if (audioManager != null)
                    audioManager.PlayEtherealBarrierSound();
            }
        }
        
        public void UseEAbility()
        {
            if (CanUseAbility(eAbility))
            {
                ConsumeAbility(eAbility);
                
                // Get target direction (mouse or forward)
                Vector3 targetDirection = GetTargetDirection();
                
                // Execute ability
                if (kaelenAbilities != null)
                    kaelenAbilities.ExecuteMarcaDoCacador(targetDirection);
                
                // Play animation
                if (animationController != null)
                    animationController.PlayHuntersMark();
                
                // Play audio
                if (audioManager != null)
                    audioManager.PlayHuntersMarkSound();
            }
        }
        
        public void UseRAbility()
        {
            if (CanUseAbility(rAbility))
            {
                ConsumeAbility(rAbility);
                
                // Execute ability
                if (kaelenAbilities != null)
                    kaelenAbilities.ExecuteConvergenciaDasLaminas();
                
                // Play animation
                if (animationController != null)
                    animationController.PlayConvergenceOfBlades();
                
                // Play audio
                if (audioManager != null)
                    audioManager.PlayConvergenceSound();
            }
        }
        
        private bool CanUseAbility(AbilityData ability)
        {
            if (ability == null || kaelenStats == null) return false;
            
            // Check if dead
            if (kaelenStats.IsDead) return false;
            
            // Check cooldown
            if (Time.time < ability.lastUsedTime + ability.cooldown)
            {
                Debug.Log($"Habilidade {ability.name} ainda está em cooldown! Restam {(ability.lastUsedTime + ability.cooldown - Time.time):F1}s");
                return false;
            }
            
            // Check mana
            if (kaelenStats.GetBaseStats().CurrentMana < ability.manaCost)
            {
                Debug.Log($"Mana insuficiente para {ability.name}! Necessário: {ability.manaCost}, Atual: {kaelenStats.GetBaseStats().CurrentMana}");
                return false;
            }
            
            // Check if already casting
            if (animationController != null && animationController.IsPlayingAbilityAnimation())
            {
                Debug.Log("Já executando uma habilidade!");
                return false;
            }
            
            return true;
        }
        
        private void ConsumeAbility(AbilityData ability)
        {
            // Consume mana
            kaelenStats.ConsumeMana(ability.manaCost);
            
            // Set cooldown
            ability.lastUsedTime = Time.time;
            
            // Enter combat
            IsInCombat = true;
            
            Debug.Log($"Usou habilidade: {ability.name}");
        }
        
        private Vector3 GetTargetDirection()
        {
            // Simple implementation - use forward direction
            // Can be enhanced with mouse targeting
            return transform.forward;
        }
        #endregion
        
        #region BASIC ATTACK SYSTEM
        private void HandleBasicAttack()
        {
            if (Input.GetKeyDown(basicAttackKey))
            {
                PerformBasicAttack();
            }
        }
        
        public void PerformBasicAttack()
        {
            if (kaelenStats.IsDead) return;
            
            // Check attack speed cooldown
            float attackCooldown = 1f / kaelenStats.GetFinalStats().CurrentAttackSpeed;
            if (Time.time < lastAttackTime + attackCooldown) return;
            
            lastAttackTime = Time.time;
            IsInCombat = true;
            
            // Play animation
            if (animationController != null)
                animationController.PlayBasicAttack();
            
            // Play audio
            if (audioManager != null)
                audioManager.PlayBasicAttackSound();
            
            Debug.Log($"Ataque básico! Dano: {kaelenStats.GetFinalStats().CurrentAttackDamage}");
        }
        
        public void ApplyBasicAttackDamage()
        {
            // Called by animation event
            // Find targets in range and apply damage
            float attackRange = kaelenStats.GetFinalStats().CurrentRange;
            float attackDamage = kaelenStats.GetFinalStats().CurrentAttackDamage;
            
            // Simple implementation - can be enhanced with proper targeting
            Debug.Log($"Aplicando dano do ataque básico: {attackDamage}");
        }
        #endregion
        
        #region COMBAT STATE
        private void UpdateCombatState()
        {
            // Exit combat after 5 seconds of inactivity
            if (IsInCombat && Time.time > lastAttackTime + 5f && 
                (animationController == null || !animationController.IsPlayingAbilityAnimation()))
            {
                IsInCombat = false;
                
                // Play theme music
                if (audioManager != null)
                    audioManager.PlayThemeMusic();
            }
        }
        #endregion
        
        #region EVENT HANDLERS
        private void OnDeath()
        {
            IsControllable = false;
            
            Debug.Log("Kaelen morreu!");
            
            // Stop all movement
            velocity = Vector3.zero;
            
            // Play death audio
            if (audioManager != null)
                audioManager.PlayDeathSound();
        }
        
        private void OnLevelUp()
        {
            Debug.Log($"Kaelen subiu para o nível {kaelenStats.currentLevel}!");
            
            // Play level up effects
            if (audioManager != null)
                audioManager.PlayEmotionalSound(EmotionType.Triumphant);
        }
        #endregion
        
        #region UTILITY METHODS
        public void Revive()
        {
            if (kaelenStats != null)
            {
                kaelenStats.Revive();
                IsControllable = true;
            }
        }
        
        public void SetTarget(Transform target)
        {
            currentTarget = target;
        }
        
        public Transform GetCurrentTarget() => currentTarget;
        
        // Getters for UI and other systems
        public float GetHealthPercentage() => kaelenStats?.GetHealthPercentage() ?? 0f;
        public float GetManaPercentage() => kaelenStats?.GetManaPercentage() ?? 0f;
        public CharacterStats GetStats() => kaelenStats?.GetFinalStats();
        
        public float GetAbilityCooldownRemaining(int abilityIndex)
        {
            AbilityData ability = null;
            switch (abilityIndex)
            {
                case 0: ability = qAbility; break;
                case 1: ability = wAbility; break;
                case 2: ability = eAbility; break;
                case 3: ability = rAbility; break;
            }
            
            if (ability == null) return 0f;
            
            float remaining = (ability.lastUsedTime + ability.cooldown) - Time.time;
            return Mathf.Max(0f, remaining);
        }
        
        public bool IsAbilityReady(int abilityIndex)
        {
            return GetAbilityCooldownRemaining(abilityIndex) <= 0f;
        }
        #endregion
    }
    
    [System.Serializable]
    public class AbilityData
    {
        public string name;
        public string description;
        public float damage;
        public float manaCost;
        public float cooldown;
        public string animationTrigger;
        public AudioClip soundEffect;
        
        [HideInInspector]
        public float lastUsedTime;
    }
}