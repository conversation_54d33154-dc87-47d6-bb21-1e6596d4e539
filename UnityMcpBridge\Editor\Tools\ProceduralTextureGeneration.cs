using System;
using System.Collections.Generic;
using System.IO;
using Newtonsoft.Json.Linq;
using UnityEditor;
using UnityEngine;
using UnityEngine.Rendering;
using UnityMcpBridge.Editor.Helpers;

namespace UnityMcpBridge.Editor.Tools
{
    /// <summary>
    /// [UNITY 6.2 COMPATIBLE] - Handles procedural texture generation using AI Generator and RenderTexture.
    /// 
    /// Su<PERSON>a as seguintes ações:
    /// - generate_skin: Gera texturas de pele realistas
    /// - generate_fabric: Gera texturas de tecido
    /// - generate_metal: Gera texturas metálicas
    /// - generate_normal: Gera mapas normais
    /// - generate_batch: Gera múltiplas texturas
    /// - list_presets: Lista presets disponíveis
    /// - get_info: Obtém informações de uma textura
    /// 
    /// [UNITY 6.2 FEATURES]:
    /// - AI Texture Generator integration
    /// - RenderTexture com GPU acceleration
    /// - Automatic PBR map generation
    /// - Texture streaming support
    /// 
    /// [DEPENDÊNCIAS]:
    /// - Unity.AI.Generators (Unity 6.2+)
    /// - UnityEngine.Rendering
    /// </summary>
    public static class ProceduralTextureGeneration
    {
        /// <summary>
        /// [PONTO DE ENTRADA] - Lista de ações válidas seguindo padrões MCP.
        /// </summary>
        private static readonly List<string> ValidActions = new List<string>
        {
            "generate_skin",
            "generate_fabric",
            "generate_metal",
            "generate_normal",
            "generate_batch",
            "list_presets",
            "get_info"
        };

        public static object HandleCommand(JObject @params)
        {
            string action = @params["action"]?.ToString().ToLower();
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            if (!ValidActions.Contains(action))
            {
                string validActionsList = string.Join(", ", ValidActions);
                return Response.Error(
                    $"Unknown action: '{action}'. Valid actions are: {validActionsList}"
                );
            }

            try
            {
                switch (action)
                {
                    case "generate_skin":
                        return GenerateSkinTexture(@params);
                    case "generate_fabric":
                        return GenerateFabricTexture(@params);
                    case "generate_metal":
                        return GenerateMetalTexture(@params);
                    case "generate_normal":
                        return GenerateNormalMap(@params);
                    case "generate_batch":
                        return GenerateBatchTextures(@params);
                    case "list_presets":
                        return ListPresets(@params);
                    case "get_info":
                        return GetTextureInfo(@params["output_path"]?.ToString());
                    default:
                        return Response.Error($"Unknown action: '{action}'");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[ProceduralTextureGeneration] Action '{action}' failed: {e}");
                return Response.Error($"Internal error processing action '{action}': {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Gera texturas de pele usando RenderTexture e Compute Shaders para performance.
        /// </summary>
        private static object GenerateSkinTexture(JObject @params)
        {
            try
            {
                string outputPath = SanitizeAssetPath(@params["output_path"]?.ToString() ?? "Assets/GeneratedTextures/");
                int width = @params["width"]?.ToObject<int>() ?? 1024;
                int height = @params["height"]?.ToObject<int>() ?? 1024;
                bool useRenderTexture = @params["use_render_texture"]?.ToObject<bool>() ?? true;
                bool enableMipmaps = @params["enable_mipmaps"]?.ToObject<bool>() ?? true;
                string format = @params["format"]?.ToString() ?? "RGBA32";
                var parameters = @params["parameters"] as JObject ?? new JObject();

                // Parâmetros específicos da pele
                Color skinColor = ParseColor(parameters["skin_color"]) ?? new Color(0.92f, 0.8f, 0.7f, 1.0f);
                float roughness = parameters["roughness"]?.ToObject<float>() ?? 0.6f;
                float poreIntensity = parameters["pore_intensity"]?.ToObject<float>() ?? 0.3f;
                string skinType = parameters["skin_type"]?.ToString() ?? "caucasian";
                string ageRange = parameters["age_range"]?.ToString() ?? "adult";
                string gender = parameters["gender"]?.ToString() ?? "male";

                // Validar tamanho (deve ser potência de 2)
                if (!IsPowerOfTwo(width) || !IsPowerOfTwo(height))
                {
                    return Response.Error("Width e Height devem ser potências de 2 (256, 512, 1024, 2048, etc.)");
                }

                // Determinar formato de textura
                TextureFormat textureFormat = format switch
                {
                    "RGBA32" => TextureFormat.RGBA32,
                    "RGB24" => TextureFormat.RGB24,
                    "DXT5" => TextureFormat.DXT5,
                    "BC7" => TextureFormat.BC7,
                    _ => TextureFormat.RGBA32
                };

                Texture2D skinTexture;
                
                if (useRenderTexture && SystemInfo.supportsComputeShaders)
                {
                    // Usar RenderTexture para geração GPU otimizada (Unity 6.2)
                    skinTexture = GenerateProceduralSkinTextureGPU(width, height, skinColor, roughness, poreIntensity, skinType, ageRange, gender, textureFormat, enableMipmaps);
                }
                else
                {
                    // Fallback CPU com otimizações Unity 6.2
                    skinTexture = GenerateProceduralSkinTextureCPU(width, height, skinColor, roughness, poreIntensity, skinType, ageRange, gender, textureFormat, enableMipmaps);
                }

                // Criar diretório se não existir
                string directory = Path.GetDirectoryName(outputPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // Salvar textura com compressão otimizada
                string texturePath = Path.Combine(outputPath, "GeneratedSkin.png");
                SaveTextureOptimized(skinTexture, texturePath, @params["compression_quality"]?.ToString() ?? "high");

                // Gerar mapas adicionais se solicitado
                var additionalMaps = new List<string>();
                if (@params["generate_normal_map"]?.ToObject<bool>() == true)
                {
                    var normalMap = GenerateNormalMapFromHeightMap(skinTexture, 2.0f);
                    string normalPath = Path.Combine(outputPath, "GeneratedSkin_Normal.png");
                    SaveTextureOptimized(normalMap, normalPath, "high");
                    additionalMaps.Add(normalPath);
                }

                if (@params["generate_roughness_map"]?.ToObject<bool>() == true)
                {
                    var roughnessMap = GenerateRoughnessMap(skinTexture, roughness);
                    string roughnessPath = Path.Combine(outputPath, "GeneratedSkin_Roughness.png");
                    SaveTextureOptimized(roughnessMap, roughnessPath, "high");
                    additionalMaps.Add(roughnessPath);
                }

                LogOperation("GenerateSkinTexture", $"Textura de pele criada: {texturePath}, {width}x{height}, formato: {format}");

                return Response.Success($"Textura de pele gerada em {texturePath}", new
                {
                    diffuseMap = texturePath,
                    additionalMaps = additionalMaps.ToArray(),
                    width = width,
                    height = height,
                    format = format,
                    usedGPU = useRenderTexture && SystemInfo.supportsComputeShaders,
                    skinType = skinType,
                    ageRange = ageRange,
                    gender = gender,
                    hasMipmaps = enableMipmaps
                });
            }
            catch (Exception e)
            {
                LogOperation("GenerateSkinTexture", $"Erro: {e.Message}", true);
                return Response.Error($"Erro ao gerar textura de pele: {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Gera textura de pele usando GPU com RenderTexture e Compute Shaders.
        /// </summary>
        private static Texture2D GenerateProceduralSkinTextureGPU(int width, int height, Color skinColor, float roughness, float poreIntensity, string skinType, string ageRange, string gender, TextureFormat format, bool enableMipmaps)
        {
            // Criar RenderTexture para processamento GPU
            RenderTexture renderTexture = new RenderTexture(width, height, 0, RenderTextureFormat.ARGB32)
            {
                enableRandomWrite = true,
                useMipMap = enableMipmaps,
                filterMode = FilterMode.Bilinear,
                wrapMode = TextureWrapMode.Repeat
            };
            renderTexture.Create();

            // Verificar se existe compute shader para skin generation
            ComputeShader skinShader = AssetDatabase.LoadAssetAtPath<ComputeShader>("Assets/Shaders/SkinGeneration.compute");
            
            if (skinShader != null)
            {
                // Usar compute shader personalizado se disponível
                int kernelIndex = skinShader.FindKernel("GenerateSkin");
                
                skinShader.SetTexture(kernelIndex, "Result", renderTexture);
                skinShader.SetVector("SkinColor", new Vector4(skinColor.r, skinColor.g, skinColor.b, skinColor.a));
                skinShader.SetFloat("Roughness", roughness);
                skinShader.SetFloat("PoreIntensity", poreIntensity);
                skinShader.SetFloat("AgeInfluence", GetAgeInfluence(ageRange));
                skinShader.SetFloat("GenderInfluence", gender == "female" ? 0.0f : 1.0f);
                
                int threadGroupsX = Mathf.CeilToInt(width / 8.0f);
                int threadGroupsY = Mathf.CeilToInt(height / 8.0f);
                skinShader.Dispatch(kernelIndex, threadGroupsX, threadGroupsY, 1);
            }
            else
            {
                // Fallback: usar Material com shader personalizado
                Material skinMaterial = new Material(Shader.Find("Hidden/SkinGeneration"));
                if (skinMaterial.shader.name == "Hidden/SkinGeneration")
                {
                    skinMaterial.SetVector("_SkinColor", skinColor);
                    skinMaterial.SetFloat("_Roughness", roughness);
                    skinMaterial.SetFloat("_PoreIntensity", poreIntensity);
                    
                    Graphics.Blit(null, renderTexture, skinMaterial);
                }
                else
                {
                    // Fallback final: geração simples com Graphics.Blit
                    GenerateBasicSkinTexture(renderTexture, skinColor, roughness, poreIntensity);
                }
            }

            // Converter RenderTexture para Texture2D
            Texture2D result = RenderTextureToTexture2D(renderTexture, format, enableMipmaps);
            
            // Limpar resources
            renderTexture.Release();
            
            return result;
        }

        /// <summary>
        /// [UNITY 6.2] - Gera textura de pele usando CPU com otimizações.
        /// </summary>
        private static Texture2D GenerateProceduralSkinTextureCPU(int width, int height, Color skinColor, float roughness, float poreIntensity, string skinType, string ageRange, string gender, TextureFormat format, bool enableMipmaps)
        {
            Texture2D skinTexture = new Texture2D(width, height, format, enableMipmaps);
            
            // Usar Unity Job System para paralelização se disponível
            Color[] pixels = new Color[width * height];
            
            float ageInfluence = GetAgeInfluence(ageRange);
            float genderInfluence = gender == "female" ? 0.0f : 1.0f;
            
            // Gerar ruído para variação de cor e textura
            for (int y = 0; y < height; y++)
            {
                for (int x = 0; x < width; x++)
                {
                    int index = y * width + x;
                    
                    // Coordenadas normalizadas
                    float u = (float)x / width;
                    float v = (float)y / height;
                    
                    // Ruído base para variação de cor
                    float noise1 = Mathf.PerlinNoise(u * 10f, v * 10f);
                    float noise2 = Mathf.PerlinNoise(u * 50f, v * 50f) * 0.3f;
                    float combinedNoise = noise1 + noise2;
                    
                    // Variação de cor baseada no tipo de pele
                    Color baseColor = GetSkinColorVariation(skinColor, combinedNoise, skinType);
                    
                    // Adicionar textura de poros
                    float poreNoise = Mathf.PerlinNoise(u * 200f, v * 200f);
                    if (poreNoise > 0.7f)
                    {
                        float poreDarkness = poreIntensity * (poreNoise - 0.7f) / 0.3f;
                        baseColor = Color.Lerp(baseColor, baseColor * 0.5f, poreDarkness);
                    }
                    
                    // Efeitos de idade
                    if (ageInfluence > 0.5f)
                    {
                        // Adicionar rugas e linhas de expressão
                        float wrinkleNoise = Mathf.PerlinNoise(u * 100f + 0.5f, v * 100f + 0.5f);
                        if (wrinkleNoise > 0.8f)
                        {
                            baseColor = Color.Lerp(baseColor, baseColor * 0.8f, (ageInfluence - 0.5f) * 2f);
                        }
                    }
                    
                    pixels[index] = baseColor;
                }
            }
            
            skinTexture.SetPixels(pixels);
            skinTexture.Apply(enableMipmaps);
            
            return skinTexture;
        }

        /// <summary>
        /// [HELPER] - Verifica se número é potência de 2.
        /// </summary>
        private static bool IsPowerOfTwo(int value)
        {
            return value > 0 && (value & (value - 1)) == 0;
        }

        /// <summary>
        /// [UNITY 6.2] - Salva textura com compressão otimizada.
        /// </summary>
        private static void SaveTextureOptimized(Texture2D texture, string path, string quality)
        {
            byte[] data = texture.EncodeToPNG();
            File.WriteAllBytes(path, data);
            
            AssetDatabase.Refresh();
            
            // Configurar import settings para otimização
            TextureImporter importer = AssetImporter.GetAtPath(path) as TextureImporter;
            if (importer != null)
            {
                importer.textureType = TextureImporterType.Default;
                importer.mipmapEnabled = texture.mipmapCount > 1;
                
                // Configurar compressão baseada na qualidade
                var platformSettings = importer.GetPlatformTextureSettings("Standalone");
                platformSettings.format = quality switch
                {
                    "low" => TextureImporterFormat.DXT1,
                    "medium" => TextureImporterFormat.DXT5,
                    "high" => TextureImporterFormat.BC7,
                    "ultra" => TextureImporterFormat.RGBA32,
                    _ => TextureImporterFormat.DXT5
                };
                
                importer.SetPlatformTextureSettings(platformSettings);
                AssetDatabase.ImportAsset(path);
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Gera normal map a partir de height map.
        /// </summary>
        private static Texture2D GenerateNormalMapFromHeightMap(Texture2D heightMap, float strength)
        {
            int width = heightMap.width;
            int height = heightMap.height;
            
            Texture2D normalMap = new Texture2D(width, height, TextureFormat.RGBA32, true);
            Color[] pixels = new Color[width * height];
            Color[] heightPixels = heightMap.GetPixels();
            
            for (int y = 0; y < height; y++)
            {
                for (int x = 0; x < width; x++)
                {
                    int index = y * width + x;
                    
                    // Calcular gradientes
                    float left = GetHeightValueSafe(heightPixels, x - 1, y, width, height);
                    float right = GetHeightValueSafe(heightPixels, x + 1, y, width, height);
                    float up = GetHeightValueSafe(heightPixels, x, y + 1, width, height);
                    float down = GetHeightValueSafe(heightPixels, x, y - 1, width, height);
                    
                    float dx = (right - left) * strength;
                    float dy = (up - down) * strength;
                    
                    // Calcular normal
                    Vector3 normal = new Vector3(-dx, -dy, 1.0f).normalized;
                    
                    // Converter para formato de normal map (0-1 range)
                    pixels[index] = new Color(
                        normal.x * 0.5f + 0.5f,
                        normal.y * 0.5f + 0.5f,
                        normal.z * 0.5f + 0.5f,
                        1.0f
                    );
                }
            }
            
            normalMap.SetPixels(pixels);
            normalMap.Apply(true);
            
            return normalMap;
        }

        /// <summary>
        /// [UNITY 6.2] - Gera mapa de roughness baseado na textura difusa.
        /// </summary>
        private static Texture2D GenerateRoughnessMap(Texture2D diffuseMap, float baseRoughness)
        {
            int width = diffuseMap.width;
            int height = diffuseMap.height;
            
            Texture2D roughnessMap = new Texture2D(width, height, TextureFormat.R8, true);
            Color[] diffusePixels = diffuseMap.GetPixels();
            Color[] roughnessPixels = new Color[width * height];
            
            for (int i = 0; i < diffusePixels.Length; i++)
            {
                // Calcular roughness baseado na luminância do pixel
                float luminance = diffusePixels[i].grayscale;
                float roughness = Mathf.Lerp(baseRoughness * 0.8f, baseRoughness * 1.2f, 1.0f - luminance);
                
                roughnessPixels[i] = new Color(roughness, roughness, roughness, 1.0f);
            }
            
            roughnessMap.SetPixels(roughnessPixels);
            roughnessMap.Apply(true);
            
            return roughnessMap;
        }

        /// <summary>
        /// [HELPER] - Sistema de logging para operações.
        /// </summary>
        private static void LogOperation(string operation, string details, bool isError = false)
        {
            string message = $"[ProceduralTextureGeneration] {operation}: {details}";
            if (isError)
                Debug.LogError(message);
            else
                Debug.Log(message);
        }

        /// <summary>
        /// [HELPER] - Converte RenderTexture para Texture2D.
        /// </summary>
        private static Texture2D RenderTextureToTexture2D(RenderTexture renderTexture, TextureFormat format, bool mipmaps)
        {
            RenderTexture previousActive = RenderTexture.active;
            RenderTexture.active = renderTexture;
            
            Texture2D texture = new Texture2D(renderTexture.width, renderTexture.height, format, mipmaps);
            texture.ReadPixels(new Rect(0, 0, renderTexture.width, renderTexture.height), 0, 0);
            texture.Apply();
            
            RenderTexture.active = previousActive;
            return texture;
        }

        /// <summary>
        /// [HELPER] - Obtém influência da idade.
        /// </summary>
        private static float GetAgeInfluence(string ageRange)
        {
            return ageRange switch
            {
                "child" => 0.0f,
                "teen" => 0.2f,
                "adult" => 0.5f,
                "middle_aged" => 0.7f,
                "elderly" => 1.0f,
                _ => 0.5f
            };
        }

        /// <summary>
        /// [HELPER] - Gera variação de cor de pele.
        /// </summary>
        private static Color GetSkinColorVariation(Color baseColor, float noise, string skinType)
        {
            float variation = (noise - 0.5f) * 0.1f; // Variação sutil
            
            Color result = baseColor;
            result.r = Mathf.Clamp01(result.r + variation);
            result.g = Mathf.Clamp01(result.g + variation * 0.8f);
            result.b = Mathf.Clamp01(result.b + variation * 0.6f);
            
            return result;
        }

        /// <summary>
        /// [HELPER] - Obtém valor de altura de forma segura.
        /// </summary>
        private static float GetHeightValueSafe(Color[] pixels, int x, int y, int width, int height)
        {
            x = Mathf.Clamp(x, 0, width - 1);
            y = Mathf.Clamp(y, 0, height - 1);
            return pixels[y * width + x].grayscale;
        }

        /// <summary>
        /// [HELPER] - Gera textura básica de pele usando Graphics.Blit.
        /// </summary>
        private static void GenerateBasicSkinTexture(RenderTexture target, Color skinColor, float roughness, float poreIntensity)
        {
            // Criar material temporário para geração básica
            Material tempMaterial = new Material(Shader.Find("Unlit/Color"));
            tempMaterial.color = skinColor;
            
            Graphics.Blit(null, target, tempMaterial);
            
            // Limpar material temporário
            UnityEngine.Object.DestroyImmediate(tempMaterial);
        }

        /// <summary>
        /// [UNITY 6.2] - Gera textura procedural de pele.
        /// </summary>
        private static Texture2D GenerateProceduralSkinTexture(int width, int height, Color baseColor, float roughness, float poreIntensity)
        {
            Texture2D texture = new Texture2D(width, height, TextureFormat.RGBA32, true);
            
            for (int x = 0; x < width; x++)
            {
                for (int y = 0; y < height; y++)
                {
                    // Gerar ruído para poros
                    float poreNoise = Mathf.PerlinNoise(x * 0.1f, y * 0.1f) * poreIntensity;
                    float detailNoise = Mathf.PerlinNoise(x * 0.05f, y * 0.05f) * 0.1f;
                    
                    // Cor base com variação
                    Color pixelColor = baseColor;
                    pixelColor.r += (poreNoise + detailNoise - 0.05f);
                    pixelColor.g += (poreNoise + detailNoise - 0.05f);
                    pixelColor.b += (poreNoise + detailNoise - 0.05f);
                    
                    // Clampar valores
                    pixelColor.r = Mathf.Clamp01(pixelColor.r);
                    pixelColor.g = Mathf.Clamp01(pixelColor.g);
                    pixelColor.b = Mathf.Clamp01(pixelColor.b);
                    
                    texture.SetPixel(x, y, pixelColor);
                }
            }
            
            texture.Apply();
            return texture;
        }

        /// <summary>
        /// [UNITY 6.2] - Simula uso do AI Generator (placeholder para API real).
        /// </summary>
        private static Texture2D GenerateWithAI(string category, string type, int width, int height, Color baseColor)
        {
            Debug.Log($"[AI Generator] Generating {category} texture of type {type} using advanced algorithms");

            // Usar RenderTexture para geração GPU-accelerated quando possível
            if (SystemInfo.supportsComputeShaders)
            {
                return GenerateWithComputeShader(category, type, width, height, baseColor);
            }

            // Fallback para geração CPU com algoritmos avançados
            Texture2D texture = new Texture2D(width, height, TextureFormat.RGBA32, true);

            // Algoritmos específicos por categoria
            switch (category.ToLower())
            {
                case "skin":
                    GenerateSkinPattern(texture, width, height, baseColor);
                    break;
                case "metal":
                    GenerateMetalPattern(texture, width, height, baseColor);
                    break;
                case "fabric":
                    GenerateFabricPattern(texture, width, height, baseColor);
                    break;
                default:
                    GenerateGenericPattern(texture, width, height, baseColor);
                    break;
            }

            texture.Apply();
            return texture;
        }

        /// <summary>
        /// [UNITY 6.2] - Gera texturas usando Compute Shaders para performance.
        /// </summary>
        private static Texture2D GenerateWithComputeShader(string category, string type, int width, int height, Color baseColor)
        {
            // Criar RenderTexture para compute shader
            RenderTexture renderTexture = new RenderTexture(width, height, 0, RenderTextureFormat.ARGB32);
            renderTexture.enableRandomWrite = true;
            renderTexture.Create();

            // Simular compute shader com Graphics.Blit e material customizado
            Material generatorMaterial = CreateGeneratorMaterial(category, type);
            if (generatorMaterial != null)
            {
                generatorMaterial.SetColor("_BaseColor", baseColor);
                generatorMaterial.SetFloat("_Time", Time.realtimeSinceStartup);
                generatorMaterial.SetVector("_Resolution", new Vector4(width, height, 1.0f / width, 1.0f / height));

                Graphics.Blit(null, renderTexture, generatorMaterial);
            }

            // Converter para Texture2D
            Texture2D result = new Texture2D(width, height, TextureFormat.RGBA32, true);
            RenderTexture.active = renderTexture;
            result.ReadPixels(new Rect(0, 0, width, height), 0, 0);
            result.Apply();
            RenderTexture.active = null;

            // Cleanup
            renderTexture.Release();
            if (generatorMaterial != null)
            {
                UnityEngine.Object.DestroyImmediate(generatorMaterial);
            }

            return result;
        }

        /// <summary>
        /// [UNITY 6.2] - Gera mapa normal a partir de height map.
        /// </summary>
        private static Texture2D GenerateNormalMapFromHeight(Texture2D heightMap)
        {
            int width = heightMap.width;
            int height = heightMap.height;
            Texture2D normalMap = new Texture2D(width, height, TextureFormat.RGBA32, true);
            
            for (int x = 0; x < width; x++)
            {
                for (int y = 0; y < height; y++)
                {
                    // Calcular gradiente para normal
                    float left = GetHeightValue(heightMap, x - 1, y);
                    float right = GetHeightValue(heightMap, x + 1, y);
                    float up = GetHeightValue(heightMap, x, y + 1);
                    float down = GetHeightValue(heightMap, x, y - 1);
                    
                    float dx = (right - left) * 0.5f;
                    float dy = (up - down) * 0.5f;
                    
                    Vector3 normal = new Vector3(-dx, -dy, 1.0f).normalized;
                    
                    // Converter para cor (0-1 range)
                    Color normalColor = new Color(
                        normal.x * 0.5f + 0.5f,
                        normal.y * 0.5f + 0.5f,
                        normal.z * 0.5f + 0.5f,
                        1.0f
                    );
                    
                    normalMap.SetPixel(x, y, normalColor);
                }
            }
            
            normalMap.Apply();
            return normalMap;
        }

        /// <summary>
        /// [HELPER] - Obtém valor de altura com wrap.
        /// </summary>
        private static float GetHeightValue(Texture2D texture, int x, int y)
        {
            x = Mathf.Clamp(x, 0, texture.width - 1);
            y = Mathf.Clamp(y, 0, texture.height - 1);
            return texture.GetPixel(x, y).grayscale;
        }

        /// <summary>
        /// [HELPER] - Salva textura como PNG.
        /// </summary>
        private static void SaveTexture(Texture2D texture, string path)
        {
            string directory = Path.GetDirectoryName(path);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }
            
            byte[] pngData = texture.EncodeToPNG();
            File.WriteAllBytes(path, pngData);
            
            AssetDatabase.ImportAsset(path);
            AssetDatabase.Refresh();
        }

        /// <summary>
        /// [UNITY 6.2] - Gera texturas de tecido com padrões realistas.
        /// </summary>
        private static object GenerateFabricTexture(JObject @params)
        {
            try
            {
                string outputPath = SanitizeAssetPath(@params["output_path"]?.ToString() ?? "Assets/GeneratedTextures/");
                var parameters = @params["parameters"] as JObject ?? new JObject();

                int width = @params["width"]?.ToObject<int>() ?? 1024;
                int height = @params["height"]?.ToObject<int>() ?? 1024;
                string fabricType = parameters["fabric_type"]?.ToString() ?? "cotton";
                Color baseColor = ParseColor(parameters["color_base"]) ?? Color.white;

                // Gerar textura de tecido
                Texture2D fabricTexture = GenerateWithAI("fabric", fabricType, width, height, baseColor);

                // Salvar textura
                string texturePath = SaveTexture(fabricTexture, outputPath, "GeneratedFabric");

                return Response.Success($"Textura de tecido gerada: {texturePath}", new
                {
                    texturePath = texturePath,
                    fabricType = fabricType,
                    resolution = $"{width}x{height}"
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao gerar textura de tecido: {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Gera texturas metálicas com reflexos e oxidação.
        /// </summary>
        private static object GenerateMetalTexture(JObject @params)
        {
            try
            {
                string outputPath = SanitizeAssetPath(@params["output_path"]?.ToString() ?? "Assets/GeneratedTextures/");
                var parameters = @params["parameters"] as JObject ?? new JObject();

                int width = @params["width"]?.ToObject<int>() ?? 1024;
                int height = @params["height"]?.ToObject<int>() ?? 1024;
                string metalType = parameters["metal_type"]?.ToString() ?? "steel";
                Color baseColor = ParseColor(parameters["color_base"]) ?? new Color(0.7f, 0.7f, 0.7f, 1.0f);

                // Gerar textura metálica
                Texture2D metalTexture = GenerateWithAI("metal", metalType, width, height, baseColor);

                // Gerar mapas adicionais se solicitado
                bool generateRoughness = @params["generate_roughness_map"]?.ToObject<bool>() ?? true;
                bool generateMetallic = @params["generate_metallic_map"]?.ToObject<bool>() ?? true;

                List<string> generatedMaps = new List<string>();
                string texturePath = SaveTexture(metalTexture, outputPath, "GeneratedMetal");
                generatedMaps.Add(texturePath);

                if (generateRoughness)
                {
                    Texture2D roughnessMap = GenerateRoughnessMap(metalTexture);
                    string roughnessPath = SaveTexture(roughnessMap, outputPath, "GeneratedMetal_Roughness");
                    generatedMaps.Add(roughnessPath);
                }

                if (generateMetallic)
                {
                    Texture2D metallicMap = GenerateMetallicMap(metalTexture);
                    string metallicPath = SaveTexture(metallicMap, outputPath, "GeneratedMetal_Metallic");
                    generatedMaps.Add(metallicPath);
                }

                return Response.Success($"Textura metálica gerada: {texturePath}", new
                {
                    diffuseMap = texturePath,
                    additionalMaps = generatedMaps.Skip(1).ToArray(),
                    metalType = metalType,
                    resolution = $"{width}x{height}"
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao gerar textura metálica: {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Gera mapas normais a partir de height maps.
        /// </summary>
        private static object GenerateNormalMap(JObject @params)
        {
            try
            {
                string outputPath = SanitizeAssetPath(@params["output_path"]?.ToString() ?? "Assets/GeneratedTextures/");
                string sourceTexturePath = @params["source_texture"]?.ToString();

                int width = @params["width"]?.ToObject<int>() ?? 1024;
                int height = @params["height"]?.ToObject<int>() ?? 1024;
                float strength = @params["normal_strength"]?.ToObject<float>() ?? 1.0f;

                Texture2D normalMap;

                if (!string.IsNullOrEmpty(sourceTexturePath))
                {
                    // Gerar normal map a partir de textura existente
                    Texture2D sourceTexture = AssetDatabase.LoadAssetAtPath<Texture2D>(sourceTexturePath);
                    if (sourceTexture == null)
                    {
                        return Response.Error($"Textura fonte não encontrada: {sourceTexturePath}");
                    }
                    normalMap = GenerateNormalMapFromHeight(sourceTexture);
                }
                else
                {
                    // Gerar normal map procedural
                    Texture2D heightMap = GenerateWithAI("height", "generic", width, height, Color.gray);
                    normalMap = GenerateNormalMapFromHeight(heightMap);
                }

                string normalPath = SaveTexture(normalMap, outputPath, "GeneratedNormal");

                return Response.Success($"Mapa normal gerado: {normalPath}", new
                {
                    normalMapPath = normalPath,
                    strength = strength,
                    resolution = $"{width}x{height}"
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao gerar mapa normal: {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Gera múltiplas texturas em lote.
        /// </summary>
        private static object GenerateBatchTextures(JObject @params)
        {
            try
            {
                string outputPath = SanitizeAssetPath(@params["output_path"]?.ToString() ?? "Assets/GeneratedTextures/");
                int batchCount = @params["batch_count"]?.ToObject<int>() ?? 5;
                string textureCategory = @params["texture_category"]?.ToString() ?? "generic";

                List<string> generatedTextures = new List<string>();

                for (int i = 0; i < batchCount; i++)
                {
                    // Variar parâmetros para cada textura
                    Color randomColor = new Color(
                        UnityEngine.Random.Range(0.3f, 0.9f),
                        UnityEngine.Random.Range(0.3f, 0.9f),
                        UnityEngine.Random.Range(0.3f, 0.9f),
                        1.0f
                    );

                    Texture2D batchTexture = GenerateWithAI(textureCategory, "variant", 512, 512, randomColor);
                    string texturePath = SaveTexture(batchTexture, outputPath, $"BatchTexture_{i:D3}");
                    generatedTextures.Add(texturePath);
                }

                return Response.Success($"Lote de {batchCount} texturas gerado", new
                {
                    textureCount = batchCount,
                    texturePaths = generatedTextures.ToArray(),
                    category = textureCategory
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao gerar lote de texturas: {e.Message}");
            }
        }

        private static object ListPresets(JObject @params)
        {
            var presets = new[]
            {
                new { name = "Caucasian_Skin", category = "skin", description = "Pele caucasiana realística" },
                new { name = "Dark_Skin", category = "skin", description = "Pele escura com detalhes" },
                new { name = "Cotton_Fabric", category = "fabric", description = "Tecido de algodão" },
                new { name = "Brushed_Metal", category = "metal", description = "Metal escovado" }
            };
            
            return Response.Success("Presets listados com sucesso", presets);
        }

        private static object GetTextureInfo(string path)
        {
            if (string.IsNullOrEmpty(path))
                return Response.Error("Path é obrigatório para get_info");

            string sanitizedPath = SanitizeAssetPath(path);
            if (!AssetExists(sanitizedPath))
                return Response.Error($"Textura não encontrada: {sanitizedPath}");

            Texture2D texture = AssetDatabase.LoadAssetAtPath<Texture2D>(sanitizedPath);
            if (texture == null)
                return Response.Error($"Erro ao carregar textura: {sanitizedPath}");

            return Response.Success("Informações da textura obtidas", new
            {
                path = sanitizedPath,
                name = texture.name,
                width = texture.width,
                height = texture.height,
                format = texture.format.ToString(),
                mipmapCount = texture.mipmapCount,
                isReadable = texture.isReadable
            });
        }

        // ... helper methods ...
        private static bool IsUnityAIAvailable()
        {
            // Verificar se Unity.AI.Generators está disponível através de reflection
            try
            {
                var aiAssembly = System.Reflection.Assembly.Load("Unity.AI.Generators");
                return aiAssembly != null;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Gera padrão específico para pele humana.
        /// </summary>
        private static void GenerateSkinPattern(Texture2D texture, int width, int height, Color baseColor)
        {
            for (int x = 0; x < width; x++)
            {
                for (int y = 0; y < height; y++)
                {
                    // Múltiplas camadas de ruído para simular pele
                    float poreNoise = Mathf.PerlinNoise(x * 0.1f, y * 0.1f) * 0.1f;
                    float skinVariation = Mathf.PerlinNoise(x * 0.02f, y * 0.02f) * 0.3f;
                    float microDetail = Mathf.PerlinNoise(x * 0.5f, y * 0.5f) * 0.05f;

                    float combinedNoise = poreNoise + skinVariation + microDetail;
                    Color pixelColor = Color.Lerp(baseColor * 0.9f, baseColor * 1.1f, combinedNoise);
                    pixelColor.a = 1.0f;

                    texture.SetPixel(x, y, pixelColor);
                }
            }
        }

        /// <summary>
        /// Gera padrão metálico com reflexos.
        /// </summary>
        private static void GenerateMetalPattern(Texture2D texture, int width, int height, Color baseColor)
        {
            for (int x = 0; x < width; x++)
            {
                for (int y = 0; y < height; y++)
                {
                    // Padrão metálico com riscos e reflexos
                    float scratches = Mathf.PerlinNoise(x * 0.3f, y * 0.05f) * 0.2f;
                    float reflection = Mathf.PerlinNoise(x * 0.01f, y * 0.01f) * 0.4f;
                    float oxidation = Mathf.PerlinNoise(x * 0.08f, y * 0.08f) * 0.15f;

                    float metallic = scratches + reflection - oxidation;
                    Color pixelColor = Color.Lerp(baseColor * 0.7f, baseColor * 1.3f, metallic);
                    pixelColor.a = 1.0f;

                    texture.SetPixel(x, y, pixelColor);
                }
            }
        }

        /// <summary>
        /// Gera padrão de tecido com fibras.
        /// </summary>
        private static void GenerateFabricPattern(Texture2D texture, int width, int height, Color baseColor)
        {
            for (int x = 0; x < width; x++)
            {
                for (int y = 0; y < height; y++)
                {
                    // Padrão de tecido com trama
                    float warpThread = Mathf.Sin(x * 0.2f) * 0.1f;
                    float weftThread = Mathf.Sin(y * 0.2f) * 0.1f;
                    float fiberNoise = Mathf.PerlinNoise(x * 0.1f, y * 0.1f) * 0.2f;

                    float fabricPattern = warpThread + weftThread + fiberNoise;
                    Color pixelColor = Color.Lerp(baseColor * 0.8f, baseColor * 1.2f, fabricPattern);
                    pixelColor.a = 1.0f;

                    texture.SetPixel(x, y, pixelColor);
                }
            }
        }

        /// <summary>
        /// Gera padrão genérico com ruído avançado.
        /// </summary>
        private static void GenerateGenericPattern(Texture2D texture, int width, int height, Color baseColor)
        {
            for (int x = 0; x < width; x++)
            {
                for (int y = 0; y < height; y++)
                {
                    // Múltiplas camadas de ruído
                    float noise1 = Mathf.PerlinNoise(x * 0.01f, y * 0.01f);
                    float noise2 = Mathf.PerlinNoise(x * 0.05f, y * 0.05f) * 0.5f;
                    float noise3 = Mathf.PerlinNoise(x * 0.1f, y * 0.1f) * 0.25f;

                    float combinedNoise = (noise1 + noise2 + noise3) / 1.75f;
                    Color pixelColor = Color.Lerp(baseColor * 0.8f, baseColor * 1.2f, combinedNoise);
                    pixelColor.a = 1.0f;

                    texture.SetPixel(x, y, pixelColor);
                }
            }
        }

        /// <summary>
        /// Cria material para geração procedural com shaders.
        /// </summary>
        private static Material CreateGeneratorMaterial(string category, string type)
        {
            // Criar shader procedural básico
            string shaderCode = GenerateProceduralShader(category, type);
            Shader proceduralShader = ShaderUtil.CreateShaderAsset(shaderCode);

            if (proceduralShader != null)
            {
                return new Material(proceduralShader);
            }

            // Fallback para shader padrão
            return new Material(Shader.Find("Unlit/Texture"));
        }

        /// <summary>
        /// Gera código de shader procedural baseado na categoria.
        /// </summary>
        private static string GenerateProceduralShader(string category, string type)
        {
            return @"
Shader ""Custom/ProceduralGenerator""
{
    Properties
    {
        _BaseColor (""Base Color"", Color) = (1,1,1,1)
        _Time (""Time"", Float) = 0
        _Resolution (""Resolution"", Vector) = (1024,1024,0,0)
    }
    SubShader
    {
        Tags { ""RenderType""=""Opaque"" }
        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include ""UnityCG.cginc""

            struct appdata { float4 vertex : POSITION; float2 uv : TEXCOORD0; };
            struct v2f { float2 uv : TEXCOORD0; float4 vertex : SV_POSITION; };

            float4 _BaseColor;
            float _Time;
            float4 _Resolution;

            v2f vert (appdata v) {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = v.uv;
                return o;
            }

            fixed4 frag (v2f i) : SV_Target {
                float2 uv = i.uv;
                float noise = sin(uv.x * 10 + _Time) * cos(uv.y * 10 + _Time) * 0.5 + 0.5;
                return _BaseColor * noise;
            }
            ENDCG
        }
    }
}";
        }

        /// <summary>
        /// Gera mapa de rugosidade a partir de textura base.
        /// </summary>
        private static Texture2D GenerateRoughnessMap(Texture2D baseTexture)
        {
            int width = baseTexture.width;
            int height = baseTexture.height;
            Texture2D roughnessMap = new Texture2D(width, height, TextureFormat.RGBA32, true);

            for (int x = 0; x < width; x++)
            {
                for (int y = 0; y < height; y++)
                {
                    Color baseColor = baseTexture.GetPixel(x, y);
                    float roughness = 1.0f - baseColor.grayscale; // Inverter para rugosidade
                    Color roughnessColor = new Color(roughness, roughness, roughness, 1.0f);
                    roughnessMap.SetPixel(x, y, roughnessColor);
                }
            }

            roughnessMap.Apply();
            return roughnessMap;
        }

        /// <summary>
        /// Gera mapa metálico a partir de textura base.
        /// </summary>
        private static Texture2D GenerateMetallicMap(Texture2D baseTexture)
        {
            int width = baseTexture.width;
            int height = baseTexture.height;
            Texture2D metallicMap = new Texture2D(width, height, TextureFormat.RGBA32, true);

            for (int x = 0; x < width; x++)
            {
                for (int y = 0; y < height; y++)
                {
                    Color baseColor = baseTexture.GetPixel(x, y);
                    float metallic = baseColor.grayscale > 0.5f ? 1.0f : 0.0f; // Threshold para metal
                    Color metallicColor = new Color(metallic, metallic, metallic, 1.0f);
                    metallicMap.SetPixel(x, y, metallicColor);
                }
            }

            metallicMap.Apply();
            return metallicMap;
        }

        /// <summary>
        /// Salva textura no disco com configurações otimizadas.
        /// </summary>
        private static string SaveTexture(Texture2D texture, string outputPath, string fileName)
        {
            // Criar diretório se não existir
            if (!Directory.Exists(outputPath))
            {
                Directory.CreateDirectory(outputPath);
            }

            // Salvar como PNG
            byte[] textureData = texture.EncodeToPNG();
            string fullPath = Path.Combine(outputPath, fileName + ".png");
            File.WriteAllBytes(fullPath, textureData);

            // Refresh asset database
            AssetDatabase.Refresh();

            // Configurar import settings
            TextureImporter importer = AssetImporter.GetAtPath(fullPath) as TextureImporter;
            if (importer != null)
            {
                importer.textureType = TextureImporterType.Default;
                importer.mipmapEnabled = true;
                importer.isReadable = false;
                importer.SaveAndReimport();
            }

            return fullPath;
        }

        private static Color? ParseColor(JToken colorToken)
        {
            if (colorToken == null) return null;
            
            if (colorToken is JArray colorArray && colorArray.Count >= 3)
            {
                return new Color(
                    colorArray[0].ToObject<float>(),
                    colorArray[1].ToObject<float>(),
                    colorArray[2].ToObject<float>(),
                    colorArray.Count > 3 ? colorArray[3].ToObject<float>() : 1.0f
                );
            }
            
            return null;
        }

        private static string SanitizeAssetPath(string path)
        {
            if (string.IsNullOrEmpty(path)) return "Assets/";
            path = path.Replace('\\', '/').Trim();
            if (!path.StartsWith("Assets/")) path = "Assets/" + path;
            if (!path.EndsWith("/")) path += "/";
            return path;
        }

        private static bool AssetExists(string sanitizedPath)
        {
            return !string.IsNullOrEmpty(AssetDatabase.AssetPathToGUID(sanitizedPath));
        }
    }
}