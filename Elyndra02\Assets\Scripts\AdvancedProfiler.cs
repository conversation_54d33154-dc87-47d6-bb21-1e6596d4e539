using UnityEngine;
using Unity.Profiling;
using System.Collections.Generic;
using System.Text;

public class AdvancedProfiler : MonoBehaviour
{
    [Header("Profiling Settings")]
    public bool enableProfiling = true;
    public float updateInterval = 1f;
    public bool showOnScreen = true;
    
    [Header("Performance Metrics")]
    public bool trackFPS = true;
    public bool trackMemory = true;
    public bool trackDrawCalls = true;
    public bool trackBatches = true;
    
    private ProfilerRecorder systemMemoryRecorder;
    private ProfilerRecorder gcMemoryRecorder;
    private ProfilerRecorder mainThreadTimeRecorder;
    private ProfilerRecorder drawCallsRecorder;
    private ProfilerRecorder batchesRecorder;
    
    private float fps;
    private float frameTime;
    private long systemMemory;
    private long gcMemory;
    private int drawCalls;
    private int batches;
    
    private float lastUpdateTime;
    private List<float> fpsHistory;
    private const int maxHistorySize = 60;
    
    void Start()
    {
        InitializeProfiler();
    }
    
    void InitializeProfiler()
    {
        fpsHistory = new List<float>();
        
        if (enableProfiling)
        {
            systemMemoryRecorder = ProfilerRecorder.StartNew(ProfilerCategory.Memory, "System Used Memory");
            gcMemoryRecorder = ProfilerRecorder.StartNew(ProfilerCategory.Memory, "GC Reserved Memory");
            mainThreadTimeRecorder = ProfilerRecorder.StartNew(ProfilerCategory.Internal, "Main Thread", 15);
            drawCallsRecorder = ProfilerRecorder.StartNew(ProfilerCategory.Render, "Draw Calls Count");
            batchesRecorder = ProfilerRecorder.StartNew(ProfilerCategory.Render, "Batches Count");
        }
        
        Debug.Log("Advanced Profiler initialized");
    }
    
    void Update()
    {
        if (!enableProfiling) return;
        
        if (Time.time - lastUpdateTime >= updateInterval)
        {
            UpdateMetrics();
            lastUpdateTime = Time.time;
        }
    }
    
    void UpdateMetrics()
    {
        // FPS Calculation
        if (trackFPS)
        {
            fps = 1f / Time.unscaledDeltaTime;
            frameTime = Time.unscaledDeltaTime * 1000f;
            
            fpsHistory.Add(fps);
            if (fpsHistory.Count > maxHistorySize)
            {
                fpsHistory.RemoveAt(0);
            }
        }
        
        // Memory Metrics
        if (trackMemory)
        {
            systemMemory = systemMemoryRecorder.LastValue;
            gcMemory = gcMemoryRecorder.LastValue;
        }
        
        // Render Metrics
        if (trackDrawCalls)
        {
            drawCalls = (int)drawCallsRecorder.LastValue;
        }
        
        if (trackBatches)
        {
            batches = (int)batchesRecorder.LastValue;
        }
    }
    
    public float GetAverageFPS()
    {
        if (fpsHistory.Count == 0) return 0f;
        
        float sum = 0f;
        foreach (float f in fpsHistory)
        {
            sum += f;
        }
        return sum / fpsHistory.Count;
    }
    
    public float GetMinFPS()
    {
        if (fpsHistory.Count == 0) return 0f;
        
        float min = float.MaxValue;
        foreach (float f in fpsHistory)
        {
            if (f < min) min = f;
        }
        return min;
    }
    
    public float GetMaxFPS()
    {
        if (fpsHistory.Count == 0) return 0f;
        
        float max = 0f;
        foreach (float f in fpsHistory)
        {
            if (f > max) max = f;
        }
        return max;
    }
    
    public string GetPerformanceReport()
    {
        StringBuilder report = new StringBuilder();
        
        report.AppendLine("=== PERFORMANCE REPORT ===");
        report.AppendLine($"FPS: {fps:F1} (Avg: {GetAverageFPS():F1}, Min: {GetMinFPS():F1}, Max: {GetMaxFPS():F1})");
        report.AppendLine($"Frame Time: {frameTime:F2}ms");
        report.AppendLine($"System Memory: {systemMemory / (1024 * 1024)}MB");
        report.AppendLine($"GC Memory: {gcMemory / (1024 * 1024)}MB");
        report.AppendLine($"Draw Calls: {drawCalls}");
        report.AppendLine($"Batches: {batches}");
        
        return report.ToString();
    }
    
    void OnGUI()
    {
        if (!showOnScreen || !enableProfiling) return;
        
        GUILayout.BeginArea(new Rect(Screen.width - 250, 10, 240, 200));
        
        GUILayout.Label("PERFORMANCE METRICS", GUI.skin.box);
        
        if (trackFPS)
        {
            Color originalColor = GUI.color;
            if (fps < 30) GUI.color = Color.red;
            else if (fps < 60) GUI.color = Color.yellow;
            else GUI.color = Color.green;
            
            GUILayout.Label($"FPS: {fps:F1}");
            GUILayout.Label($"Frame: {frameTime:F2}ms");
            GUI.color = originalColor;
        }
        
        if (trackMemory)
        {
            GUILayout.Label($"System: {systemMemory / (1024 * 1024)}MB");
            GUILayout.Label($"GC: {gcMemory / (1024 * 1024)}MB");
        }
        
        if (trackDrawCalls)
        {
            GUILayout.Label($"Draw Calls: {drawCalls}");
        }
        
        if (trackBatches)
        {
            GUILayout.Label($"Batches: {batches}");
        }
        
        GUILayout.EndArea();
    }
    
    void OnDestroy()
    {
        systemMemoryRecorder.Dispose();
        gcMemoryRecorder.Dispose();
        mainThreadTimeRecorder.Dispose();
        drawCallsRecorder.Dispose();
        batchesRecorder.Dispose();
    }
}