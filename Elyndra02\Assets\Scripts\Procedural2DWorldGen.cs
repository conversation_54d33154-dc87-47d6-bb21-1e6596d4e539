using UnityEngine;
using UnityEngine.Tilemaps;

public class Procedural2DWorldGen : MonoBehaviour
{
    [Header("World Generation")]
    public int worldWidth = 100;
    public int worldHeight = 100;
    public float noiseScale = 0.1f;
    public int seed = 12345;
    
    [Header("Terrain Types")]
    public float waterLevel = 0.3f;
    public float grassLevel = 0.6f;
    public float mountainLevel = 0.8f;
    
    private Tilemap tilemap;
    private TilemapRenderer tilemapRenderer;
    
    void Start()
    {
        SetupTilemap();
        GenerateWorld();
    }
    
    void SetupTilemap()
    {
        GameObject tilemapObj = new GameObject("GeneratedTilemap");
        tilemap = tilemapObj.AddComponent<Tilemap>();
        tilemapRenderer = tilemapObj.AddComponent<TilemapRenderer>();
        
        GameObject gridObj = new GameObject("Grid");
        Grid grid = gridObj.AddComponent<Grid>();
        tilemapObj.transform.SetParent(gridObj.transform);
    }
    
    void GenerateWorld()
    {
        Random.InitState(seed);
        
        for (int x = 0; x < worldWidth; x++)
        {
            for (int y = 0; y < worldHeight; y++)
            {
                float noiseValue = Mathf.PerlinNoise(x * noiseScale, y * noiseScale);
                Vector3Int position = new Vector3Int(x, y, 0);
                
                // Create simple colored tiles based on height
                if (noiseValue < waterLevel)
                {
                    // Water - Blue
                    CreateColorTile(position, Color.blue);
                }
                else if (noiseValue < grassLevel)
                {
                    // Grass - Green
                    CreateColorTile(position, Color.green);
                }
                else if (noiseValue < mountainLevel)
                {
                    // Hills - Brown
                    CreateColorTile(position, new Color(0.6f, 0.4f, 0.2f));
                }
                else
                {
                    // Mountains - Gray
                    CreateColorTile(position, Color.gray);
                }
            }
        }
        
        Debug.Log($"Generated 2D world: {worldWidth}x{worldHeight} tiles");
    }
    
    void CreateColorTile(Vector3Int position, Color color)
    {
        // Create a simple sprite for the tile
        Texture2D texture = new Texture2D(1, 1);
        texture.SetPixel(0, 0, color);
        texture.Apply();
        
        Sprite sprite = Sprite.Create(texture, new Rect(0, 0, 1, 1), Vector2.one * 0.5f, 1);
        
        // Create tile asset
        Tile tile = ScriptableObject.CreateInstance<Tile>();
        tile.sprite = sprite;
        
        tilemap.SetTile(position, tile);
    }
    
    public void RegenerateWorld()
    {
        tilemap.SetTilesBlock(new BoundsInt(0, 0, 0, worldWidth, worldHeight, 1), new TileBase[worldWidth * worldHeight]);
        GenerateWorld();
    }
}