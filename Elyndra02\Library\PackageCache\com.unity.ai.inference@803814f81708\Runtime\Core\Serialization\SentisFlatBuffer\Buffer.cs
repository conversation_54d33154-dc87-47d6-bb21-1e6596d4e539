// <auto-generated>
//  automatically generated by the FlatBuffers compiler, do not modify
// </auto-generated>
#define ENABLE_SPAN_T
#define UNSAFE_BYTEBUFFER
#define BYTEBUFFER_NO_BOUNDS_CHECK

namespace SentisFlatBuffer
{

using global::System;
using global::System.Collections.Generic;
using global::Unity.InferenceEngine.Google.FlatBuffers;

struct Buffer : IFlatbufferObject
{
  private Table __p;
  public ByteBuffer ByteBuffer { get { return __p.bb; } }
  public static void ValidateVersion() { FlatBufferConstants.FLATBUFFERS_23_5_26(); }
  public static Buffer GetRootAsBuffer(ByteBuffer _bb) { return GetRootAsBuffer(_bb, new Buffer()); }
  public static Buffer GetRootAsBuffer(ByteBuffer _bb, Buffer obj) { return (obj.__assign(_bb.GetInt(_bb.Position) + _bb.Position, _bb)); }
  public void __init(int _i, Byte<PERSON>uffer _bb) { __p = new Table(_i, _bb); }
  public Buffer __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public byte Storage(int j) { int o = __p.__offset(4); return o != 0 ? __p.bb.Get(__p.__vector(o) + j * 1) : (byte)0; }
  public int StorageLength { get { int o = __p.__offset(4); return o != 0 ? __p.__vector_len(o) : 0; } }
#if ENABLE_SPAN_T
  public Span<byte> GetStorageBytes() { return __p.__vector_as_span<byte>(4, 1); }
#else
  public ArraySegment<byte>? GetStorageBytes() { return __p.__vector_as_arraysegment(4); }
#endif
  public byte[] GetStorageArray() { return __p.__vector_as_array<byte>(4); }

  public static Offset<SentisFlatBuffer.Buffer> CreateBuffer(FlatBufferBuilder builder,
      VectorOffset storageOffset = default(VectorOffset)) {
    builder.StartTable(1);
    Buffer.AddStorage(builder, storageOffset);
    return Buffer.EndBuffer(builder);
  }

  public static void StartBuffer(FlatBufferBuilder builder) { builder.StartTable(1); }
  public static void AddStorage(FlatBufferBuilder builder, VectorOffset storageOffset) { builder.AddOffset(0, storageOffset.Value, 0); }
  public static VectorOffset CreateStorageVector(FlatBufferBuilder builder, byte[] data) { builder.StartVector(1, data.Length, 1); for (int i = data.Length - 1; i >= 0; i--) builder.AddByte(data[i]); return builder.EndVector(); }
  public static VectorOffset CreateStorageVectorBlock(FlatBufferBuilder builder, byte[] data) { builder.StartVector(1, data.Length, 1); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateStorageVectorBlock(FlatBufferBuilder builder, ArraySegment<byte> data) { builder.StartVector(1, data.Count, 1); builder.Add(data); return builder.EndVector(); }
  public static VectorOffset CreateStorageVectorBlock(FlatBufferBuilder builder, IntPtr dataPtr, int sizeInBytes) { builder.StartVector(1, sizeInBytes, 1); builder.Add<byte>(dataPtr, sizeInBytes); return builder.EndVector(); }
  public static void StartStorageVector(FlatBufferBuilder builder, int numElems) { builder.StartVector(1, numElems, 1); }
  public static Offset<SentisFlatBuffer.Buffer> EndBuffer(FlatBufferBuilder builder) {
    int o = builder.EndTable();
    return new Offset<SentisFlatBuffer.Buffer>(o);
  }
}


static class BufferVerify
{
  static public bool Verify(Unity.InferenceEngine.Google.FlatBuffers.Verifier verifier, uint tablePos)
  {
    return verifier.VerifyTableStart(tablePos)
      && verifier.VerifyVectorOfData(tablePos, 4 /*Storage*/, 1 /*byte*/, false)
      && verifier.VerifyTableEnd(tablePos);
  }
}

}
