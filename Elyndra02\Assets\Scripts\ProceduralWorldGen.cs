using UnityEngine;
using System.Collections.Generic;

public class ProceduralWorldGen : MonoBehaviour
{
    [Header("Generation")]
    public int mapSize = 50;
    public float noiseScale = 0.1f;
    public int seed = 12345;
    
    [Header("Objects")]
    public GameObject cubePrefab;
    public float spawnChance = 0.1f;
    public float maxHeight = 10f;
    
    private List<GameObject> generated = new List<GameObject>();
    
    void Start()
    {
        InitGenerator();
        Generate();
    }
    
    void InitGenerator()
    {
        Random.InitState(seed);
        Debug.Log($"Procedural World Generator initialized! Size: {mapSize}, Seed: {seed}");
    }
    
    void Generate()
    {
        for (int x = 0; x < mapSize; x++)
        {
            for (int z = 0; z < mapSize; z++)
            {
                if (Random.Range(0f, 1f) < spawnChance)
                {
                    float height = Mathf.PerlinNoise(x * noiseScale, z * noiseScale) * maxHeight;
                    Vector3 pos = new Vector3(x, height, z);
                    
                    GameObject obj = CreateObject(pos);
                    if (obj != null)
                    {
                        generated.Add(obj);
                    }
                }
            }
        }
        
        Debug.Log($"Generated {generated.Count} objects");
    }
    
    GameObject CreateObject(Vector3 position)
    {
        if (cubePrefab != null)
        {
            GameObject obj = Instantiate(cubePrefab, position, Quaternion.identity);
            obj.transform.parent = transform;
            return obj;
        }
        else
        {
            GameObject obj = GameObject.CreatePrimitive(PrimitiveType.Cube);
            obj.transform.position = position;
            obj.transform.parent = transform;
            return obj;
        }
    }
    
    [ContextMenu("Regenerate")]
    public void Regenerate()
    {
        Clear();
        Generate();
    }
    
    [ContextMenu("Clear")]
    public void Clear()
    {
        foreach (GameObject obj in generated)
        {
            if (obj != null)
            {
                DestroyImmediate(obj);
            }
        }
        
        generated.Clear();
        Debug.Log("Cleared generated objects");
    }
    
    public void SetSeed(int newSeed)
    {
        seed = newSeed;
        Random.InitState(seed);
        Debug.Log($"Seed: {seed}");
    }
    
    public void SetMapSize(int size)
    {
        mapSize = size;
        Debug.Log($"Map size: {size}");
    }
    
    public void SetSpawnChance(float chance)
    {
        spawnChance = chance;
        Debug.Log($"Spawn chance: {chance}");
    }
    
    public int GetObjectCount()
    {
        return generated.Count;
    }
}