using UnityEngine;
using UnityEngine.VFX;
using System.Collections.Generic;

public class VfxParticleController : MonoBehaviour
{
    [Header("VFX Settings")]
    public VisualEffect visualEffect;
    public new ParticleSystem particleSystem;
    
    [Header("Control Parameters")]
    public float emissionRate = 100f;
    public Vector3 velocity = Vector3.up;
    public Color startColor = Color.white;
    public float lifetime = 5f;
    
    private Dictionary<string, object> vfxProperties = new Dictionary<string, object>();
    
    void Start()
    {
        InitializeVFX();
    }
    
    void InitializeVFX()
    {
        if (visualEffect == null)
            visualEffect = GetComponent<VisualEffect>();
            
        if (particleSystem == null)
            particleSystem = GetComponent<ParticleSystem>();
            
        SetupDefaultProperties();
        Debug.Log("VFX Particle Controller initialized!");
    }
    
    void SetupDefaultProperties()
    {
        if (visualEffect != null)
        {
            visualEffect.SetFloat("EmissionRate", emissionRate);
            visualEffect.SetVector3("Velocity", velocity);
            visualEffect.SetVector4("Color", startColor);
            visualEffect.SetFloat("Lifetime", lifetime);
        }
        
        if (particleSystem != null)
        {
            var main = particleSystem.main;
            main.startLifetime = lifetime;
            main.startColor = startColor;
            main.startSpeed = velocity.magnitude;
            
            var emission = particleSystem.emission;
            emission.rateOverTime = emissionRate;
        }
    }
    
    public void PlayVFX()
    {
        if (visualEffect != null)
            visualEffect.Play();
            
        if (particleSystem != null)
            particleSystem.Play();
    }
    
    public void StopVFX()
    {
        if (visualEffect != null)
            visualEffect.Stop();
            
        if (particleSystem != null)
            particleSystem.Stop();
    }
    
    public void SetEmissionRate(float rate)
    {
        emissionRate = rate;
        
        if (visualEffect != null)
            visualEffect.SetFloat("EmissionRate", rate);
            
        if (particleSystem != null)
        {
            var emission = particleSystem.emission;
            emission.rateOverTime = rate;
        }
    }
    
    public void SetColor(Color color)
    {
        startColor = color;
        
        if (visualEffect != null)
            visualEffect.SetVector4("Color", color);
            
        if (particleSystem != null)
        {
            var main = particleSystem.main;
            main.startColor = color;
        }
    }
    
    public void SetVelocity(Vector3 vel)
    {
        velocity = vel;
        
        if (visualEffect != null)
            visualEffect.SetVector3("Velocity", vel);
            
        if (particleSystem != null)
        {
            var main = particleSystem.main;
            main.startSpeed = vel.magnitude;
        }
    }
}