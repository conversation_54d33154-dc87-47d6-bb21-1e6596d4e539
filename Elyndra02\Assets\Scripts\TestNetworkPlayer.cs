using UnityEngine;
using Unity.Netcode;

public class TestNetworkPlayer : NetworkBehaviour
{
    [Head<PERSON>("Player Settings")]
    public float moveSpeed = 5f;
    public float rotationSpeed = 100f;
    
    private NetworkVariable<Vector3> networkPosition = new NetworkVariable<Vector3>();
    private NetworkVariable<Quaternion> networkRotation = new NetworkVariable<Quaternion>();
    
    public override void OnNetworkSpawn()
    {
        if (IsOwner)
        {
            Debug.Log("Local player spawned");
        }
        else
        {
            Debug.Log("Remote player spawned");
        }
    }
    
    private void Update()
    {
        if (IsOwner)
        {
            HandleInput();
        }
        else
        {
            // Interpolate position for remote players
            transform.position = Vector3.Lerp(transform.position, networkPosition.Value, Time.deltaTime * 10f);
            transform.rotation = Quaternion.Lerp(transform.rotation, networkRotation.Value, Time.deltaTime * 10f);
        }
    }
    
    private void HandleInput()
    {
        float horizontal = Input.GetAxis("Horizontal");
        float vertical = Input.GetAxis("Vertical");
        
        Vector3 movement = new Vector3(horizontal, 0, vertical) * moveSpeed * Time.deltaTime;
        transform.Translate(movement);
        
        if (horizontal != 0)
        {
            transform.Rotate(0, horizontal * rotationSpeed * Time.deltaTime, 0);
        }
        
        // Update network variables
        UpdatePositionServerRpc(transform.position, transform.rotation);
    }
    
    [ServerRpc]
    private void UpdatePositionServerRpc(Vector3 position, Quaternion rotation)
    {
        networkPosition.Value = position;
        networkRotation.Value = rotation;
    }
    
    [ClientRpc]
    public void SendMessageClientRpc(string message)
    {
        Debug.Log($"Received message: {message}");
    }
}