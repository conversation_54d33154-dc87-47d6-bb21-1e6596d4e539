using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

public class SimpleUIManager : MonoBehaviour
{
    [Head<PERSON>("UI Settings")]
    public Canvas mainCanvas;
    public float animationDuration = 0.3f;
    
    private Dictionary<string, GameObject> uiPanels;
    
    void Start()
    {
        InitializeUI();
    }
    
    void InitializeUI()
    {
        uiPanels = new Dictionary<string, GameObject>();
        
        if (mainCanvas == null)
        {
            mainCanvas = FindFirstObjectByType<Canvas>();
        }
        
        Debug.Log("Simple UI Manager initialized");
    }
    
    public void RegisterPanel(string name, GameObject panel)
    {
        uiPanels[name] = panel;
        panel.SetActive(false);
    }
    
    public void ShowPanel(string name)
    {
        if (uiPanels.ContainsKey(name))
        {
            uiPanels[name].SetActive(true);
            Debug.Log($"Panel {name} shown");
        }
    }
    
    public void HidePanel(string name)
    {
        if (uiPanels.ContainsKey(name))
        {
            uiPanels[name].SetActive(false);
            Debug.Log($"Panel {name} hidden");
        }
    }
    
    public void CreateNotification(string message)
    {
        Debug.Log($"Notification: {message}");
    }
}