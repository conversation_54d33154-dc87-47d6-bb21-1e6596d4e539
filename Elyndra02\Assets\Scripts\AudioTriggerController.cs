using System.Collections;
using System.Collections.Generic;
using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif

public class AudioTriggerController : MonoBehaviour
{
    [Header("Trigger Settings")]
    public string triggerType = "OnTriggerEnter";
    public bool randomizeClips = false;
    public bool playOnce = false;
    public float cooldownTime = 0f;
    
    [Header("Conditions")]
    public string requiredTag = "";
    public string requiredLayer = "";
    public bool playerOnly = false;
    
    [Header("Runtime Info")]
    public bool hasTriggered = false;
    public bool isOnCooldown = false;
    public float lastTriggerTime = 0f;
    
    private AudioSource audioSource;
    private List<AudioClip> audioClips = new List<AudioClip>();
    private int currentClipIndex = 0;
    private string triggerName;
    
    void Start()
    {
        triggerName = gameObject.name;
        audioSource = GetComponent<AudioSource>();
        
        LoadConfiguration();
        LoadAudioClips();
    }
    
    void LoadConfiguration()
    {
#if UNITY_EDITOR
        triggerType = EditorPrefs.GetString($"AudioTrigger.{triggerName}.TriggerType", "OnTriggerEnter");
        randomizeClips = EditorPrefs.GetBool($"AudioTrigger.{triggerName}.RandomizeClips", false);
        playOnce = EditorPrefs.GetBool($"AudioTrigger.{triggerName}.PlayOnce", false);
        cooldownTime = EditorPrefs.GetFloat($"AudioTrigger.{triggerName}.CooldownTime", 0f);
        
        requiredTag = EditorPrefs.GetString($"AudioTrigger.{triggerName}.RequiredTag", "");
        requiredLayer = EditorPrefs.GetString($"AudioTrigger.{triggerName}.RequiredLayer", "");
        playerOnly = EditorPrefs.GetBool($"AudioTrigger.{triggerName}.PlayerOnly", false);
#endif
    }
    
    void LoadAudioClips()
    {
#if UNITY_EDITOR
        int clipCount = EditorPrefs.GetInt($"AudioTrigger.{triggerName}.ClipCount", 0);
        
        for (int i = 0; i < clipCount; i++)
        {
            string clipName = EditorPrefs.GetString($"AudioTrigger.{triggerName}.Clip{i}", "");
            if (!string.IsNullOrEmpty(clipName))
            {
                // Find AudioClip by name
                AudioClip[] allClips = Resources.FindObjectsOfTypeAll<AudioClip>();
                AudioClip foundClip = System.Array.Find(allClips, clip => clip.name == clipName);
                
                if (foundClip != null)
                {
                    audioClips.Add(foundClip);
                }
            }
        }
#endif
    }
    
    void OnTriggerEnter(Collider other)
    {
        if (triggerType.ToLower().Contains("enter"))
        {
            HandleTrigger(other.gameObject);
        }
    }
    
    void OnTriggerExit(Collider other)
    {
        if (triggerType.ToLower().Contains("exit"))
        {
            HandleTrigger(other.gameObject);
        }
    }
    
    void OnTriggerStay(Collider other)
    {
        if (triggerType.ToLower().Contains("stay"))
        {
            HandleTrigger(other.gameObject);
        }
    }
    
    void OnCollisionEnter(Collision collision)
    {
        if (triggerType.ToLower().Contains("collision") && triggerType.ToLower().Contains("enter"))
        {
            HandleTrigger(collision.gameObject);
        }
    }
    
    void HandleTrigger(GameObject triggeringObject)
    {
        // Check if already triggered and playOnce is enabled
        if (playOnce && hasTriggered)
            return;
            
        // Check cooldown
        if (isOnCooldown)
            return;
            
        // Check conditions
        if (!CheckTriggerConditions(triggeringObject))
            return;
            
        // Play audio
        PlayAudio();
        
        // Set triggered state
        hasTriggered = true;
        lastTriggerTime = Time.time;
        
        // Start cooldown if specified
        if (cooldownTime > 0)
        {
            StartCoroutine(CooldownCoroutine());
        }
    }
    
    bool CheckTriggerConditions(GameObject triggeringObject)
    {
        // Check player only condition
        if (playerOnly)
        {
            if (!triggeringObject.CompareTag("Player") && 
                triggeringObject.name.ToLower() != "player")
            {
                return false;
            }
        }
        
        // Check required tag
        if (!string.IsNullOrEmpty(requiredTag))
        {
            if (!triggeringObject.CompareTag(requiredTag))
            {
                return false;
            }
        }
        
        // Check required layer
        if (!string.IsNullOrEmpty(requiredLayer))
        {
            int layerIndex = LayerMask.NameToLayer(requiredLayer);
            if (layerIndex != -1 && triggeringObject.layer != layerIndex)
            {
                return false;
            }
        }
        
        return true;
    }
    
    void PlayAudio()
    {
        if (audioClips.Count == 0 || audioSource == null)
            return;
            
        AudioClip clipToPlay = null;
        
        if (randomizeClips)
        {
            int randomIndex = Random.Range(0, audioClips.Count);
            clipToPlay = audioClips[randomIndex];
        }
        else
        {
            clipToPlay = audioClips[currentClipIndex];
            currentClipIndex = (currentClipIndex + 1) % audioClips.Count;
        }
        
        audioSource.clip = clipToPlay;
        audioSource.Play();
        
        Debug.Log($"Audio trigger '{triggerName}' played clip: {clipToPlay.name}");
    }
    
    IEnumerator CooldownCoroutine()
    {
        isOnCooldown = true;
        yield return new WaitForSeconds(cooldownTime);
        isOnCooldown = false;
    }
    
    public void ForcePlay()
    {
        PlayAudio();
    }
    
    public void ResetTrigger()
    {
        hasTriggered = false;
        isOnCooldown = false;
    }
    
    public void AddAudioClip(AudioClip clip)
    {
        if (clip != null && !audioClips.Contains(clip))
        {
            audioClips.Add(clip);
        }
    }
    
    public void RemoveAudioClip(AudioClip clip)
    {
        if (audioClips.Contains(clip))
        {
            audioClips.Remove(clip);
        }
    }
    
    public void SetVolume(float volume)
    {
        if (audioSource != null)
        {
            audioSource.volume = Mathf.Clamp01(volume);
        }
    }
    
    public void SetPitch(float pitch)
    {
        if (audioSource != null)
        {
            audioSource.pitch = pitch;
        }
    }
    
    // Public API methods
    public bool HasTriggered() => hasTriggered;
    public bool IsOnCooldown() => isOnCooldown;
    public int GetClipCount() => audioClips.Count;
    public float GetTimeSinceLastTrigger() => Time.time - lastTriggerTime;
}