using UnityEngine;
using UnityEngine.Audio;
using System.Collections;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Gerenciador de AudioMixer para runtime
/// Controla snapshots, volumes e efeitos durante o jogo
/// </summary>
public class AudioMixerManager : MonoBehaviour
{
    [System.Serializable]
    public class MixerGroup
    {
        public string groupName;
        public string volumeParameter;
        [Range(-80f, 20f)]
        public float defaultVolume = 0f;
        [Range(-80f, 20f)]
        public float currentVolume = 0f;
        public bool isMuted = false;
    }

    [System.Serializable]
    public class MixerSnapshot
    {
        public string snapshotName;
        public AudioMixerSnapshot snapshot;
        public float transitionTime = 1f;
        public bool isActive = false;
    }

    [System.Serializable]
    public class GameStateAudio
    {
        public string stateName;
        public string snapshotName;
        public float musicVolume = 0f;
        public float sfxVolume = 0f;
        public float voiceVolume = 0f;
        public float ambientVolume = 0f;
        public bool enableLowpass = false;
        public float lowpassCutoff = 5000f;
    }

    [Header("AudioMixer Configuration")]
    public AudioMixer mainMixer;
    
    [Header("Mixer Groups")]
    public List<MixerGroup> mixerGroups = new List<MixerGroup>();
    
    [Header("Snapshots")]
    public List<MixerSnapshot> snapshots = new List<MixerSnapshot>();
    
    [Header("Game States")]
    public List<GameStateAudio> gameStates = new List<GameStateAudio>();
    
    [Header("Runtime Settings")]
    [Range(0f, 1f)]
    public float masterVolume = 1f;
    [Range(0f, 1f)]
    public float musicVolume = 1f;
    [Range(0f, 1f)]
    public float sfxVolume = 1f;
    [Range(0f, 1f)]
    public float voiceVolume = 1f;
    [Range(0f, 1f)]
    public float ambientVolume = 1f;
    
    [Header("Fade Settings")]
    public float defaultFadeTime = 1f;
    public AnimationCurve fadeCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
    
    [Header("Dynamic Effects")]
    public bool enableDynamicLowpass = false;
    public float lowpassCutoffFrequency = 5000f;
    public bool enableDynamicReverb = false;
    public float reverbLevel = 0f;
    
    [Header("Debug")]
    public bool debugMode = false;
    public bool showVolumeMeters = false;
    
    // Runtime variables
    private Dictionary<string, AudioMixerSnapshot> snapshotDict;
    private Dictionary<string, string> parameterDict;
    private Coroutine currentFadeCoroutine;
    private string currentGameState = "Normal";
    private bool isInitialized = false;
    
    // Events
    public System.Action<string> OnSnapshotChanged;
    public System.Action<string> OnGameStateChanged;
    public System.Action<string, float> OnVolumeChanged;
    
    // Singleton pattern
    public static AudioMixerManager Instance { get; private set; }
    
    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeAudioMixer();
        }
        else
        {
            Destroy(gameObject);
        }
    }
    
    private void Start()
    {
        if (isInitialized)
        {
            LoadAudioSettings();
            SetGameState("Normal");
        }
    }
    
    private void Update()
    {
        if (debugMode && showVolumeMeters)
        {
            UpdateVolumeMeters();
        }
        
        UpdateDynamicEffects();
    }
    
    private void InitializeAudioMixer()
    {
        if (mainMixer == null)
        {
            Debug.LogError("AudioMixerManager: No AudioMixer assigned!");
            return;
        }
        
        // Initialize dictionaries
        snapshotDict = new Dictionary<string, AudioMixerSnapshot>();
        parameterDict = new Dictionary<string, string>();
        
        // Setup snapshots
        foreach (var snapshot in snapshots)
        {
            if (snapshot.snapshot != null)
            {
                snapshotDict[snapshot.snapshotName] = snapshot.snapshot;
            }
        }
        
        // Setup parameter mappings
        foreach (var group in mixerGroups)
        {
            if (!string.IsNullOrEmpty(group.volumeParameter))
            {
                parameterDict[group.groupName] = group.volumeParameter;
            }
        }
        
        // Initialize default game states if empty
        if (gameStates.Count == 0)
        {
            CreateDefaultGameStates();
        }
        
        isInitialized = true;
        
        if (debugMode)
        {
            Debug.Log($"AudioMixerManager initialized with {snapshots.Count} snapshots and {mixerGroups.Count} groups");
        }
    }
    
    private void CreateDefaultGameStates()
    {
        gameStates.Add(new GameStateAudio
        {
            stateName = "Normal",
            snapshotName = "Normal",
            musicVolume = 0f,
            sfxVolume = 0f,
            voiceVolume = 0f,
            ambientVolume = -10f
        });
        
        gameStates.Add(new GameStateAudio
        {
            stateName = "Paused",
            snapshotName = "Paused",
            musicVolume = -20f,
            sfxVolume = -40f,
            voiceVolume = 0f,
            ambientVolume = -30f,
            enableLowpass = true,
            lowpassCutoff = 1000f
        });
        
        gameStates.Add(new GameStateAudio
        {
            stateName = "Combat",
            snapshotName = "Combat",
            musicVolume = -5f,
            sfxVolume = 5f,
            voiceVolume = 0f,
            ambientVolume = -15f
        });
        
        gameStates.Add(new GameStateAudio
        {
            stateName = "Menu",
            snapshotName = "Menu",
            musicVolume = -10f,
            sfxVolume = -20f,
            voiceVolume = 0f,
            ambientVolume = -40f
        });
    }
    
    #region Public API
    
    /// <summary>
    /// Define o volume master (0-1)
    /// </summary>
    public void SetMasterVolume(float volume)
    {
        masterVolume = Mathf.Clamp01(volume);
        float dbValue = VolumeToDecibel(masterVolume);
        
        if (mainMixer.SetFloat("MasterVolume", dbValue))
        {
            OnVolumeChanged?.Invoke("Master", masterVolume);
            SaveAudioSettings();
        }
    }
    
    /// <summary>
    /// Define o volume de música (0-1)
    /// </summary>
    public void SetMusicVolume(float volume)
    {
        musicVolume = Mathf.Clamp01(volume);
        float dbValue = VolumeToDecibel(musicVolume);
        
        if (mainMixer.SetFloat("MusicVolume", dbValue))
        {
            OnVolumeChanged?.Invoke("Music", musicVolume);
            SaveAudioSettings();
        }
    }
    
    /// <summary>
    /// Define o volume de efeitos sonoros (0-1)
    /// </summary>
    public void SetSFXVolume(float volume)
    {
        sfxVolume = Mathf.Clamp01(volume);
        float dbValue = VolumeToDecibel(sfxVolume);
        
        if (mainMixer.SetFloat("SFXVolume", dbValue))
        {
            OnVolumeChanged?.Invoke("SFX", sfxVolume);
            SaveAudioSettings();
        }
    }
    
    /// <summary>
    /// Define o volume de voz (0-1)
    /// </summary>
    public void SetVoiceVolume(float volume)
    {
        voiceVolume = Mathf.Clamp01(volume);
        float dbValue = VolumeToDecibel(voiceVolume);
        
        if (mainMixer.SetFloat("VoiceVolume", dbValue))
        {
            OnVolumeChanged?.Invoke("Voice", voiceVolume);
            SaveAudioSettings();
        }
    }
    
    /// <summary>
    /// Define o volume ambiente (0-1)
    /// </summary>
    public void SetAmbientVolume(float volume)
    {
        ambientVolume = Mathf.Clamp01(volume);
        float dbValue = VolumeToDecibel(ambientVolume);
        
        if (mainMixer.SetFloat("AmbientVolume", dbValue))
        {
            OnVolumeChanged?.Invoke("Ambient", ambientVolume);
            SaveAudioSettings();
        }
    }
    
    /// <summary>
    /// Muda para um snapshot específico
    /// </summary>
    public void TransitionToSnapshot(string snapshotName, float transitionTime = -1f)
    {
        if (!isInitialized)
        {
            Debug.LogWarning("AudioMixerManager not initialized!");
            return;
        }
        
        if (snapshotDict.ContainsKey(snapshotName))
        {
            float fadeTime = transitionTime >= 0 ? transitionTime : defaultFadeTime;
            snapshotDict[snapshotName].TransitionTo(fadeTime);
            
            // Update snapshot states
            foreach (var snapshot in snapshots)
            {
                snapshot.isActive = snapshot.snapshotName == snapshotName;
            }
            
            OnSnapshotChanged?.Invoke(snapshotName);
            
            if (debugMode)
            {
                Debug.Log($"Transitioned to snapshot: {snapshotName} (fade: {fadeTime}s)");
            }
        }
        else
        {
            Debug.LogWarning($"Snapshot '{snapshotName}' not found!");
        }
    }
    
    /// <summary>
    /// Define o estado do jogo e aplica configurações de áudio
    /// </summary>
    public void SetGameState(string stateName)
    {
        var gameState = gameStates.FirstOrDefault(gs => gs.stateName == stateName);
        if (gameState != null)
        {
            currentGameState = stateName;
            
            // Apply snapshot
            if (!string.IsNullOrEmpty(gameState.snapshotName))
            {
                TransitionToSnapshot(gameState.snapshotName);
            }
            
            // Apply volume settings
            StartCoroutine(FadeToGameState(gameState));
            
            OnGameStateChanged?.Invoke(stateName);
            
            if (debugMode)
            {
                Debug.Log($"Game state changed to: {stateName}");
            }
        }
        else
        {
            Debug.LogWarning($"Game state '{stateName}' not found!");
        }
    }
    
    /// <summary>
    /// Aplica fade suave para um grupo específico
    /// </summary>
    public void FadeGroup(string groupName, float targetVolume, float fadeTime)
    {
        var group = mixerGroups.FirstOrDefault(g => g.groupName == groupName);
        if (group != null && parameterDict.ContainsKey(groupName))
        {
            StartCoroutine(FadeGroupCoroutine(parameterDict[groupName], targetVolume, fadeTime));
        }
    }
    
    /// <summary>
    /// Silencia/desilencia um grupo
    /// </summary>
    public void MuteGroup(string groupName, bool mute)
    {
        var group = mixerGroups.FirstOrDefault(g => g.groupName == groupName);
        if (group != null)
        {
            group.isMuted = mute;
            float targetVolume = mute ? -80f : group.defaultVolume;
            
            if (parameterDict.ContainsKey(groupName))
            {
                mainMixer.SetFloat(parameterDict[groupName], targetVolume);
            }
        }
    }
    
    /// <summary>
    /// Define filtro lowpass dinâmico
    /// </summary>
    public void SetLowpassFilter(float cutoffFrequency, float transitionTime = 1f)
    {
        lowpassCutoffFrequency = Mathf.Clamp(cutoffFrequency, 10f, 22000f);
        
        if (currentFadeCoroutine != null)
        {
            StopCoroutine(currentFadeCoroutine);
        }
        
        currentFadeCoroutine = StartCoroutine(FadeLowpassCoroutine(cutoffFrequency, transitionTime));
    }
    
    /// <summary>
    /// Define nível de reverb dinâmico
    /// </summary>
    public void SetReverbLevel(float level, float transitionTime = 1f)
    {
        reverbLevel = Mathf.Clamp01(level);
        float dbValue = VolumeToDecibel(reverbLevel);
        
        StartCoroutine(FadeParameterCoroutine("ReverbLevel", dbValue, transitionTime));
    }
    
    /// <summary>
    /// Pausa/despausa todo o áudio
    /// </summary>
    public void PauseAudio(bool pause)
    {
        if (pause)
        {
            SetGameState("Paused");
        }
        else
        {
            SetGameState("Normal");
        }
    }
    
    #endregion
    
    #region Private Methods
    
    private IEnumerator FadeToGameState(GameStateAudio gameState)
    {
        float elapsed = 0f;
        
        // Get current values
        mainMixer.GetFloat("MusicVolume", out float currentMusic);
        mainMixer.GetFloat("SFXVolume", out float currentSFX);
        mainMixer.GetFloat("VoiceVolume", out float currentVoice);
        mainMixer.GetFloat("AmbientVolume", out float currentAmbient);
        
        while (elapsed < defaultFadeTime)
        {
            elapsed += Time.unscaledDeltaTime;
            float t = fadeCurve.Evaluate(elapsed / defaultFadeTime);
            
            // Interpolate volumes
            float musicVol = Mathf.Lerp(currentMusic, gameState.musicVolume, t);
            float sfxVol = Mathf.Lerp(currentSFX, gameState.sfxVolume, t);
            float voiceVol = Mathf.Lerp(currentVoice, gameState.voiceVolume, t);
            float ambientVol = Mathf.Lerp(currentAmbient, gameState.ambientVolume, t);
            
            // Apply volumes
            mainMixer.SetFloat("MusicVolume", musicVol);
            mainMixer.SetFloat("SFXVolume", sfxVol);
            mainMixer.SetFloat("VoiceVolume", voiceVol);
            mainMixer.SetFloat("AmbientVolume", ambientVol);
            
            // Apply lowpass if enabled
            if (gameState.enableLowpass)
            {
                float cutoff = Mathf.Lerp(22000f, gameState.lowpassCutoff, t);
                mainMixer.SetFloat("LowpassCutoff", cutoff);
            }
            
            yield return null;
        }
        
        // Ensure final values
        mainMixer.SetFloat("MusicVolume", gameState.musicVolume);
        mainMixer.SetFloat("SFXVolume", gameState.sfxVolume);
        mainMixer.SetFloat("VoiceVolume", gameState.voiceVolume);
        mainMixer.SetFloat("AmbientVolume", gameState.ambientVolume);
        
        if (gameState.enableLowpass)
        {
            mainMixer.SetFloat("LowpassCutoff", gameState.lowpassCutoff);
        }
    }
    
    private IEnumerator FadeGroupCoroutine(string parameter, float targetVolume, float fadeTime)
    {
        mainMixer.GetFloat(parameter, out float currentVolume);
        float elapsed = 0f;
        
        while (elapsed < fadeTime)
        {
            elapsed += Time.unscaledDeltaTime;
            float t = fadeCurve.Evaluate(elapsed / fadeTime);
            float volume = Mathf.Lerp(currentVolume, targetVolume, t);
            
            mainMixer.SetFloat(parameter, volume);
            yield return null;
        }
        
        mainMixer.SetFloat(parameter, targetVolume);
    }
    
    private IEnumerator FadeLowpassCoroutine(float targetCutoff, float fadeTime)
    {
        mainMixer.GetFloat("LowpassCutoff", out float currentCutoff);
        float elapsed = 0f;
        
        while (elapsed < fadeTime)
        {
            elapsed += Time.unscaledDeltaTime;
            float t = elapsed / fadeTime;
            float cutoff = Mathf.Lerp(currentCutoff, targetCutoff, t);
            
            mainMixer.SetFloat("LowpassCutoff", cutoff);
            yield return null;
        }
        
        mainMixer.SetFloat("LowpassCutoff", targetCutoff);
    }
    
    private IEnumerator FadeParameterCoroutine(string parameter, float targetValue, float fadeTime)
    {
        mainMixer.GetFloat(parameter, out float currentValue);
        float elapsed = 0f;
        
        while (elapsed < fadeTime)
        {
            elapsed += Time.unscaledDeltaTime;
            float t = elapsed / fadeTime;
            float value = Mathf.Lerp(currentValue, targetValue, t);
            
            mainMixer.SetFloat(parameter, value);
            yield return null;
        }
        
        mainMixer.SetFloat(parameter, targetValue);
    }
    
    private void UpdateDynamicEffects()
    {
        if (enableDynamicLowpass)
        {
            // Exemplo: aplicar lowpass baseado na velocidade do player
            // float playerSpeed = GetPlayerSpeed();
            // float cutoff = Mathf.Lerp(22000f, 1000f, playerSpeed / maxSpeed);
            // mainMixer.SetFloat("LowpassCutoff", cutoff);
        }
        
        if (enableDynamicReverb)
        {
            // Exemplo: aplicar reverb baseado no ambiente
            // float environmentReverb = GetEnvironmentReverb();
            // mainMixer.SetFloat("ReverbLevel", environmentReverb);
        }
    }
    
    private void UpdateVolumeMeters()
    {
        // Implementar medidores de volume para debug
        if (mainMixer.GetFloat("MusicVolume", out float musicVol))
        {
            // Debug.Log($"Music Volume: {musicVol} dB");
        }
    }
    
    private float VolumeToDecibel(float volume)
    {
        if (volume <= 0f)
            return -80f;
        
        return Mathf.Log10(volume) * 20f;
    }
    
    private float DecibelToVolume(float decibel)
    {
        if (decibel <= -80f)
            return 0f;
        
        return Mathf.Pow(10f, decibel / 20f);
    }
    
    private void SaveAudioSettings()
    {
        PlayerPrefs.SetFloat("AudioMixer_MasterVolume", masterVolume);
        PlayerPrefs.SetFloat("AudioMixer_MusicVolume", musicVolume);
        PlayerPrefs.SetFloat("AudioMixer_SFXVolume", sfxVolume);
        PlayerPrefs.SetFloat("AudioMixer_VoiceVolume", voiceVolume);
        PlayerPrefs.SetFloat("AudioMixer_AmbientVolume", ambientVolume);
        PlayerPrefs.Save();
    }
    
    private void LoadAudioSettings()
    {
        masterVolume = PlayerPrefs.GetFloat("AudioMixer_MasterVolume", 1f);
        musicVolume = PlayerPrefs.GetFloat("AudioMixer_MusicVolume", 1f);
        sfxVolume = PlayerPrefs.GetFloat("AudioMixer_SFXVolume", 1f);
        voiceVolume = PlayerPrefs.GetFloat("AudioMixer_VoiceVolume", 1f);
        ambientVolume = PlayerPrefs.GetFloat("AudioMixer_AmbientVolume", 1f);
        
        // Apply loaded settings
        SetMasterVolume(masterVolume);
        SetMusicVolume(musicVolume);
        SetSFXVolume(sfxVolume);
        SetVoiceVolume(voiceVolume);
        SetAmbientVolume(ambientVolume);
    }
    
    #endregion
    
    #region Debug Methods
    
    [System.Diagnostics.Conditional("UNITY_EDITOR")]
    private void OnDrawGizmos()
    {
        if (!debugMode || !Application.isPlaying) return;
        
        // Visualizar informações de áudio no Scene view
        UnityEditor.Handles.Label(transform.position + Vector3.up * 2f, 
            $"Audio State: {currentGameState}\nMaster: {masterVolume:F2}\nMusic: {musicVolume:F2}");
    }
    
    #endregion
}