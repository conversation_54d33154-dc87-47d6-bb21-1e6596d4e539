using UnityEngine;
using UnityEditor;
using UnityEngine.Audio;
using System.IO;

public class AudioMixerCreator : EditorWindow
{
    private string mixerName = "GameAudioMixer";
    private string savePath = "Assets/Audio/Mixers/";
    
    [MenuItem("Tools/Audio/Create Audio Mixer")]
    public static void ShowWindow()
    {
        GetWindow<AudioMixerCreator>("Audio Mixer Creator");
    }
    
    private void OnGUI()
    {
        GUILayout.Label("Audio Mixer Creator", EditorStyles.boldLabel);
        
        EditorGUILayout.Space();
        
        mixerName = EditorGUILayout.TextField("Mixer Name:", mixerName);
        savePath = EditorGUILayout.TextField("Save Path:", savePath);
        
        EditorGUILayout.Space();
        
        if (GUILayout.Button("Create Audio Mixer"))
        {
            CreateAudioMixer();
        }
        
        EditorGUILayout.Space();
        
        EditorGUILayout.HelpBox(
            "Este script cria um AudioMixer com grupos padrão:\n" +
            "• Master (grupo principal)\n" +
            "• Music (música de fundo)\n" +
            "• SFX (efeitos sonoros)\n" +
            "• Voice (diálogos)\n\n" +
            "Após criar, você pode configurar os efeitos manualmente no AudioMixer.",
            MessageType.Info
        );
    }
    
    private void CreateAudioMixer()
    {
        // Criar diretório se não existir
        if (!Directory.Exists(savePath))
        {
            Directory.CreateDirectory(savePath);
        }
        
        // Caminho completo do arquivo
        string fullPath = Path.Combine(savePath, mixerName + ".mixer");
        
        // Verificar se já existe
        if (File.Exists(fullPath))
        {
            if (!EditorUtility.DisplayDialog("Arquivo Existe", 
                $"O AudioMixer '{mixerName}' já existe. Deseja sobrescrever?", 
                "Sim", "Não"))
            {
                return;
            }
        }
        
        // Criar o AudioMixer usando a abordagem correta do Unity
        // Como AudioMixer não pode ser criado via script, vamos usar o menu do Unity
        string guid = AssetDatabase.CreateFolder(Path.GetDirectoryName(fullPath), "temp_mixer_folder");
        string tempFolderPath = AssetDatabase.GUIDToAssetPath(guid);
        
        // Usar reflexão para acessar o método interno do Unity
        var audioMixerType = typeof(AudioMixer);
        var createMethod = audioMixerType.GetMethod("CreateMixerAsset", 
            System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.NonPublic);
        
        if (createMethod != null)
        {
            try
            {
                createMethod.Invoke(null, new object[] { fullPath });
                
                // Carregar o mixer criado
                AssetDatabase.Refresh();
                AudioMixer mixer = AssetDatabase.LoadAssetAtPath<AudioMixer>(fullPath);
                
                if (mixer != null)
                {
                    mixer.name = mixerName;
                    EditorUtility.SetDirty(mixer);
                    AssetDatabase.SaveAssets();
                    
                    // Selecionar o mixer criado
                    Selection.activeObject = mixer;
                    EditorGUIUtility.PingObject(mixer);
                    
                    Debug.Log($"AudioMixer '{mixerName}' criado com sucesso em: {fullPath}");
                    
                    // Mostrar instruções
                    EditorUtility.DisplayDialog("AudioMixer Criado", 
                        $"AudioMixer '{mixerName}' foi criado com sucesso!\n\n" +
                        "Próximos passos:\n" +
                        "1. Abra o AudioMixer Window (Window > Audio > Audio Mixer)\n" +
                        "2. Adicione grupos (Master, Music, SFX, Voice)\n" +
                        "3. Configure os efeitos desejados\n" +
                        "4. Exponha parâmetros para controle via script", 
                        "OK");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Erro ao criar AudioMixer: {e.Message}");
                ShowManualCreationDialog();
            }
        }
        else
        {
            ShowManualCreationDialog();
        }
        
        // Limpar pasta temporária
        if (Directory.Exists(tempFolderPath))
        {
            AssetDatabase.DeleteAsset(tempFolderPath);
        }
    }
    
    private void ShowManualCreationDialog()
    {
        EditorUtility.DisplayDialog("Criação Manual Necessária", 
            "Não foi possível criar o AudioMixer automaticamente.\n\n" +
            "Por favor, crie manualmente:\n" +
            "1. Clique com botão direito na pasta " + savePath + "\n" +
            "2. Selecione Create > Audio Mixer\n" +
            "3. Renomeie para '" + mixerName + "'\n\n" +
            "Alternativamente, use Window > Audio > Audio Mixer para criar.", 
            "OK");
    }
    
    [MenuItem("Tools/Audio/Setup Audio Folders")]
    public static void SetupAudioFolders()
    {
        string[] folders = {
            "Assets/Audio",
            "Assets/Audio/Music",
            "Assets/Audio/SFX",
            "Assets/Audio/Voice",
            "Assets/Audio/Mixers"
        };
        
        foreach (string folder in folders)
        {
            if (!Directory.Exists(folder))
            {
                Directory.CreateDirectory(folder);
                Debug.Log($"Pasta criada: {folder}");
            }
        }
        
        AssetDatabase.Refresh();
        Debug.Log("Estrutura de pastas de áudio configurada!");
    }
}