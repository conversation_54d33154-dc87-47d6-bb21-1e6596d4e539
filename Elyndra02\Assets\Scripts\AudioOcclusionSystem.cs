using UnityEngine;

public class AudioOcclusionSystem : MonoBehaviour
{
    [Header("Occlusion Settings")]
    public float raycastFrequency = 0.1f;
    public float occlusionIntensity = 0.5f;
    public float smoothTime = 0.1f;
    public float maxDistance = 100f;
    public LayerMask occlusionLayers = -1;
    public bool useReverbZones = true;
    
    private AudioListener audioListener;
    private AudioSource[] audioSources;
    private float[] originalCutoffs;
    private float[] targetCutoffs;
    private float[] currentCutoffs;
    private float[] cutoffVelocities;
    
    void Start()
    {
        // Load settings from PlayerPrefs
        raycastFrequency = PlayerPrefs.GetFloat("AudioOcclusion.RaycastFrequency", 0.1f);
        occlusionIntensity = PlayerPrefs.GetFloat("AudioOcclusion.OcclusionIntensity", 0.5f);
        smoothTime = PlayerPrefs.GetFloat("AudioOcclusion.SmoothTime", 0.1f);
        maxDistance = PlayerPrefs.GetFloat("AudioOcclusion.MaxDistance", 100f);
        occlusionLayers = PlayerPrefs.GetInt("AudioOcclusion.LayerMask", -1);
        useReverbZones = PlayerPrefs.GetInt("AudioOcclusion.UseReverbZones", 1) == 1;
        
        audioListener = UnityEngine.Object.FindFirstObjectByType<AudioListener>();
        if (audioListener == null)
        {
            Debug.LogError("AudioOcclusionSystem: No AudioListener found in scene!");
            enabled = false;
            return;
        }
        
        RefreshAudioSources();
        InvokeRepeating(nameof(UpdateOcclusion), 0f, raycastFrequency);
    }
    
    void RefreshAudioSources()
    {
        audioSources = FindObjectsByType<AudioSource>(FindObjectsSortMode.None);
        originalCutoffs = new float[audioSources.Length];
        targetCutoffs = new float[audioSources.Length];
        currentCutoffs = new float[audioSources.Length];
        cutoffVelocities = new float[audioSources.Length];
        
        for (int i = 0; i < audioSources.Length; i++)
        {
            AudioLowPassFilter filter = audioSources[i].GetComponent<AudioLowPassFilter>();
            if (filter != null)
            {
                originalCutoffs[i] = filter.cutoffFrequency;
                currentCutoffs[i] = filter.cutoffFrequency;
                targetCutoffs[i] = filter.cutoffFrequency;
            }
            else
            {
                originalCutoffs[i] = 22000f;
                currentCutoffs[i] = 22000f;
                targetCutoffs[i] = 22000f;
            }
        }
    }
    
    void UpdateOcclusion()
    {
        if (audioListener == null) return;
        
        Vector3 listenerPos = audioListener.transform.position;
        
        for (int i = 0; i < audioSources.Length; i++)
        {
            if (audioSources[i] == null || audioSources[i].gameObject == audioListener.gameObject) 
                continue;
                
            Vector3 sourcePos = audioSources[i].transform.position;
            float distance = Vector3.Distance(listenerPos, sourcePos);
            
            if (distance > maxDistance)
            {
                targetCutoffs[i] = originalCutoffs[i] * 0.1f; // Heavily filtered
                continue;
            }
            
            Vector3 direction = (sourcePos - listenerPos).normalized;
            RaycastHit hit;
            
            bool isOccluded = Physics.Raycast(listenerPos, direction, out hit, distance, occlusionLayers);
            
            if (isOccluded)
            {
                float occlusionAmount = 1f - (hit.distance / distance);
                float cutoffReduction = Mathf.Lerp(1f, 0.1f, occlusionAmount * occlusionIntensity);
                targetCutoffs[i] = originalCutoffs[i] * cutoffReduction;
                
                // Apply reverb for occluded sounds
                if (useReverbZones)
                {
                    AudioReverbFilter reverbFilter = audioSources[i].GetComponent<AudioReverbFilter>();
                    if (reverbFilter != null)
                    {
                        reverbFilter.room = Mathf.RoundToInt(-1000 + (900 * occlusionAmount));
                    }
                }
            }
            else
            {
                targetCutoffs[i] = originalCutoffs[i];
                
                if (useReverbZones)
                {
                    AudioReverbFilter reverbFilter = audioSources[i].GetComponent<AudioReverbFilter>();
                    if (reverbFilter != null)
                    {
                        reverbFilter.room = -1000; // No reverb
                    }
                }
            }
        }
    }
    
    void Update()
    {
        // Smooth the cutoff transitions
        for (int i = 0; i < audioSources.Length; i++)
        {
            if (audioSources[i] == null) continue;
            
            currentCutoffs[i] = Mathf.SmoothDamp(currentCutoffs[i], targetCutoffs[i], 
                ref cutoffVelocities[i], smoothTime);
            
            AudioLowPassFilter filter = audioSources[i].GetComponent<AudioLowPassFilter>();
            if (filter != null)
            {
                filter.cutoffFrequency = currentCutoffs[i];
            }
        }
    }
    
    void OnDrawGizmos()
    {
        if (audioListener == null) return;
        
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(audioListener.transform.position, maxDistance);
        
        if (audioSources != null)
        {
            Vector3 listenerPos = audioListener.transform.position;
            
            for (int i = 0; i < audioSources.Length; i++)
            {
                if (audioSources[i] == null || audioSources[i].gameObject == audioListener.gameObject) 
                    continue;
                    
                Vector3 sourcePos = audioSources[i].transform.position;
                float distance = Vector3.Distance(listenerPos, sourcePos);
                
                if (distance <= maxDistance)
                {
                    Vector3 direction = (sourcePos - listenerPos).normalized;
                    RaycastHit hit;
                    
                    bool isOccluded = Physics.Raycast(listenerPos, direction, out hit, distance, occlusionLayers);
                    
                    Gizmos.color = isOccluded ? Color.red : Color.green;
                    Gizmos.DrawLine(listenerPos, sourcePos);
                    
                    if (isOccluded)
                    {
                        Gizmos.color = Color.red;
                        Gizmos.DrawSphere(hit.point, 0.1f);
                    }
                }
            }
        }
    }
}